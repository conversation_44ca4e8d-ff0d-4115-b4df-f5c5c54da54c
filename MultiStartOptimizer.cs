using System;
using System.Collections.Generic;
using System.Linq;

namespace CalculateOffset
{
    /// <summary>
    /// 多起点优化器：使用不同的启发式策略作为起点，运行多个优化算法并选择最佳结果
    /// 这是改进方案3.md的核心实现，提供更好的解空间覆盖和鲁棒性
    /// </summary>
    public class MultiStartOptimizer
    {
        #region 私有字段

        private readonly int _timeBaseUs;
        private readonly int _maxOffsetUs;
        private readonly MultiStartParameters _parameters;
        private readonly Random _random;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="timeBaseUs">时基值(微秒)</param>
        /// <param name="maxOffsetUs">最大偏移量(微秒)</param>
        /// <param name="parameters">多起点优化参数</param>
        public MultiStartOptimizer(int timeBaseUs, int maxOffsetUs, 
            MultiStartParameters parameters = null)
        {
            _timeBaseUs = timeBaseUs;
            _maxOffsetUs = maxOffsetUs;
            _parameters = parameters ?? new MultiStartParameters();
            
            // 根据参数选择随机种子
            var randomSeed = _parameters.SAParameters?.RandomSeed ?? _parameters.GAParameters?.RandomSeed;
            _random = randomSeed.HasValue ? new Random(randomSeed.Value) : new Random();
            
            //Console.WriteLine($"[调试警告检查] MultiStartOptimizer构造 - 最大offset限制: {TaskUtility.ConvertToMilliseconds(maxOffsetUs):F3}ms");
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 执行多起点优化
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <returns>多起点优化结果</returns>
        public MultiStartResult Optimize(List<TaskItem> tasks)
        {
            var result = new MultiStartResult();
            var startTime = DateTime.Now;

            try
            {
                //Console.WriteLine("[多起点优化] 开始执行多起点优化...");
                
                // 验证输入
                if (tasks == null || tasks.Count == 0)
                {
                    return CreateErrorResult("任务列表为空或无效");
                }
                
                // 计算所有任务的最大MaxOffsetUs值，用于创建优化器
                int actualMaxOffsetUs = tasks.Any(t => t.MaxOffsetUs > 0) ? 
                    tasks.Where(t => t.MaxOffsetUs > 0).Max(t => t.MaxOffsetUs) : _maxOffsetUs;

                // 获取要使用的策略列表
                var strategies = GetStrategiesToUse();
                //Console.WriteLine($"[多起点优化] 将使用 {strategies.Count} 种策略: {string.Join(", ", strategies)}");

                // 存储所有策略的结果
                var strategyResults = new List<MultiStartStrategyResult>();

                // 为每种策略生成初始解并运行优化
                foreach (var strategy in strategies)
                {
                    //Console.WriteLine($"\n[多起点优化] 执行策略: {strategy}");
                    
                    var strategyResult = ExecuteStrategyOptimization(tasks, strategy, actualMaxOffsetUs);
                    strategyResults.Add(strategyResult);
                    
                    //Console.WriteLine($"[多起点优化] 策略 {strategy} 完成, 成本: {strategyResult.FinalCost:F6}");
                }

                // 选择最佳结果
                var bestResult = SelectBestResult(strategyResults, tasks, actualMaxOffsetUs);
                var bestStats = IntelligentSchedulingEngine.CalculateOffsetStatistics(bestResult.FinalSolution);
                //Console.WriteLine($"\n[多起点优化] 最佳策略: {bestResult.Strategy}, 最佳成本: {bestResult.FinalCost:F6}");
                //Console.WriteLine($"[多起点优化] 最佳策略统计: 均值={bestStats.Mean:F3}ms, 方差={bestStats.Variance:F3}ms², 标准差={bestStats.StdDev:F3}ms");

                // 应用最佳解到任务
                ApplyBestSolutionToTasks(tasks, bestResult);

                // 构建最终结果
                result.Success = true;
                result.BestStrategy = bestResult.Strategy;
                result.BestCost = bestResult.FinalCost;
                result.BestSolution = bestResult.FinalSolution;
                result.StrategyResults = strategyResults;
                result.OptimizationTime = DateTime.Now - startTime;
                result.TotalStrategiesUsed = strategies.Count;

                //Console.WriteLine($"[多起点优化] 优化完成，总耗时: {result.OptimizationTime.TotalMilliseconds:F0}ms");
                
                return result;
            }
            catch (Exception ex)
            {
                //Console.WriteLine($"[多起点优化] 发生错误: {ex.Message}");
                return CreateErrorResult($"多起点优化失败: {ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 获取要使用的策略列表
        /// </summary>
        /// <returns>策略列表</returns>
        private List<HeuristicSortStrategy> GetStrategiesToUse()
        {
            var strategies = new List<HeuristicSortStrategy>();

            // 第一阶段：基础双策略
            if (_parameters.UseBasicStrategies)
            {
                strategies.Add(HeuristicSortStrategy.NonPreemptiveFirst);
                strategies.Add(HeuristicSortStrategy.HighPriorityFirst);
            }

            // 第三阶段：扩展策略
            if (_parameters.UseExtendedStrategies)
            {
                strategies.Add(HeuristicSortStrategy.PeriodOptimized);
                strategies.Add(HeuristicSortStrategy.ExecutionTimeOptimized);
                strategies.Add(HeuristicSortStrategy.LoadBalanced);
            }

            // 第四阶段：高级策略
            if (_parameters.UseAdvancedStrategies)
            {
                strategies.Add(HeuristicSortStrategy.OffsetCalculatorBased);
            }

            // 确保至少有一个策略
            if (strategies.Count == 0)
            {
                strategies.Add(HeuristicSortStrategy.NonPreemptiveFirst);
            }

            return strategies;
        }

        /// <summary>
        /// 执行单个策略的优化
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="strategy">启发式策略</param>
        /// <param name="actualMaxOffsetUs">实际的最大offset限制（从任务中计算得出）</param>
        /// <returns>策略结果</returns>
        private MultiStartStrategyResult ExecuteStrategyOptimization(List<TaskItem> tasks, HeuristicSortStrategy strategy, int actualMaxOffsetUs)
        {
            var strategyResult = new MultiStartStrategyResult
            {
                Strategy = strategy,
                StartTime = DateTime.Now
            };

            try
            {
                // 生成该策略的初始解
                var initialSolution = TaskUtility.GenerateHeuristicSeed(tasks, _timeBaseUs, actualMaxOffsetUs, strategy);
                
                if (initialSolution == null || initialSolution.Count == 0)
                {
                    strategyResult.Success = false;
                    strategyResult.ErrorMessage = $"策略 {strategy} 生成初始解失败，当前offset限制({TaskUtility.ConvertToMilliseconds(actualMaxOffsetUs):F3}ms)可能过小，无法为所有任务找到合适的启发式解";
                    //Console.WriteLine($"[多起点] 跳过策略 {strategy}: {strategyResult.ErrorMessage}");
                    return strategyResult;
                }

                strategyResult.InitialSolution = new Dictionary<int, int>(initialSolution);
                
                // 计算初始成本
                var tempTasks = CreateTasksCopy(tasks);
                ApplySolutionToTasks(tempTasks, initialSolution);
                var analyzer = new SchedulabilityAnalyzer(_timeBaseUs);
                var initialAnalysis = analyzer.AnalyzeSchedulability(tempTasks);
                strategyResult.InitialCost = initialAnalysis.CostValue;

                // 选择要运行的优化器
                var optimizerResults = new List<OptimizerResult>();

                // 运行模拟退火
                if (_parameters.UseSimulatedAnnealing)
                {
                    var saResult = RunSimulatedAnnealing(tasks, initialSolution, actualMaxOffsetUs);
                    optimizerResults.Add(saResult);
                }

                // 运行遗传算法
                if (_parameters.UseGeneticAlgorithm)
                {
                    var gaResult = RunGeneticAlgorithm(tasks, initialSolution, actualMaxOffsetUs);
                    optimizerResults.Add(gaResult);
                }

                // 选择最佳优化器结果
                var bestOptimizerResult = optimizerResults.OrderBy(r => r.FinalCost).First();
                
                strategyResult.Success = true;
                strategyResult.FinalSolution = bestOptimizerResult.FinalSolution;
                strategyResult.FinalCost = bestOptimizerResult.FinalCost;
                strategyResult.BestOptimizer = bestOptimizerResult.OptimizerType;
                strategyResult.OptimizerResults = optimizerResults;
                strategyResult.ImprovementRatio = (strategyResult.InitialCost - strategyResult.FinalCost) / strategyResult.InitialCost;
            }
            catch (Exception ex)
            {
                strategyResult.Success = false;
                strategyResult.ErrorMessage = $"策略 {strategy} 执行失败: {ex.Message}";
            }
            finally
            {
                strategyResult.EndTime = DateTime.Now;
                strategyResult.ExecutionTime = strategyResult.EndTime - strategyResult.StartTime;
            }

            return strategyResult;
        }

        /// <summary>
        /// 运行模拟退火优化
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="initialSolution">初始解</param>
        /// <param name="actualMaxOffsetUs">实际的最大offset限制</param>
        /// <returns>优化器结果</returns>
        private OptimizerResult RunSimulatedAnnealing(List<TaskItem> tasks, Dictionary<int, int> initialSolution, int actualMaxOffsetUs)
        {
            var result = new OptimizerResult
            {
                OptimizerType = "SimulatedAnnealing",
                StartTime = DateTime.Now
            };

            try
            {
                var saOptimizer = new SimulatedAnnealingOptimizer(_timeBaseUs, actualMaxOffsetUs, _parameters.SAParameters);
                var tasksCopy = CreateTasksCopy(tasks);
                var saResult = saOptimizer.Optimize(tasksCopy, initialSolution);

                result.Success = saResult.Success;
                result.FinalCost = saResult.BestCost;
                result.FinalSolution = ExtractSolutionFromTasks(saResult.OptimizedTasks);
                result.ErrorMessage = saResult.ErrorMessage;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = $"模拟退火执行失败: {ex.Message}";
                result.FinalCost = double.MaxValue;
            }
            finally
            {
                result.EndTime = DateTime.Now;
                result.ExecutionTime = result.EndTime - result.StartTime;
            }

            return result;
        }

        /// <summary>
        /// 运行遗传算法优化
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="initialSolution">初始解</param>
        /// <param name="actualMaxOffsetUs">实际的最大offset限制</param>
        /// <returns>优化器结果</returns>
        private OptimizerResult RunGeneticAlgorithm(List<TaskItem> tasks, Dictionary<int, int> initialSolution, int actualMaxOffsetUs)
        {
            var result = new OptimizerResult
            {
                OptimizerType = "GeneticAlgorithm",
                StartTime = DateTime.Now
            };

            try
            {
                var gaOptimizer = new GeneticAlgorithmOptimizer(_timeBaseUs, actualMaxOffsetUs, _parameters.GAParameters);
                var tasksCopy = CreateTasksCopy(tasks);
                var gaResult = gaOptimizer.Optimize(tasksCopy, initialSolution);

                result.Success = gaResult.Success;
                result.FinalCost = gaResult.BestCost;
                result.FinalSolution = gaResult.BestOffsets;
                result.ErrorMessage = gaResult.ErrorMessage;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = $"遗传算法执行失败: {ex.Message}";
                result.FinalCost = double.MaxValue;
            }
            finally
            {
                result.EndTime = DateTime.Now;
                result.ExecutionTime = result.EndTime - result.StartTime;
            }

            return result;
        }

        /// <summary>
        /// 选择最佳策略结果
        /// 使用后处理筛选策略：效果相同时按综合评估分数选择，支持重叠率检测
        /// </summary>
        /// <param name="strategyResults">所有策略结果</param>
        /// <param name="tasks">任务列表（用于重叠率计算）</param>
        /// <param name="actualMaxOffsetUs">实际的最大offset限制</param>
        /// <returns>最佳策略结果</returns>
        private MultiStartStrategyResult SelectBestResult(List<MultiStartStrategyResult> strategyResults, List<TaskItem> tasks, int actualMaxOffsetUs)
        {
            // 过滤成功的结果
            var successfulResults = strategyResults.Where(r => r.Success).ToList();
            
            if (successfulResults.Count == 0)
            {
                // 检查是否所有策略都因为offset限制过小而失败
                var offsetLimitFailures = strategyResults.Where(r => 
                    !r.Success && r.ErrorMessage.Contains("当前offset限制") && r.ErrorMessage.Contains("过小")).ToList();
                
                if (offsetLimitFailures.Count == strategyResults.Count)
                {
                    // 所有策略都因为offset限制过小而失败
                    var failedResult = strategyResults.First();
                    failedResult.ErrorMessage = $"所有启发式策略都失败，当前offset限制({TaskUtility.ConvertToMilliseconds(actualMaxOffsetUs):F3}ms)过小，无法为任务集找到合适的初始解";
                    //Console.WriteLine($"❌ [多起点优化] {failedResult.ErrorMessage}");
                    return failedResult;
                }
                
                // 其他类型的失败，返回初始成本最低的
                //Console.WriteLine($"⚠️ [多起点优化] 所有策略执行失败，但不是因为offset限制问题");
                return strategyResults.OrderBy(r => r.InitialCost).First();
            }

            // 按最终成本排序，获取最佳成本
            var bestCost = successfulResults.Min(r => r.FinalCost);
            
            //Console.WriteLine($"[后处理筛选] 最佳成本: {bestCost:F6}");

            // 第一步：筛选最佳成本的结果（移除容差，只选择真正最佳成本）
            var bestCostResults = successfulResults
                .Where(r => r.FinalCost == bestCost)
                .ToList();
            
            //Console.WriteLine($"[后处理筛选] 最佳成本策略数量: {bestCostResults.Count}");
            
            if (bestCostResults.Count == 1)
            {
                //Console.WriteLine($"[后处理筛选] 唯一最佳策略: {bestCostResults.First().Strategy}");
                return bestCostResults.First();
            }
            
            // 第二步：使用统一的新选择逻辑（支持重叠率计算）
            var strategyCandidates = bestCostResults.Select(r => 
            {
                // 为重叠率计算创建临时的OptimizationResultSummary
                var tempTasks = CreateTasksCopy(tasks);
                ApplySolutionToTasks(tempTasks, r.FinalSolution);
                
                var tempResult = new OptimizationResultSummary
                {
                    Algorithm = "MultiStart",
                    Success = true,
                    BestCost = r.FinalCost,
                    OptimizedTasks = tempTasks
                };
                
                return new StrategyCandidateResult
                {
                    Algorithm = "MultiStart",
                    Strategy = r.Strategy.ToString(),
                    Cost = r.FinalCost,
                    Solution = r.FinalSolution,
                    OriginalResult = tempResult // 提供任务信息以支持重叠率计算
                };
            }).ToList();

            var bestCandidate = IntelligentSchedulingEngine.SelectBestByNewCriteria(strategyCandidates);
            
            // 找到对应的MultiStartStrategyResult
            var selectedResult = bestCostResults.First(r => r.Strategy.ToString() == bestCandidate.Strategy);
            
            //Console.WriteLine($"\n[后处理筛选] 使用新统一选择逻辑（包含重叠率检测）");
            //Console.WriteLine($"[后处理筛选] 最终选择: {selectedResult.Strategy}");
            //Console.WriteLine($"  选择原因: 新选择逻辑 - 优先冲突率 -> 重叠率 -> 均值 -> 方差(越大越好)");
                
            return selectedResult;
        }

        /// <summary>
        /// 应用最佳解到任务列表
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="bestResult">最佳结果</param>
        private void ApplyBestSolutionToTasks(List<TaskItem> tasks, MultiStartStrategyResult bestResult)
        {
            if (bestResult.FinalSolution != null)
            {
                //Console.WriteLine($"[调试警告检查] MultiStartOptimizer应用最佳解 - 策略: {bestResult.Strategy}");
                
                // 检查解是否超出各任务的独立限制
                var taskIndexToTask = tasks.ToDictionary(t => t.TaskIndex, t => t);
                foreach (var kvp in bestResult.FinalSolution)
                {
                    if (taskIndexToTask.ContainsKey(kvp.Key))
                    {
                        var task = taskIndexToTask[kvp.Key];
                        int taskMaxOffsetUs = task.MaxOffsetUs > 0 ? task.MaxOffsetUs : _maxOffsetUs;
                        
                        if (kvp.Value > taskMaxOffsetUs) // 直接比较微秒值
                        {
                            //Console.WriteLine($"🚨 [警告] MultiStart最佳解超出任务限制! TaskIndex={kvp.Key}, Offset={TaskUtility.ConvertToMilliseconds(kvp.Value):F3}ms, 任务限制={TaskUtility.ConvertToMilliseconds(taskMaxOffsetUs):F3}ms");
                        }
                    }
                }
                
                ApplySolutionToTasks(tasks, bestResult.FinalSolution);
            }
        }

        /// <summary>
        /// 应用解到任务列表
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="solution">解字典</param>
        private void ApplySolutionToTasks(List<TaskItem> tasks, Dictionary<int, int> solution)
        {
            foreach (var task in tasks)
            {
                if (solution.TryGetValue(task.TaskIndex, out int offsetUs))
                {
                    task.OffsetUs = offsetUs;
                }
            }
        }

        /// <summary>
        /// 从任务列表中提取解
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <returns>解字典</returns>
        private Dictionary<int, int> ExtractSolutionFromTasks(List<TaskItem> tasks)
        {
            var solution = new Dictionary<int, int>();
            foreach (var task in tasks)
            {
                if (task.OffsetUs.HasValue)
                {
                    solution[task.TaskIndex] = task.OffsetUs.Value;
                }
            }
            return solution;
        }

        /// <summary>
        /// 创建任务列表的副本
        /// </summary>
        /// <param name="originalTasks">原始任务列表</param>
        /// <returns>任务列表副本</returns>
        private List<TaskItem> CreateTasksCopy(List<TaskItem> originalTasks)
        {
            return TaskUtility.CloneTasks(originalTasks);
        }

        /// <summary>
        /// 创建错误结果
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <returns>错误结果</returns>
        private MultiStartResult CreateErrorResult(string errorMessage)
        {
            return new MultiStartResult
            {
                Success = false,
                ErrorMessage = errorMessage,
                OptimizationTime = TimeSpan.Zero,
                StrategyResults = new List<MultiStartStrategyResult>()
            };
        }

        #endregion
    }

    #region 参数和结果类

    /// <summary>
    /// 多起点优化参数
    /// </summary>
    public class MultiStartParameters
    {
        /// <summary>
        /// 是否使用基础策略（第一阶段）
        /// </summary>
        public bool UseBasicStrategies { get; set; } = true;

        /// <summary>
        /// 是否使用扩展策略（第三阶段）
        /// </summary>
        public bool UseExtendedStrategies { get; set; } = true;

        /// <summary>
        /// 是否使用高级策略（包括OffsetCalculator等）
        /// </summary>
        public bool UseAdvancedStrategies { get; set; } = true;

        /// <summary>
        /// 是否使用模拟退火优化器
        /// </summary>
        public bool UseSimulatedAnnealing { get; set; } = true;

        /// <summary>
        /// 是否使用遗传算法优化器
        /// </summary>
        public bool UseGeneticAlgorithm { get; set; } = true;

        /// <summary>
        /// 后处理筛选的成本容差比例（已废弃：现在直接选择最佳成本，无容差）
        /// </summary>
        [Obsolete("容差机制已移除，现在直接选择最佳成本")]
        public double CostToleranceRatio { get; set; } = 0.001;

        /// <summary>
        /// 模拟退火参数
        /// </summary>
        public OptimizationParameters SAParameters { get; set; } = new OptimizationParameters();

        /// <summary>
        /// 遗传算法参数
        /// </summary>
        public GAOptimizationParameters GAParameters { get; set; } = new GAOptimizationParameters();
    }

    /// <summary>
    /// 多起点优化结果
    /// </summary>
    public class MultiStartResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; } = "";

        /// <summary>
        /// 最佳策略
        /// </summary>
        public HeuristicSortStrategy BestStrategy { get; set; }

        /// <summary>
        /// 最佳成本
        /// </summary>
        public double BestCost { get; set; }

        /// <summary>
        /// 最佳解
        /// </summary>
        public Dictionary<int, int> BestSolution { get; set; }

        /// <summary>
        /// 所有策略的结果
        /// </summary>
        public List<MultiStartStrategyResult> StrategyResults { get; set; } = new List<MultiStartStrategyResult>();

        /// <summary>
        /// 总优化时间
        /// </summary>
        public TimeSpan OptimizationTime { get; set; }

        /// <summary>
        /// 使用的策略总数
        /// </summary>
        public int TotalStrategiesUsed { get; set; }
    }

    /// <summary>
    /// 单个策略的结果
    /// </summary>
    public class MultiStartStrategyResult
    {
        /// <summary>
        /// 策略类型
        /// </summary>
        public HeuristicSortStrategy Strategy { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; } = "";

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 执行时间
        /// </summary>
        public TimeSpan ExecutionTime { get; set; }

        /// <summary>
        /// 初始解
        /// </summary>
        public Dictionary<int, int> InitialSolution { get; set; }

        /// <summary>
        /// 初始成本
        /// </summary>
        public double InitialCost { get; set; }

        /// <summary>
        /// 最终解
        /// </summary>
        public Dictionary<int, int> FinalSolution { get; set; }

        /// <summary>
        /// 最终成本
        /// </summary>
        public double FinalCost { get; set; }

        /// <summary>
        /// 改进比例
        /// </summary>
        public double ImprovementRatio { get; set; }

        /// <summary>
        /// 最佳优化器
        /// </summary>
        public string BestOptimizer { get; set; }

        /// <summary>
        /// 优化器结果列表
        /// </summary>
        public List<OptimizerResult> OptimizerResults { get; set; } = new List<OptimizerResult>();

        /// <summary>
        /// 统一挑选阶段计算并缓存的额外指标（用于报告复用，避免重复计算）
        /// </summary>
        public SelectionExtraMetrics ExtraMetrics { get; set; }
    }

    /// <summary>
    /// 单个优化器的结果
    /// </summary>
    public class OptimizerResult
    {
        /// <summary>
        /// 优化器类型
        /// </summary>
        public string OptimizerType { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; } = "";

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 执行时间
        /// </summary>
        public TimeSpan ExecutionTime { get; set; }

        /// <summary>
        /// 最终成本
        /// </summary>
        public double FinalCost { get; set; }

        /// <summary>
        /// 最终解
        /// </summary>
        public Dictionary<int, int> FinalSolution { get; set; }
    }

    #endregion
} 