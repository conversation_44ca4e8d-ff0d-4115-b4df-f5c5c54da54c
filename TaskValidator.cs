using System;
using System.Collections.Generic;
using System.Linq;

namespace CalculateOffset
{
    /// <summary>
    /// 循环依赖异常类
    /// </summary>
    public class CircularDependencyException : Exception
    {
        public List<List<string>> CircularDependencies { get; }

        public CircularDependencyException(List<List<string>> circularDependencies) 
            : base(GenerateErrorMessage(circularDependencies))
        {
            CircularDependencies = circularDependencies;
        }

        private static string GenerateErrorMessage(List<List<string>> circularDependencies)
        {
            if (circularDependencies == null || circularDependencies.Count == 0)
                return "检测到循环依赖，但无法确定具体的循环路径。";

            var message = "检测到以下循环依赖:\n";
            for (int i = 0; i < circularDependencies.Count; i++)
            {
                var cycle = circularDependencies[i];
                message += $"循环 {i + 1}: {string.Join(" → ", cycle)} → {cycle[0]}\n";
            }
            return message.TrimEnd('\n');
        }
    }

    /// <summary>
    /// 任务验证器，用于验证前置任务的有效性和循环依赖检查
    /// </summary>
    public class TaskValidator
    {
        /// <summary>
        /// 验证结果
        /// </summary>
        public class ValidationResult
        {
            public bool IsValid { get; set; }
            public string ErrorMessage { get; set; } = "";
            public List<string> Warnings { get; set; } = new List<string>();
        }

        /// <summary>
        /// 验证任务列表中的前置任务关系
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <returns>验证结果</returns>
        public static ValidationResult ValidatePrerequisiteTasks(List<TaskItem> tasks)
        {
            var result = new ValidationResult { IsValid = true };

            // 获取所有有效任务名（非空且不重复）
            var validTaskNames = new HashSet<string>(
                tasks.Where(t => !string.IsNullOrWhiteSpace(t.TaskName))
                     .Select(t => t.TaskName.Trim())
                     .Distinct(StringComparer.OrdinalIgnoreCase),
                StringComparer.OrdinalIgnoreCase);

            // 检查每个任务的前置任务
            foreach (var task in tasks)
            {
                var taskName = task.TaskName.Trim();

                // 如果没有前置任务，跳过
                if (string.IsNullOrWhiteSpace(task.PrerequisiteTasks))
                    continue;

                // 解析前置任务列表
                var prerequisiteNames = ParsePrerequisiteTaskNames(task.PrerequisiteTasks);

                // 检查前置任务是否存在
                foreach (var prerequisiteName in prerequisiteNames)
                {
                    if (!validTaskNames.Contains(prerequisiteName))
                    {
                        result.IsValid = false;
                        result.ErrorMessage += $"任务 '{taskName}' 的前置任务 '{prerequisiteName}' 不存在于任务列表中。\n";
                    }
                }

                // 检查是否存在自引用
                if (prerequisiteNames.Contains(taskName, StringComparer.OrdinalIgnoreCase))
                {
                    result.IsValid = false;
                    result.ErrorMessage += $"任务 '{taskName}' 不能将自己设为前置任务。\n";
                }
            }

            // 检查循环依赖
            var circularDependencies = DetectCircularDependencies(tasks);
            if (circularDependencies.Count > 0)
            {
                result.IsValid = false;
                result.ErrorMessage += "检测到循环依赖：\n";
                foreach (var cycle in circularDependencies)
                {
                    result.ErrorMessage += $"  {string.Join(" -> ", cycle)} -> {cycle[0]}\n";
                }
            }

            return result;
        }

        /// <summary>
        /// 快速可调度性预检查（必要条件）：仅基于执行时间与每个任务一次jitter
        /// 条件：
        /// 1) ∀i: C_i + J_i <= T_i
        /// 2) Σ(C_i + J_i)/T_i <= 1
        /// 任一不满足则认为不可调度，用于在启动重型优化前做快速拦截。
        /// </summary>
        public static bool QuickFeasibilityCheck(List<TaskItem> tasks, out string message)
        {
            message = string.Empty;
            if (tasks == null || tasks.Count == 0)
            {
                message = "任务列表为空";
                return false;
            }

            try
            {
                var invalidTasks = new List<string>();
                double utilization = 0.0;

                foreach (var t in tasks)
                {
                    if (!t.Enabled || !t.ExecutionTimeUs.HasValue || !t.PeriodUs.HasValue)
                    {
                        continue;
                    }

                    int execUs = t.ExecutionTimeUs.Value;
                    int periodUs = t.PeriodUs.Value;
                    int jitterUs = t.JitterTimeUs;

                    if (execUs + jitterUs > periodUs)
                    {
                        string name = string.IsNullOrWhiteSpace(t.TaskName) ? $"Task#{t.TaskIndex}" : t.TaskName;
                        invalidTasks.Add($"{name}(C={TaskUtility.ConvertToMilliseconds(execUs):F3}ms, J={TaskUtility.ConvertToMilliseconds(jitterUs):F3}ms, T={TaskUtility.ConvertToMilliseconds(periodUs):F3}ms)");
                    }

                    utilization += (double)(execUs + jitterUs) / periodUs;
                }

                if (invalidTasks.Count > 0)
                {
                    message = "快速可调度性预检查失败：以下任务满足不了 (C+J)<=T 的必要条件:\n\n" + string.Join("\n", invalidTasks);
                    return false;
                }

                if (utilization > 1.0)
                {
                    message = $"快速可调度性预检查失败：Σ(C+J)/T = {utilization * 100.0:F2}% > 100%";
                    return false;
                }

                // 通过快速可调度性检查
                message = $"通过：Σ(C+J)/T = {utilization * 100.0:F2}% ≤ 100% (仅必要条件)";
                return true;
            }
            catch (Exception ex)
            {
                message = $"快速检查异常：{ex.Message}。忽略快速检查。";
                return true; // 快速检查异常不阻塞
            }
        }

        /// <summary>
        /// 解析前置任务名称列表
        /// </summary>
        /// <param name="prerequisiteTasksStr">前置任务字符串，用逗号分隔</param>
        /// <returns>前置任务名称列表</returns>
        public static List<string> ParsePrerequisiteTaskNames(string prerequisiteTasksStr)
        {
            if (string.IsNullOrWhiteSpace(prerequisiteTasksStr))
                return new List<string>();

            return prerequisiteTasksStr
                .Split(new char[] { ',', '，' }, StringSplitOptions.RemoveEmptyEntries) // 支持中英文逗号
                .Select(name => name.Trim())
                .Where(name => !string.IsNullOrWhiteSpace(name))
                .Distinct(StringComparer.OrdinalIgnoreCase)
                .ToList();
        }

        /// <summary>
        /// 检测循环依赖
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <returns>循环依赖路径列表</returns>
        private static List<List<string>> DetectCircularDependencies(List<TaskItem> tasks)
        {
            var circularDependencies = new List<List<string>>();

            // 构建依赖图
            var dependencyGraph = new Dictionary<string, List<string>>(StringComparer.OrdinalIgnoreCase);

            foreach (var task in tasks)
            {
                if (string.IsNullOrWhiteSpace(task.TaskName))
                    continue;

                var taskName = task.TaskName.Trim();
                var prerequisites = ParsePrerequisiteTaskNames(task.PrerequisiteTasks);

                dependencyGraph[taskName] = prerequisites;
            }

            // 使用DFS检测循环
            var visited = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
            var recursionStack = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
            var currentPath = new List<string>();

            foreach (var taskName in dependencyGraph.Keys)
            {
                if (!visited.Contains(taskName))
                {
                    DetectCyclesDFS(taskName, dependencyGraph, visited, recursionStack, currentPath, circularDependencies);
                }
            }

            return circularDependencies;
        }

        /// <summary>
        /// 使用深度优先搜索检测循环依赖
        /// </summary>
        private static bool DetectCyclesDFS(
            string taskName,
            Dictionary<string, List<string>> dependencyGraph,
            HashSet<string> visited,
            HashSet<string> recursionStack,
            List<string> currentPath,
            List<List<string>> circularDependencies)
        {
            visited.Add(taskName);
            recursionStack.Add(taskName);
            currentPath.Add(taskName);

            if (dependencyGraph.ContainsKey(taskName))
            {
                foreach (var prerequisite in dependencyGraph[taskName])
                {
                    if (!dependencyGraph.ContainsKey(prerequisite))
                        continue; // 跳过不存在的前置任务

                    if (!visited.Contains(prerequisite))
                    {
                        if (DetectCyclesDFS(prerequisite, dependencyGraph, visited, recursionStack, currentPath, circularDependencies))
                        {
                            return true;
                        }
                    }
                    else if (recursionStack.Contains(prerequisite))
                    {
                        // 找到循环，构建循环路径
                        var cycleStartIndex = currentPath.FindIndex(t => string.Equals(t, prerequisite, StringComparison.OrdinalIgnoreCase));
                        if (cycleStartIndex >= 0)
                        {
                            var cycle = currentPath.Skip(cycleStartIndex).ToList();
                            circularDependencies.Add(cycle);
                        }
                        return true;
                    }
                }
            }

            recursionStack.Remove(taskName);
            currentPath.RemoveAt(currentPath.Count - 1);
            return false;
        }

        /// <summary>
        /// 根据前置任务关系对任务进行拓扑排序
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <returns>排序后的任务列表</returns>
        /// <exception cref="CircularDependencyException">当检测到循环依赖时抛出</exception>
        public static List<TaskItem> TopologicalSort(List<TaskItem> tasks)
        {
            // 首先检测循环依赖
            var circularDependencies = DetectCircularDependencies(tasks);
            if (circularDependencies.Count > 0)
            {
                // 发现循环依赖，立即抛出异常
                throw new CircularDependencyException(circularDependencies);
            }

            // 构建依赖图和入度计算
            var dependencyGraph = new Dictionary<string, List<string>>(StringComparer.OrdinalIgnoreCase);
            var inDegree = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
            var taskMap = new Dictionary<string, TaskItem>(StringComparer.OrdinalIgnoreCase);

            // 初始化
            foreach (var task in tasks)
            {
                if (string.IsNullOrWhiteSpace(task.TaskName))
                    continue;

                var taskName = task.TaskName.Trim();
                taskMap[taskName] = task;
                dependencyGraph[taskName] = new List<string>();
                inDegree[taskName] = 0;
            }

            // 构建依赖关系
            foreach (var task in tasks)
            {
                if (string.IsNullOrWhiteSpace(task.TaskName))
                    continue;

                var taskName = task.TaskName.Trim();
                var prerequisites = ParsePrerequisiteTaskNames(task.PrerequisiteTasks);

                foreach (var prerequisite in prerequisites)
                {
                    if (dependencyGraph.ContainsKey(prerequisite))
                    {
                        dependencyGraph[prerequisite].Add(taskName);
                        inDegree[taskName]++;
                    }
                }
            }

            // Kahn算法进行拓扑排序
            var queue = new Queue<string>();
            var result = new List<TaskItem>();

            // 找到所有入度为0的节点
            foreach (var kvp in inDegree)
            {
                if (kvp.Value == 0)
                {
                    queue.Enqueue(kvp.Key);
                }
            }

            // 处理队列
            while (queue.Count > 0)
            {
                var currentTask = queue.Dequeue();
                result.Add(taskMap[currentTask]);

                // 减少相邻节点的入度
                foreach (var neighbor in dependencyGraph[currentTask])
                {
                    inDegree[neighbor]--;
                    if (inDegree[neighbor] == 0)
                    {
                        queue.Enqueue(neighbor);
                    }
                }
            }

            // 理论上不应该再有循环依赖，但为了安全起见再次检查
            if (result.Count != taskMap.Count)
            {
                // 这种情况理论上不应该发生，因为我们已经在开头检测了循环依赖
                // 但如果真的发生了，说明检测算法有问题，抛出异常
                throw new CircularDependencyException(new List<List<string>>
                {
                    new List<string> { "未知循环依赖" }
                });
            }

            return result;
        }
    }
}