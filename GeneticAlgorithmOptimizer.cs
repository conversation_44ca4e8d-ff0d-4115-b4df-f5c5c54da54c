using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace CalculateOffset
{
    /// <summary>
    /// 遗传算法优化器
    /// 基于生物进化理论的全局搜索算法，通过选择、交叉、变异操作寻找最优Offset组合
    /// </summary>
    public class GeneticAlgorithmOptimizer
    {
        #region 私有字段
        
        private readonly int _timeBaseUs;
        private readonly int _maxOffsetUs;
        private readonly GAOptimizationParameters _parameters;
        private readonly SchedulabilityAnalyzer _analyzer;
        private readonly Random _random;
        
        /// <summary>
        /// 最佳解缓存管理器 - 实现三级挑选机制的一级挑选
        /// </summary>
        private ThreeTierSelectionCache _bestSolutionCache;
        
        #endregion
        
        #region 构造函数
        
        /// <summary>
        /// 初始化遗传算法优化器
        /// </summary>
        /// <param name="timeBaseUs">时基值(微秒)</param>
        /// <param name="maxOffsetUs">最大Offset值(微秒)</param>
        /// <param name="parameters">GA优化参数</param>
        public GeneticAlgorithmOptimizer(int timeBaseUs, int maxOffsetUs, GAOptimizationParameters parameters = null)
        {
            _timeBaseUs = timeBaseUs;
            _maxOffsetUs = maxOffsetUs;
            _parameters = parameters ?? new GAOptimizationParameters();
            _analyzer = new SchedulabilityAnalyzer(timeBaseUs);
            _random = new Random(_parameters.RandomSeed);
        }
        
        #endregion
        
        #region 主要公共方法
        
        /// <summary>
        /// 使用遗传算法优化任务Offset
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="initialSolution">外部提供的初始解（可选）</param>
        /// <returns>优化结果</returns>
        public GAOptimizationResult Optimize(List<TaskItem> tasks, Dictionary<int, int> initialSolution = null)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new GAOptimizationResult();
            
            // 📊 输出优化开始前的缓存统计
            var initialCacheStats = SchedulabilityAnalyzer.GetCacheStatistics();
            Console.WriteLine($"🚀 [GA优化开始] 评估缓存状态: {initialCacheStats.CacheSize}个解已缓存, 命中率: {initialCacheStats.HitRate:P2}");
            
            try
            {

                // 参数验证
                if (tasks == null || tasks.Count == 0)
                {
                    return CreateErrorResult("任务列表为空或无效");
                }
                
                // 初始化统计信息
                var statistics = new GAOptimizationStatistics();
                statistics.StartTime = DateTime.Now;
                
                // 执行遗传算法优化
                var optimizationResult = ExecuteGeneticAlgorithm(tasks, statistics, initialSolution);
                
                if (optimizationResult.Success)
                {
                    // 应用最佳Offset到任务
                    ApplyOffsetsToTasks(tasks, optimizationResult.BestOffsets);
                    
                    // 计算最终的可调度性分析
                    var finalAnalysis = _analyzer.AnalyzeSchedulability(tasks);
                    
                    // 构建成功结果
                    result.Success = true;
                    result.BestOffsets = optimizationResult.BestOffsets;
                    result.BestCost = optimizationResult.BestCost;
                    result.SchedulabilityResult = finalAnalysis;
                    result.Statistics = statistics;
                    result.Statistics.OptimizationTime = stopwatch.Elapsed;
                }
                else
                {
                    result.Success = false;
                    result.ErrorMessage = optimizationResult.ErrorMessage;
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = $"遗传算法优化异常: {ex.Message}";
            }
            finally
            {
                stopwatch.Stop();
                
                // 📊 输出优化结束后的缓存统计
                var finalCacheStats = SchedulabilityAnalyzer.GetCacheStatistics();
                var cacheHitIncrease = finalCacheStats.HitCount - initialCacheStats.HitCount;
                var cacheMissIncrease = finalCacheStats.MissCount - initialCacheStats.MissCount;
                Console.WriteLine($"🎯 [GA优化完成] 缓存统计变化: 新增命中 {cacheHitIncrease} 次, 新增未命中 {cacheMissIncrease} 次");
                Console.WriteLine($"📈 [GA优化完成] 最终缓存: {finalCacheStats.CacheSize}个解, 总命中率: {finalCacheStats.HitRate:P2}");
                if (cacheHitIncrease > 0)
                {
                    Console.WriteLine($"⚡ [性能提升] GA优化过程中缓存节省了 {cacheHitIncrease} 次WCRT分析！");
                }
            }
            
            return result;
        }
        
        #endregion
        
        #region 核心GA算法实现
        
        /// <summary>
        /// 执行遗传算法主流程
        /// </summary>
        private GAInternalResult ExecuteGeneticAlgorithm(List<TaskItem> tasks, GAOptimizationStatistics statistics, Dictionary<int, int> initialSolution = null)
        {
            try
            {
                // 初始化种群
                var population = InitializePopulation(tasks, initialSolution);
                statistics.InitialPopulationSize = population.Count;
                
                // 评估初始种群
                EvaluatePopulation(tasks, population);
                
                // 初始化最佳解缓存管理器 - 三级挑选机制的一级挑选
                _bestSolutionCache = new ThreeTierSelectionCache(tasks, "GA");
                
                // 将初始种群中的所有个体加入缓存
                foreach (var individual in population)
                {
                    _bestSolutionCache.TryAddSolution(individual.Offsets, individual.Cost);
                }
                
                var bestIndividual = population.OrderBy(ind => ind.Cost).First();
                statistics.InitialBestCost = bestIndividual.Cost;
                statistics.GenerationStats = new List<GAGenerationStatistics>();
                
                int generationsWithoutImprovement = 0;
                
                // 主要进化循环
                for (int generation = 0; generation < _parameters.MaxGenerations; generation++)
                {
                    var generationStats = new GAGenerationStatistics
                    {
                        Generation = generation,
                        StartTime = DateTime.Now
                    };
                    
                    // 选择操作
                    var selectedParents = Selection(population);
                    generationStats.SelectionCount = selectedParents.Count;
                    
                    // 交叉操作
                    var offspring = Crossover(selectedParents, tasks);
                    generationStats.CrossoverCount = offspring.Count;
                    
                    // 变异操作
                    var mutatedOffspring = Mutation(offspring, tasks, generation);
                    generationStats.MutationCount = mutatedOffspring.Count(ind => ind.WasMutated);
                    
                    // 评估新个体
                    EvaluatePopulation(tasks, mutatedOffspring);
                    
                    // 将新个体加入缓存
                    foreach (var individual in mutatedOffspring)
                    {
                        _bestSolutionCache.TryAddSolution(individual.Offsets, individual.Cost);
                    }
                    
                    // 环境选择（生存者选择）
                    population = EnvironmentalSelection(population, mutatedOffspring);
                    
                    // 更新最佳解（用于早停和统计）
                    var currentBest = population.OrderBy(ind => ind.Cost).First();
                    var currentBestCost = _bestSolutionCache.GetBestCost();
                    
                    if (currentBest.Cost <= currentBestCost + 1e-10) // 考虑浮点误差
                    {
                        generationsWithoutImprovement = 0;
                        statistics.BestSolutionGeneration = generation;
                    }
                    else
                    {
                        generationsWithoutImprovement++;
                    }
                    
                    // 记录代际统计
                    generationStats.BestCost = currentBest.Cost;
                    generationStats.AverageCost = population.Average(ind => ind.Cost);
                    generationStats.WorstCost = population.Max(ind => ind.Cost);
                    generationStats.EndTime = DateTime.Now;
                    statistics.GenerationStats.Add(generationStats);
                    
                    // 早停检查
                    if (_parameters.UseEarlyStopping && 
                        generationsWithoutImprovement >= _parameters.NoImprovementThreshold)
                    {
                        statistics.EarlyStoppingSavings = _parameters.MaxGenerations - generation - 1;
                        break;
                    }
                    
                    // 收敛检查
                    if (CheckConvergence(population))
                    {
                        statistics.ConvergedAtGeneration = generation;
                        break;
                    }
                }
                
                // 进化结束后，执行一级挑选：从缓存的相同成本解中选出最优解
                var finalBestOffsets = _bestSolutionCache.SelectBestFromCache();
                var finalBestCost = _bestSolutionCache.GetBestCost();

                if (_bestSolutionCache.GetCachedCount() > 1)
                {
                    //Console.WriteLine($"[一级挑选-GA] 进化结束，缓存了 {_bestSolutionCache.GetCachedCount()} 个最佳成本解，最终成本={finalBestCost:F6}");
                }
                
                // 更新最终统计
                statistics.FinalBestCost = finalBestCost;
                statistics.TotalGenerations = statistics.GenerationStats.Count;
                statistics.ImprovementPercentage = statistics.InitialBestCost > 0 ? 
                    ((statistics.InitialBestCost - finalBestCost) / statistics.InitialBestCost) * 100 : 0;
                
                return new GAInternalResult
                {
                    Success = true,
                    BestOffsets = finalBestOffsets ?? bestIndividual.Offsets,
                    BestCost = finalBestCost
                };
            }
            catch (Exception ex)
            {
                return new GAInternalResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }
        
        /// <summary>
        /// 初始化种群
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="externalInitialSolution">外部提供的初始解（可选）</param>
        /// <returns>初始种群</returns>
        private List<GAIndividual> InitializePopulation(List<TaskItem> tasks, Dictionary<int, int> externalInitialSolution = null)
        {
            var population = new List<GAIndividual>();
            
            // 1. 优先注入外部提供的初始解
            if (externalInitialSolution != null && externalInitialSolution.Count > 0)
            {
                population.Add(new GAIndividual { Offsets = new Dictionary<int, int>(externalInitialSolution) });
            }

            // 2. 注入启发式种子
            if (_parameters.UseHeuristicSeeding)
            {
                // 计算所有任务的最大MaxOffsetUs值，用于启发式种子生成
                int actualMaxOffsetUs = tasks.Any(t => t.MaxOffsetUs > 0) ? 
                    tasks.Where(t => t.MaxOffsetUs > 0).Max(t => t.MaxOffsetUs) : _maxOffsetUs;
                
                // 调用 TaskUtility 中的共享方法
                var heuristicOffsets = TaskUtility.GenerateHeuristicSeed(tasks, _timeBaseUs, actualMaxOffsetUs);
                if (heuristicOffsets != null && heuristicOffsets.Count > 0)
                {
                    population.Add(new GAIndividual { Offsets = heuristicOffsets });
                }
            }

            // 3. 填充剩余的随机个体
            int remainingSize = _parameters.PopulationSize - population.Count;

            for (int i = 0; i < remainingSize; i++)
            {
                var individual = new GAIndividual();
                individual.Offsets = new Dictionary<int, int>();
                
                foreach (var task in tasks)
                {
                    if (task.ManualInput && task.OffsetUs.HasValue)
                    {
                        individual.Offsets[task.TaskIndex] = task.OffsetUs.Value;
                    }
                    else
                    {
                        // 使用该任务的最大offset限制
                        int taskMaxOffsetUs = task.MaxOffsetUs;
                        // 方案1：直接生成时基对齐的随机offset，避免浮点运算
                        int maxSteps = taskMaxOffsetUs / _timeBaseUs;
                        int randomOffsetUs = _random.Next(0, maxSteps + 1) * _timeBaseUs;
                        individual.Offsets[task.TaskIndex] = randomOffsetUs;
                    }
                }
                population.Add(individual);
            }
            
            return population;
        }


        
        /// <summary>
        /// 评估种群中所有个体的适应度
        /// </summary>
        private void EvaluatePopulation(List<TaskItem> tasks, List<GAIndividual> population)
        {
            foreach (var individual in population.Where(ind => !ind.IsEvaluated))
            {
                // 应用Offset到任务副本
                var tasksCopy = CreateTasksCopy(tasks);
                ApplyOffsetsToTasks(tasksCopy, individual.Offsets);
                
                var analysis = _analyzer.AnalyzeSchedulability(tasksCopy);
                individual.Cost = analysis.CostValue;
                // 降噪：仅在出现截止期违背或成为当前最佳时输出
                if (analysis.Statistics != null && analysis.Statistics.DeadlineMissCount > 0)
                {
                    Console.WriteLine($"[GA] 个体评估: 成本={individual.Cost:F6}, Miss={analysis.Statistics.DeadlineMissCount}, MaxR/DDL={analysis.Statistics.MaxResponseTimeRatio:F6}");
                }
                individual.IsEvaluated = true;
            }
        }
        
        /// <summary>
        /// 选择操作
        /// </summary>
        private List<GAIndividual> Selection(List<GAIndividual> population)
        {
            switch (_parameters.SelectionMethod)
            {
                case SelectionType.Tournament:
                    return TournamentSelection(population);
                case SelectionType.Rank:
                    return RankSelection(population);
                case SelectionType.RouletteWheel:
                    return RouletteWheelSelection(population);
                default:
                    return TournamentSelection(population);
            }
        }
        
        /// <summary>
        /// 锦标赛选择 - 更稳健的选择策略
        /// </summary>
        private List<GAIndividual> TournamentSelection(List<GAIndividual> population)
        {
            var selected = new List<GAIndividual>();
            
            for (int i = 0; i < _parameters.SelectionSize; i++)
            {
                // 随机选择K个个体组成锦标赛
                var tournament = new List<GAIndividual>();
                for (int j = 0; j < _parameters.TournamentSize; j++)
                {
                    int randomIndex = _random.Next(population.Count);
                    tournament.Add(population[randomIndex]);
                }
                
                // 选择锦标赛中成本最低的个体
                var winner = tournament.OrderBy(ind => ind.Cost).First();
                selected.Add(CopyIndividual(winner));
            }
            
            return selected;
        }
        
        /// <summary>
        /// 排序选择
        /// </summary>
        private List<GAIndividual> RankSelection(List<GAIndividual> population)
        {
            var selected = new List<GAIndividual>();
            var sortedPopulation = population.OrderBy(ind => ind.Cost).ToList();
            
            // 计算排序适应度（排名越靠前，适应度越高）
            var rankFitness = new List<double>();
            double totalRankFitness = 0;
            
            for (int i = 0; i < sortedPopulation.Count; i++)
            {
                double fitness = sortedPopulation.Count - i; // 最好的个体适应度最高
                rankFitness.Add(fitness);
                totalRankFitness += fitness;
            }
            
            // 基于排序适应度进行轮盘赌选择
            for (int i = 0; i < _parameters.SelectionSize; i++)
            {
                double randomValue = _random.NextDouble() * totalRankFitness;
                double cumulativeFitness = 0;
                
                for (int j = 0; j < sortedPopulation.Count; j++)
                {
                    cumulativeFitness += rankFitness[j];
                    if (randomValue <= cumulativeFitness)
                    {
                        selected.Add(CopyIndividual(sortedPopulation[j]));
                        break;
                    }
                }
            }
            
            return selected;
        }
        
        /// <summary>
        /// 轮盘赌选择（原实现，保留作为选项）
        /// </summary>
        private List<GAIndividual> RouletteWheelSelection(List<GAIndividual> population)
        {
            var selected = new List<GAIndividual>();
            
            // 计算适应度（成本越低，适应度越高）
            var maxCost = population.Max(ind => ind.Cost);
            var minCost = population.Min(ind => ind.Cost);
            var costRange = maxCost - minCost;
            
            var fitnessValues = new List<double>();
            double totalFitness = 0;
            
            foreach (var individual in population)
            {
                // 适应度 = (maxCost - currentCost) + small_value
                double fitness = costRange > 0 ? (maxCost - individual.Cost) + 0.001 : 1.0;
                fitnessValues.Add(fitness);
                totalFitness += fitness;
            }
            
            // 轮盘赌选择
            for (int i = 0; i < _parameters.SelectionSize; i++)
            {
                double randomValue = _random.NextDouble() * totalFitness;
                double cumulativeFitness = 0;
                
                for (int j = 0; j < population.Count; j++)
                {
                    cumulativeFitness += fitnessValues[j];
                    if (randomValue <= cumulativeFitness)
                    {
                        selected.Add(CopyIndividual(population[j]));
                        break;
                    }
                }
            }
            
            return selected;
        }
        
        /// <summary>
        /// 交叉操作
        /// </summary>
        private List<GAIndividual> Crossover(List<GAIndividual> parents, List<TaskItem> tasks)
        {
            var offspring = new List<GAIndividual>();
            
            for (int i = 0; i < parents.Count - 1; i += 2)
            {
                if (_random.NextDouble() < _parameters.CrossoverRate)
                {
                    var parent1 = parents[i];
                    var parent2 = parents[i + 1];
                    
                    var (child1, child2) = PerformCrossover(parent1, parent2, tasks);
                    offspring.Add(child1);
                    offspring.Add(child2);
                }
                else
                {
                    // 不交叉，直接复制父代
                    offspring.Add(CopyIndividual(parents[i]));
                    if (i + 1 < parents.Count)
                        offspring.Add(CopyIndividual(parents[i + 1]));
                }
            }
            
            return offspring;
        }
        
        /// <summary>
        /// 执行交叉操作
        /// </summary>
        private (GAIndividual child1, GAIndividual child2) PerformCrossover(GAIndividual parent1, GAIndividual parent2, List<TaskItem> tasks)
        {
            switch (_parameters.CrossoverMethod)
            {
                case CrossoverType.Uniform:
                    return UniformCrossover(parent1, parent2);
                case CrossoverType.Arithmetic:
                    return ArithmeticCrossover(parent1, parent2, tasks);
                case CrossoverType.SinglePoint:
                    return SinglePointCrossover(parent1, parent2, tasks.Count);
                default:
                    return UniformCrossover(parent1, parent2);
            }
        }
        
        /// <summary>
        /// 均匀交叉 - 更适合独立基因的交叉方式
        /// </summary>
        private (GAIndividual child1, GAIndividual child2) UniformCrossover(GAIndividual parent1, GAIndividual parent2)
        {
            var child1 = new GAIndividual { Offsets = new Dictionary<int, int>() };
            var child2 = new GAIndividual { Offsets = new Dictionary<int, int>() };
            
            // 对每个基因位独立决定继承自哪个父代
            foreach (var taskIndex in parent1.Offsets.Keys)
            {
                if (_random.NextDouble() < 0.5)
                {
                    // 子代1继承父代1，子代2继承父代2
                    child1.Offsets[taskIndex] = parent1.Offsets[taskIndex];
                    child2.Offsets[taskIndex] = parent2.Offsets[taskIndex];
                }
                else
                {
                    // 子代1继承父代2，子代2继承父代1
                    child1.Offsets[taskIndex] = parent2.Offsets[taskIndex];
                    child2.Offsets[taskIndex] = parent1.Offsets[taskIndex];
                }
            }
            
            return (child1, child2);
        }
        
        /// <summary>
        /// 算术交叉 - 数值优化问题的理想交叉方式
        /// </summary>
        private (GAIndividual child1, GAIndividual child2) ArithmeticCrossover(GAIndividual parent1, GAIndividual parent2, List<TaskItem> tasks)
        {
            var child1 = new GAIndividual { Offsets = new Dictionary<int, int>() };
            var child2 = new GAIndividual { Offsets = new Dictionary<int, int>() };
            
            double alpha = _parameters.ArithmeticCrossoverAlpha;
            
            // 创建任务索引到任务对象的映射，用于获取每个任务的MaxOffsetUs
            var taskIndexToTask = tasks.ToDictionary(t => t.TaskIndex, t => t);
            
            // 线性组合生成子代（使用微秒整数运算）
            foreach (var taskIndex in parent1.Offsets.Keys)
            {
                int p1_value = parent1.Offsets[taskIndex];
                int p2_value = parent2.Offsets[taskIndex];
                
                // child1 = alpha * p1 + (1-alpha) * p2
                // child2 = alpha * p2 + (1-alpha) * p1
                int raw_child1 = (int)(alpha * p1_value + (1 - alpha) * p2_value);
                int raw_child2 = (int)(alpha * p2_value + (1 - alpha) * p1_value);
                
                // 获取该任务的最大offset限制
                int taskMaxOffsetUs = taskIndexToTask.ContainsKey(taskIndex) ? 
                    taskIndexToTask[taskIndex].MaxOffsetUs : _maxOffsetUs;
                
                // 使用高级边界处理策略
                child1.Offsets[taskIndex] = HandleBoundaryViolationUs(raw_child1, taskMaxOffsetUs);
                child2.Offsets[taskIndex] = HandleBoundaryViolationUs(raw_child2, taskMaxOffsetUs);
            }
            
            return (child1, child2);
        }

        /// <summary>
        /// 边界违规处理 - 使用多种策略避免边界聚集（微秒版本）
        /// </summary>
        /// <param name="value">原始值（微秒）</param>
        /// <returns>处理后的值（微秒）</returns>
        private int HandleBoundaryViolationUs(int value, int maxOffsetUs)
        {
            // 如果在合法范围内，直接返回时基约束后的值
            if (value >= 0 && value <= maxOffsetUs)
            {
                return TaskUtility.EnsureTimeBaseMultiple(value, _timeBaseUs);
            }
            
            // 根据参数选择边界处理策略
            switch (_parameters.BoundaryHandlingMethod)
            {
                case BoundaryHandlingType.Reflection:
                    return HandleReflectionBoundaryUs(value, maxOffsetUs);
                    
                case BoundaryHandlingType.Regeneration:
                    return HandleRegenerationBoundaryUs(maxOffsetUs);
                    
                case BoundaryHandlingType.Wrapping:
                    return HandleWrappingBoundaryUs(value, maxOffsetUs);
                    
                case BoundaryHandlingType.Clamping:
                default:
                    int clampedValue = Math.Max(0, Math.Min(maxOffsetUs, value));
                    return TaskUtility.EnsureTimeBaseMultiple(clampedValue, _timeBaseUs);
            }
        }

        /// <summary>
        /// 反射边界处理 - 持续反射直到回到有效区间（微秒版本）
        /// </summary>
        private int HandleReflectionBoundaryUs(int value, int maxOffsetUs)
        {
            int min = 0;
            int max = maxOffsetUs;
            
            // 持续反射直到值回到有效区间内
            while (value < min || value > max)
            {
                if (value < min)
                {
                    value = 2 * min - value; // 从下界反射
                }
                if (value > max)
                {
                    value = 2 * max - value; // 从上界反射
                }
            }
            
            return TaskUtility.EnsureTimeBaseMultiple(value, _timeBaseUs);
        }

        /// <summary>
        /// 重新生成边界处理（微秒版本）
        /// </summary>
        private int HandleRegenerationBoundaryUs(int maxOffsetUs)
        {
            // 越界时重新随机生成，方案1：直接生成时基对齐的随机值
            int maxSteps = maxOffsetUs / _timeBaseUs;
            int newValue = _random.Next(0, maxSteps + 1) * _timeBaseUs;
            return newValue; // 已经时基对齐，无需EnsureTimeBaseMultiple
        }

        /// <summary>
        /// 环绕边界处理（微秒版本）
        /// </summary>
        private int HandleWrappingBoundaryUs(int value, int maxOffsetUs)
        {
            if (maxOffsetUs <= 0) return 0;
            
            // 使用模运算实现环绕
            while (value < 0) value += maxOffsetUs;
            while (value > maxOffsetUs) value -= maxOffsetUs;
            
            return TaskUtility.EnsureTimeBaseMultiple(value, _timeBaseUs);
        }
        
        /// <summary>
        /// 单点交叉（原实现，保留作为选项）
        /// </summary>
        private (GAIndividual child1, GAIndividual child2) SinglePointCrossover(GAIndividual parent1, GAIndividual parent2, int taskCount)
        {
            var child1 = new GAIndividual { Offsets = new Dictionary<int, int>() };
            var child2 = new GAIndividual { Offsets = new Dictionary<int, int>() };
            
            // 随机选择交叉点
            int crossoverPoint = _random.Next(1, taskCount);
            
            var taskIndices = parent1.Offsets.Keys.OrderBy(k => k).ToList();
            
            for (int i = 0; i < taskIndices.Count; i++)
            {
                int taskIndex = taskIndices[i];
                
                if (i < crossoverPoint)
                {
                    child1.Offsets[taskIndex] = parent1.Offsets[taskIndex];
                    child2.Offsets[taskIndex] = parent2.Offsets[taskIndex];
                }
                else
                {
                    child1.Offsets[taskIndex] = parent2.Offsets[taskIndex];
                    child2.Offsets[taskIndex] = parent1.Offsets[taskIndex];
                }
            }
            
            return (child1, child2);
        }
        
        /// <summary>
        /// 变异操作
        /// </summary>
        private List<GAIndividual> Mutation(List<GAIndividual> offspring, List<TaskItem> tasks, int currentGeneration)
        {
            
            foreach (var individual in offspring)
            {
                bool mutated = false;
                
                // 新增：阻塞修复变异（优先级最高）
                if (_parameters.UseHeuristicMutation && _random.NextDouble() < 0.3) // 30%概率
                {
                    if (TryBlockingRepairMutationUs(individual, tasks))
                    {
                        mutated = true;
                        Console.WriteLine("[GA阻塞修复] 执行阻塞修复变异");
                    }
                }
                
                // 基因级变异检查
                foreach (var task in tasks)
                {
                    // 跳过手动输入的任务，不对其进行变异
                    if (task.ManualInput)
                    {
                        continue;
                    }
                    
                    if (_random.NextDouble() < _parameters.MutationRate)
                    {
                        var mutationType = _random.Next(4); // 增加到4种变异类型
                        int newOffsetUs;
                        
                        // 获取该任务的最大offset限制
                        int taskMaxOffsetUs = task.MaxOffsetUs;
                        
                        switch (mutationType)
                        {
                            case 0: // 完全随机变异，方案1：直接生成时基对齐的随机值
                                int maxSteps = taskMaxOffsetUs / _timeBaseUs;
                                newOffsetUs = _random.Next(0, maxSteps + 1) * _timeBaseUs;
                                individual.Offsets[task.TaskIndex] = newOffsetUs;
                                break;
                                
                            case 1: // 自适应高斯变异
                                var currentOffsetUs = individual.Offsets[task.TaskIndex];
                                var mutationIntensity = GetAdaptiveMutationIntensity(currentGeneration);
                                // 方案1：直接生成时基对齐的扰动
                                int perturbationRangeUs = (int)(taskMaxOffsetUs * mutationIntensity);
                                int maxPerturbSteps = perturbationRangeUs / _timeBaseUs;
                                int randomSteps = _random.Next(-maxPerturbSteps, maxPerturbSteps + 1);
                                int deltaUs = randomSteps * _timeBaseUs;
                                newOffsetUs = Math.Max(0, Math.Min(taskMaxOffsetUs, currentOffsetUs + deltaUs));
                                individual.Offsets[task.TaskIndex] = TaskUtility.EnsureTimeBaseMultiple(newOffsetUs, _timeBaseUs);
                                break;
                                
                            case 2: // 边界变异（设置为0或最大值）
                                newOffsetUs = _random.NextDouble() < 0.5 ? 0 : taskMaxOffsetUs;
                                individual.Offsets[task.TaskIndex] = TaskUtility.EnsureTimeBaseMultiple(newOffsetUs, _timeBaseUs);
                                break;
                                
                            case 3: // 启发式变异 - 寻找负载最低点
                                if (_parameters.UseHeuristicMutation)
                                {
                                    newOffsetUs = FindLowLoadOffsetUs(task, tasks, individual.Offsets);
                                }
                                else
                                {
                                    // 如果未启用启发式变异，回退到随机变异（已经时基对齐）
                                    int fallbackMaxSteps = taskMaxOffsetUs / _timeBaseUs;
                                    newOffsetUs = _random.Next(0, fallbackMaxSteps + 1) * _timeBaseUs;
                                }
                                individual.Offsets[task.TaskIndex] = newOffsetUs;
                                break;
                        }
                        
                        mutated = true;
                    }
                }
                
                if (mutated)
                {
                    individual.IsEvaluated = false;
                    individual.WasMutated = true;
                }
            }
            
            return offspring;
        }
        
        /// <summary>
        /// 计算自适应变异强度
        /// </summary>
        private double GetAdaptiveMutationIntensity(int currentGeneration)
        {
            if (!_parameters.UseAdaptiveMutation)
            {
                return 0.1; // 固定10%强度
            }
            
            // 线性递减：从初始强度逐渐减小到最终强度
            double progress = (double)currentGeneration / _parameters.MaxGenerations;
            double intensity = _parameters.InitialMutationIntensity * (1 - progress) + 
                              _parameters.FinalMutationIntensity * progress;
            
            return Math.Max(_parameters.FinalMutationIntensity, Math.Min(_parameters.InitialMutationIntensity, intensity));
        }

        /// <summary>
        /// 阻塞修复变异：识别并修复个体中的严重阻塞问题（微秒版本）
        /// </summary>
        /// <param name="individual">待修复的个体</param>
        /// <param name="tasks">任务列表</param>
        /// <returns>是否执行了修复</returns>
        private bool TryBlockingRepairMutationUs(GAIndividual individual, List<TaskItem> tasks)
        {
            // 1. 应用当前Offset并分析阻塞情况
            var tasksCopy = CreateTasksCopy(tasks);
            ApplyOffsetsToTasks(tasksCopy, individual.Offsets);
            var analysis = _analyzer.AnalyzeSchedulability(tasksCopy);
            if (analysis.Statistics != null && analysis.Statistics.DeadlineMissCount > 0)
            {
                Console.WriteLine($"[GA-Repair] 修复后评估: 成本={analysis.CostValue:F6}, Miss={analysis.Statistics.DeadlineMissCount}");
            }
            
            // 2. 找出被严重阻塞的任务和造成阻塞的任务
            var blockingProblems = new List<(TaskItem blockedTask, List<TaskItem> blockingSources)>();
            
            foreach (var taskResult in analysis.TaskResults)
            {
                if (taskResult.BlockingTimeUs > 0)
                {
                    var blockedTask = tasksCopy.FirstOrDefault(t => t.TaskIndex == taskResult.TaskIndex);
                    if (blockedTask != null)
                    {
                        // 找出造成阻塞的不可抢占任务
                        var blockingSources = tasksCopy.Where(t => 
                            t.NonPreemptive && 
                            !t.ManualInput &&
                            (t.Priority ?? 0) < (blockedTask.Priority ?? 0)).ToList();
                        
                        if (blockingSources.Any())
                        {
                            blockingProblems.Add((blockedTask, blockingSources));
                        }
                    }
                }
            }
            
            // 3. 如果没有阻塞问题，不需要修复
            if (!blockingProblems.Any()) return false;
            
            // 4. 选择最严重的阻塞问题进行修复
            var worstProblem = blockingProblems.OrderByDescending(p => 
                analysis.TaskResults.First(r => r.TaskIndex == p.blockedTask.TaskIndex).BlockingTimeUs).First();
            
            // 5. 尝试移动造成阻塞的不可抢占任务
            var blockingTask = worstProblem.blockingSources[_random.Next(worstProblem.blockingSources.Count)];
            
            // 6. 为阻塞任务寻找新的、冲突较少的位置
            int currentOffsetUs = individual.Offsets[blockingTask.TaskIndex];
            int periodUs = blockingTask.PeriodUs.Value;
            int bestNewOffsetUs = currentOffsetUs;
            double minTotalBlocking = analysis.TaskResults.Sum(r => r.BlockingTimeUs);

            // 核心改进：自适应采样次数
            int sampleCount;
            int possiblePositions = periodUs / _timeBaseUs;

            if (possiblePositions <= 30) 
            {
                // 如果可能的位置不多，直接全部遍历，这是最精确的
                sampleCount = possiblePositions;
            }
            else
            {
                // 如果位置很多，进行智能采样
                // 采样数与周期的平方根成正比，并设置上下限
                sampleCount = (int)Math.Max(20, Math.Min(100, (int)Math.Sqrt(possiblePositions) * 5));
            }

            // 在一个周期内进行采样搜索
            for (int i = 0; i < sampleCount; i++)
            {
                int candidateOffsetUs;
                if (possiblePositions <= 30)
                {
                    // 遍历模式：按顺序生成所有可能位置
                    candidateOffsetUs = i * _timeBaseUs;
                }
                else
                {
                    // 增强改进：负载感知采样模式
                    candidateOffsetUs = SampleLowLoadPositionUs(blockingTask, periodUs, i, sampleCount);
                }
                
                // 确保时基对齐并限制在一个周期内
                candidateOffsetUs = TaskUtility.EnsureTimeBaseMultiple(candidateOffsetUs, _timeBaseUs);
                candidateOffsetUs = Math.Min(candidateOffsetUs, periodUs);

                // 临时应用新offset并评估冲突
                var tempOffsets = new Dictionary<int, int>(individual.Offsets);
                tempOffsets[blockingTask.TaskIndex] = candidateOffsetUs;
                
                var tempTasks = CreateTasksCopy(tasks);
                ApplyOffsetsToTasks(tempTasks, tempOffsets);
                var tempAnalysis = _analyzer.AnalyzeSchedulability(tempTasks);
                
                // 增强改进：综合评估指标
                double totalBlocking = tempAnalysis.TaskResults.Sum(r => r.BlockingTimeUs);
                double maxResponseRatio = tempAnalysis.TaskResults.Max(r => r.ResponseTimeRatio);
                double combinedScore = totalBlocking + (maxResponseRatio > 1.0 ? 50000 : 0); // 严重惩罚超时
                
                if (combinedScore < minTotalBlocking)
                {
                    minTotalBlocking = combinedScore;
                    bestNewOffsetUs = candidateOffsetUs;
                }
            }
            
            // 7. 如果找到更好的位置，应用修复
            if (Math.Abs(bestNewOffsetUs - currentOffsetUs) > _timeBaseUs / 2)
            {
                individual.Offsets[blockingTask.TaskIndex] = bestNewOffsetUs;
                individual.IsEvaluated = false; // 标记需要重新评估
                Console.WriteLine($"[阻塞修复] 移动任务{blockingTask.TaskName}从{TaskUtility.ConvertToMilliseconds(currentOffsetUs):F1}ms到{TaskUtility.ConvertToMilliseconds(bestNewOffsetUs):F1}ms");
                return true;
            }
            
            return false;
        }

        /// <summary>
        /// 负载感知采样位置生成（微秒版本）
        /// </summary>
        /// <param name="task">目标任务</param>
        /// <param name="periodUs">周期微秒</param>
        /// <param name="sampleIndex">采样索引</param>
        /// <param name="totalSamples">总采样数</param>
        /// <returns>采样位置（微秒）</returns>
        private int SampleLowLoadPositionUs(TaskItem task, int periodUs, int sampleIndex, int totalSamples)
        {
            // 增强策略：组合采样
            // 50%均匀采样 + 30%早期偏向 + 20%随机采样
            
            double samplingStrategy = (double)sampleIndex / totalSamples;
            
            if (samplingStrategy < 0.5)
            {
                // 均匀采样：覆盖整个周期
                int rawPositionUs = (int)((sampleIndex / (double)totalSamples) * periodUs);
                return TaskUtility.EnsureTimeBaseMultiple(rawPositionUs, _timeBaseUs);
            }
            else if (samplingStrategy < 0.8)
            {
                // 早期偏向采样：不可抢占任务往往在周期早期执行更好
                double earlyBias = Math.Pow((sampleIndex - totalSamples * 0.5) / (totalSamples * 0.3), 0.5);
                int rawPositionUs = (int)(earlyBias * periodUs * 0.3); // 集中在前30%区域
                return TaskUtility.EnsureTimeBaseMultiple(rawPositionUs, _timeBaseUs);
            }
            else
            {
                // 随机采样：探索性采样，方案1：直接生成时基对齐的随机值
                int maxSteps = periodUs / _timeBaseUs;
                int randomPositionUs = _random.Next(0, maxSteps + 1) * _timeBaseUs;
                return randomPositionUs; // 已经时基对齐，无需EnsureTimeBaseMultiple
            }
        }

        /// <summary>
        /// 启发式变异 - 基于任务特性的智能offset选择（微秒版本）
        /// </summary>
        private int FindLowLoadOffsetUs(TaskItem targetTask, List<TaskItem> allTasks, Dictionary<int, int> currentOffsets)
        {
            // 获取该任务的最大offset限制
            int taskMaxOffsetUs = targetTask.MaxOffsetUs;
            
            try
            {
                
                // 简化的启发式策略：避免与其他任务的offset过于接近
                var usedOffsets = currentOffsets.Values.Where(o => o >= 0).ToList();
                
                // 生成候选offset位置
                var candidates = new List<int>();
                int stepUs = _timeBaseUs;
                
                for (int candidateOffsetUs = 0; candidateOffsetUs <= taskMaxOffsetUs; candidateOffsetUs += stepUs)
                {
                    // 检查是否与现有offset有足够的间距
                    bool hasGoodSpacing = true;
                    int minSpacingUs = Math.Max(_timeBaseUs * 2, (targetTask.ExecutionTimeUs ?? TaskUtility.MIN_EXECUTION_TIME_US) / 2);
                    
                    foreach (var usedOffset in usedOffsets)
                    {
                        if (Math.Abs(candidateOffsetUs - usedOffset) < minSpacingUs)
                        {
                            hasGoodSpacing = false;
                            break;
                        }
                    }
                    
                    if (hasGoodSpacing)
                    {
                        candidates.Add(candidateOffsetUs);
                    }
                }
                
                // 如果找到了合适的候选位置，随机选择一个（已经时基对齐）
                if (candidates.Count > 0)
                {
                    return candidates[_random.Next(candidates.Count)];
                }
                
                // 如果没有找到间距良好的位置，尝试基于任务周期的策略
                if (targetTask.PeriodUs > 0)
                {
                    double periodFraction = _random.NextDouble() * 0.8 + 0.1; // 0.1-0.9
                    int rawOffsetUs = (int)(targetTask.PeriodUs * periodFraction);
                    int candidateOffsetUs = Math.Min(taskMaxOffsetUs, rawOffsetUs);
                    return TaskUtility.EnsureTimeBaseMultiple(candidateOffsetUs, _timeBaseUs);
                }
                
                // 最后回退到随机选择，方案1：直接生成时基对齐的随机值
                int maxSteps = taskMaxOffsetUs / _timeBaseUs;
                int randomOffsetUs = _random.Next(0, maxSteps + 1) * _timeBaseUs;
                return randomOffsetUs; // 已经时基对齐，无需EnsureTimeBaseMultiple
            }
            catch (Exception)
            {
                // 如果启发式计算失败，回退到随机选择
                int maxSteps = taskMaxOffsetUs / _timeBaseUs;
                int fallbackOffsetUs = _random.Next(0, maxSteps + 1) * _timeBaseUs;
                return fallbackOffsetUs; // 已经时基对齐，无需EnsureTimeBaseMultiple
            }
        }
        
        /// <summary>
        /// 环境选择 - 多种策略可配置
        /// </summary>
        private List<GAIndividual> EnvironmentalSelection(List<GAIndividual> population, List<GAIndividual> offspring)
        {
            switch (_parameters.EnvironmentalSelectionMethod)
            {
                case EnvironmentalSelectionType.PlusSelection:
                    return EnvironmentalSelection_Plus(population, offspring);
                    
                case EnvironmentalSelectionType.TournamentReplacement:
                    return EnvironmentalSelection_Tournament(population, offspring);
                    
                case EnvironmentalSelectionType.ElitistWithEnhanced:
                default:
                    return EnvironmentalSelection_ElitistWithEnhanced(population, offspring);
            }
        }

        /// <summary>
        /// (μ+λ)选择 - 精英主义的自然延伸
        /// </summary>
        private List<GAIndividual> EnvironmentalSelection_Plus(List<GAIndividual> population, List<GAIndividual> offspring)
        {
            // 1. 合并父代和子代
            var combinedPool = population.Concat(offspring).ToList();

            // 2. 按成本从低到高排序
            var sortedPool = combinedPool.OrderBy(ind => ind.Cost).ToList();

            // 3. 直接截取前 PopulationSize 个最优秀的个体作为下一代
            var newPopulation = sortedPool.Take(_parameters.PopulationSize).Select(CopyIndividual).ToList();

            return newPopulation;
        }

        /// <summary>
        /// 锦标赛替换 - 平衡的选择压力
        /// </summary>
        private List<GAIndividual> EnvironmentalSelection_Tournament(List<GAIndividual> population, List<GAIndividual> offspring)
        {
            var combinedPool = population.Concat(offspring).ToList();
            var sortedPool = combinedPool.OrderBy(ind => ind.Cost).ToList();

            // 1. 保留精英个体
            var eliteCount = (int)(_parameters.PopulationSize * _parameters.ElitismRate);
            var newPopulation = sortedPool.Take(eliteCount).Select(CopyIndividual).ToList();

            var remainingPool = sortedPool.Skip(eliteCount).ToList();

            // 2. 通过锦标赛选择填充剩余名额
            while (newPopulation.Count < _parameters.PopulationSize && remainingPool.Count > 0)
            {
                var tournamentGroup = new List<GAIndividual>();
                for (int i = 0; i < _parameters.TournamentSize && remainingPool.Count > 0; i++)
                {
                    int randomIndex = _random.Next(remainingPool.Count);
                    tournamentGroup.Add(remainingPool[randomIndex]);
                    remainingPool.RemoveAt(randomIndex); // 避免重复选择
                }

                if (tournamentGroup.Any())
                {
                    var winner = tournamentGroup.OrderBy(ind => ind.Cost).First();
                    newPopulation.Add(CopyIndividual(winner));
                }
            }
            return newPopulation;
        }

        /// <summary>
        /// 精英主义 + 增强选择策略（原实现的改进版）
        /// </summary>
        private List<GAIndividual> EnvironmentalSelection_ElitistWithEnhanced(List<GAIndividual> population, List<GAIndividual> offspring)
        {
            // 合并父代和子代
            var combined = new List<GAIndividual>();
            combined.AddRange(population);
            combined.AddRange(offspring);
            
            // 按成本排序
            var sorted = combined.OrderBy(ind => ind.Cost).ToList();
            
            // 选择精英个体
            var eliteCount = (int)(_parameters.PopulationSize * _parameters.ElitismRate);
            var newPopulation = sorted.Take(eliteCount).Select(CopyIndividual).ToList();
            
            // 对剩余个体使用增强选择策略
            var remaining = sorted.Skip(eliteCount).ToList();
            int remainingSlots = _parameters.PopulationSize - newPopulation.Count;
            
            if (remaining.Count > 0 && remainingSlots > 0)
            {
                // 根据参数选择不同的选择策略
                var selectedRemaining = SelectRemainingIndividuals(remaining, remainingSlots);
                newPopulation.AddRange(selectedRemaining.Select(CopyIndividual));
            }
            
            return newPopulation;
        }

        /// <summary>
        /// 选择剩余个体 - 使用多种策略而非纯随机
        /// </summary>
        private List<GAIndividual> SelectRemainingIndividuals(List<GAIndividual> candidates, int count)
        {
            if (candidates.Count <= count)
            {
                return candidates;
            }
            
            var selected = new List<GAIndividual>();
            
            switch (_parameters.NonEliteSelectionMethod)
            {
                case NonEliteSelectionType.Tournament:
                    // 对非精英个体也使用锦标赛选择
                    for (int i = 0; i < count; i++)
                    {
                        var tournamentSize = Math.Min(_parameters.NonEliteTournamentSize, candidates.Count);
                        var tournament = new List<GAIndividual>();
                        
                        for (int j = 0; j < tournamentSize; j++)
                        {
                            int randomIndex = _random.Next(candidates.Count);
                            tournament.Add(candidates[randomIndex]);
                        }
                        
                        var winner = tournament.OrderBy(ind => ind.Cost).First();
                        selected.Add(winner);
                    }
                    break;
                    
                case NonEliteSelectionType.LinearRanking:
                    // 线性排名选择：排名越靠前，被选中概率越高
                    selected.AddRange(LinearRankingSelection(candidates, count));
                    break;
                    
                case NonEliteSelectionType.RouletteWheel:
                    // 适应度比例选择
                    selected.AddRange(FitnessProportionateSelection(candidates, count));
                    break;
                    
                case NonEliteSelectionType.Random:
                default:
                    // 纯随机选择（原策略）
                    var shuffled = candidates.OrderBy(x => _random.Next()).ToList();
                    selected.AddRange(shuffled.Take(count));
                    break;
            }
            
            return selected;
        }

        /// <summary>
        /// 线性排名选择
        /// </summary>
        private List<GAIndividual> LinearRankingSelection(List<GAIndividual> candidates, int count)
        {
            var selected = new List<GAIndividual>();
            var sorted = candidates.OrderBy(ind => ind.Cost).ToList();
            
            // 计算线性排名权重（最好的个体权重最高）
            var weights = new List<double>();
            double totalWeight = 0;
            
            for (int i = 0; i < sorted.Count; i++)
            {
                // 线性递减权重：最好的个体(i=0)权重最高
                double weight = sorted.Count - i;
                weights.Add(weight);
                totalWeight += weight;
            }
            
            // 基于权重进行轮盘赌选择
            for (int i = 0; i < count; i++)
            {
                double randomValue = _random.NextDouble() * totalWeight;
                double cumulativeWeight = 0;
                
                for (int j = 0; j < sorted.Count; j++)
                {
                    cumulativeWeight += weights[j];
                    if (randomValue <= cumulativeWeight)
                    {
                        selected.Add(sorted[j]);
                        break;
                    }
                }
            }
            
            return selected;
        }

        /// <summary>
        /// 适应度比例选择
        /// </summary>
        private List<GAIndividual> FitnessProportionateSelection(List<GAIndividual> candidates, int count)
        {
            var selected = new List<GAIndividual>();
            
            // 计算适应度（成本越低，适应度越高）
            var maxCost = candidates.Max(ind => ind.Cost);
            var minCost = candidates.Min(ind => ind.Cost);
            var costRange = maxCost - minCost;
            
            var fitnessValues = new List<double>();
            double totalFitness = 0;
            
            foreach (var individual in candidates)
            {
                // 适应度 = (maxCost - currentCost) + small_value
                double fitness = costRange > 0 ? (maxCost - individual.Cost) + 0.001 : 1.0;
                fitnessValues.Add(fitness);
                totalFitness += fitness;
            }
            
            // 基于适应度进行轮盘赌选择
            for (int i = 0; i < count; i++)
            {
                double randomValue = _random.NextDouble() * totalFitness;
                double cumulativeFitness = 0;
                
                for (int j = 0; j < candidates.Count; j++)
                {
                    cumulativeFitness += fitnessValues[j];
                    if (randomValue <= cumulativeFitness)
                    {
                        selected.Add(candidates[j]);
                        break;
                    }
                }
            }
            
            return selected;
        }
        
        /// <summary>
        /// 检查种群是否收敛
        /// </summary>
        private bool CheckConvergence(List<GAIndividual> population)
        {
            if (!_parameters.UseConvergenceCheck)
                return false;
                
            var costs = population.Select(ind => ind.Cost).ToList();
            var average = costs.Average();
            var variance = costs.Sum(cost => Math.Pow(cost - average, 2)) / costs.Count;
            
            return variance < _parameters.ConvergenceThreshold;
        }
        
        /// <summary>
        /// 复制个体
        /// </summary>
        private GAIndividual CopyIndividual(GAIndividual original)
        {
            return new GAIndividual
            {
                Offsets = new Dictionary<int, int>(original.Offsets),
                Cost = original.Cost,
                IsEvaluated = original.IsEvaluated
            };
        }

        /// <summary>
        /// 创建错误结果
        /// </summary>
        private GAOptimizationResult CreateErrorResult(string errorMessage)
        {
            return new GAOptimizationResult
            {
                Success = false,
                ErrorMessage = errorMessage
            };
        }
        
        /// <summary>
        /// 创建任务列表的副本
        /// </summary>
        private List<TaskItem> CreateTasksCopy(List<TaskItem> originalTasks)
        {
            return TaskUtility.CloneTasks(originalTasks);
        }
        
        /// <summary>
        /// 将Offset应用到任务列表
        /// </summary>
        private void ApplyOffsetsToTasks(List<TaskItem> tasks, Dictionary<int, int> offsets)
        {
            foreach (var task in tasks)
            {
                // 跳过手动输入的任务，不覆盖其Offset值
                if (task.ManualInput)
                {
                    continue;
                }
                
                if (offsets.ContainsKey(task.TaskIndex))
                {
                    task.OffsetUs = offsets[task.TaskIndex];
                }
            }
        }
        
        #endregion
    }
    
    #region GA相关数据结构
    
    /// <summary>
    /// GA优化参数
    /// </summary>
    public class GAOptimizationParameters
    {
        /// <summary>种群大小</summary>
        public int PopulationSize { get; set; } = 100;
        
        /// <summary>最大进化代数</summary>
        public int MaxGenerations { get; set; } = 500;//1000;
        
        /// <summary>交叉率</summary>
        public double CrossoverRate { get; set; } = 0.8;
        
        /// <summary>变异率（基因级）</summary>
        public double MutationRate { get; set; } = 0.02;  // 降低基因级变异率
        
        /// <summary>精英主义比例</summary>
        public double ElitismRate { get; set; } = 0.1;
        
        /// <summary>选择操作大小</summary>
        public int SelectionSize { get; set; } = 80;
        
        /// <summary>是否使用早停</summary>
        public bool UseEarlyStopping { get; set; } = false;
        
        /// <summary>无改进代数阈值</summary>
        public int NoImprovementThreshold { get; set; } = 50;
        
        /// <summary>是否使用收敛检查</summary>
        public bool UseConvergenceCheck { get; set; } = true;
        
        /// <summary>收敛阈值</summary>
        public double ConvergenceThreshold { get; set; } = 0.0001;
        
        /// <summary>随机种子</summary>
        public int RandomSeed { get; set; } = Environment.TickCount;
        
        // === 新增改进参数 ===
        
        /// <summary>选择机制类型</summary>
        public SelectionType SelectionMethod { get; set; } = SelectionType.Tournament;
        
        /// <summary>锦标赛大小（锦标赛选择时使用）</summary>
        public int TournamentSize { get; set; } = 3;
        
        /// <summary>交叉算子类型</summary>
        public CrossoverType CrossoverMethod { get; set; } = CrossoverType.Uniform;
        
        /// <summary>算术交叉混合比例（算术交叉时使用）</summary>
        public double ArithmeticCrossoverAlpha { get; set; } = 0.5;
        
        /// <summary>是否使用自适应变异</summary>
        public bool UseAdaptiveMutation { get; set; } = true;
        
        /// <summary>初始变异强度系数</summary>
        public double InitialMutationIntensity { get; set; } = 0.2;
        
        /// <summary>最终变异强度系数</summary>
        public double FinalMutationIntensity { get; set; } = 0.02;
        
        // === 新增高级优化参数 ===
        
        /// <summary>边界处理方法</summary>
        public BoundaryHandlingType BoundaryHandlingMethod { get; set; } = BoundaryHandlingType.Reflection;
        
        /// <summary>是否使用启发式种群初始化</summary>
        public bool UseHeuristicSeeding { get; set; } = true;
        
        /// <summary>是否使用启发式变异</summary>
        public bool UseHeuristicMutation { get; set; } = true;
        
        /// <summary>非精英个体选择方法</summary>
        public NonEliteSelectionType NonEliteSelectionMethod { get; set; } = NonEliteSelectionType.Tournament;
        
        /// <summary>非精英锦标赛大小</summary>
        public int NonEliteTournamentSize { get; set; } = 2;
        
        /// <summary>环境选择方法</summary>
        public EnvironmentalSelectionType EnvironmentalSelectionMethod { get; set; } = EnvironmentalSelectionType.PlusSelection;
    }
    
    /// <summary>
    /// 选择机制类型
    /// </summary>
    public enum SelectionType
    {
        RouletteWheel,  // 轮盘赌选择
        Tournament,     // 锦标赛选择
        Rank           // 排序选择
    }
    
    /// <summary>
    /// 交叉算子类型
    /// </summary>
    public enum CrossoverType
    {
        SinglePoint,   // 单点交叉
        Uniform,       // 均匀交叉
        Arithmetic     // 算术交叉
    }

    /// <summary>
    /// 边界处理类型
    /// </summary>
    public enum BoundaryHandlingType
    {
        Clamping,      // 钳位/截断（原策略）
        Reflection,    // 反射边界
        Regeneration,  // 重新生成
        Wrapping       // 环绕边界
    }

    /// <summary>
    /// 非精英个体选择类型
    /// </summary>
    public enum NonEliteSelectionType
    {
        Random,        // 随机选择（原策略）
        Tournament,    // 锦标赛选择
        LinearRanking, // 线性排名选择
        RouletteWheel  // 轮盘赌选择
    }

    /// <summary>
    /// 环境选择方法类型
    /// </summary>
    public enum EnvironmentalSelectionType
    {
        PlusSelection,         // (μ+λ)选择 - 最激进的选择压力
        TournamentReplacement, // 锦标赛替换 - 平衡的选择压力
        ElitistWithEnhanced   // 精英主义+增强策略 - 保守但稳健
    }
    
    /// <summary>
    /// GA个体
    /// </summary>
    public class GAIndividual
    {
        /// <summary>任务Offset字典</summary>
        public Dictionary<int, int> Offsets { get; set; }
        
        /// <summary>适应度（成本值）</summary>
        public double Cost { get; set; }
        
        /// <summary>是否已评估</summary>
        public bool IsEvaluated { get; set; }
        
        /// <summary>是否发生变异</summary>
        public bool WasMutated { get; set; }
    }
    
    /// <summary>
    /// GA内部结果
    /// </summary>
    internal class GAInternalResult
    {
        public bool Success { get; set; }
        public Dictionary<int, int> BestOffsets { get; set; }
        public double BestCost { get; set; }
        public string ErrorMessage { get; set; }
    }
    
    /// <summary>
    /// GA优化结果
    /// </summary>
    public class GAOptimizationResult
    {
        /// <summary>是否成功</summary>
        public bool Success { get; set; }
        
        /// <summary>最佳Offset组合（微秒）</summary>
        public Dictionary<int, int> BestOffsets { get; set; }
        
        /// <summary>最佳成本值</summary>
        public double BestCost { get; set; }
        
        /// <summary>可调度性分析结果</summary>
        public SchedulabilityResult SchedulabilityResult { get; set; }
        
        /// <summary>优化统计信息</summary>
        public GAOptimizationStatistics Statistics { get; set; }
        
        /// <summary>错误信息</summary>
        public string ErrorMessage { get; set; }
    }
    
    /// <summary>
    /// GA优化统计信息
    /// </summary>
    public class GAOptimizationStatistics
    {
        /// <summary>开始时间</summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>优化耗时</summary>
        public TimeSpan OptimizationTime { get; set; }
        
        /// <summary>初始种群大小</summary>
        public int InitialPopulationSize { get; set; }
        
        /// <summary>总进化代数</summary>
        public int TotalGenerations { get; set; }
        
        /// <summary>初始最佳成本</summary>
        public double InitialBestCost { get; set; }
        
        /// <summary>最终最佳成本</summary>
        public double FinalBestCost { get; set; }
        
        /// <summary>改进百分比</summary>
        public double ImprovementPercentage { get; set; }
        
        /// <summary>最佳解发现代数</summary>
        public int BestSolutionGeneration { get; set; }
        
        /// <summary>早停节省的代数</summary>
        public int EarlyStoppingSavings { get; set; }
        
        /// <summary>收敛代数</summary>
        public int? ConvergedAtGeneration { get; set; }
        
        /// <summary>代际统计列表</summary>
        public List<GAGenerationStatistics> GenerationStats { get; set; }
    }
    
    /// <summary>
    /// GA代际统计信息
    /// </summary>
    public class GAGenerationStatistics
    {
        /// <summary>代数</summary>
        public int Generation { get; set; }
        
        /// <summary>开始时间</summary>
        public DateTime StartTime { get; set; }
        
        /// <summary>结束时间</summary>
        public DateTime EndTime { get; set; }
        
        /// <summary>最佳成本</summary>
        public double BestCost { get; set; }
        
        /// <summary>平均成本</summary>
        public double AverageCost { get; set; }
        
        /// <summary>最差成本</summary>
        public double WorstCost { get; set; }
        
        /// <summary>选择个体数</summary>
        public int SelectionCount { get; set; }
        
        /// <summary>交叉操作数</summary>
        public int CrossoverCount { get; set; }
        
        /// <summary>变异个体数</summary>
        public int MutationCount { get; set; }
    }
    

    
    #endregion
} 