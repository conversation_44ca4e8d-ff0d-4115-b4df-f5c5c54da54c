using System;
using System.IO;
using System.Text;
using System.Windows.Forms;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Reflection;
using System.Globalization;

namespace CalculateOffset
{
    /// <summary>
    /// 数据IO管理类，负责导入导出和自动保存
    /// </summary>
    public class DataIO
    {
        // 配置文件路径
        private string _configFilePath;
        
        // 获取AppConfig的委托，确保总是获取最新的配置
        private Func<AppConfig> _getAppConfig;
        
        // 上次保存时间
        private DateTime _lastSaveTime = DateTime.MinValue;
        
        // 保存延迟(毫秒)
        private const int SAVE_DELAY = 1000;
        
        // 是否有待保存的更改
        private bool _hasPendingChanges = false;
        
        // 保存锁对象
        private readonly object _saveLock = new object();
        
        // 导入配置事件
        public event Action<AppConfig> ConfigImported;

        // 构造函数
        public DataIO(Func<AppConfig> getAppConfig)
        {
            _getAppConfig = getAppConfig;
            
            // 获取程序所在目录，并设置配置文件路径
            string exePath = Application.ExecutablePath;
            string exeDirectory = Path.GetDirectoryName(exePath);
            string exeName = Path.GetFileNameWithoutExtension(exePath);
            _configFilePath = Path.Combine(exeDirectory, $"{exeName}.json");
        }

        /// <summary>
        /// 导出配置到指定文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功</returns>
        public bool ExportConfig(string filePath)
        {
            try
            {
                string jsonContent = SerializeConfig();
                File.WriteAllText(filePath, jsonContent, Encoding.UTF8);
                
                // 更新最后保存路径
                AppConfig currentConfig = _getAppConfig();
                if (currentConfig != null)
                {
                    currentConfig.MainConfig.LastSavePath = filePath;
                }
                
                // 自动保存当前配置
                SaveConfigImmediately();
                
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出配置失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 导入配置从指定文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>导入的配置对象</returns>
        public AppConfig ImportConfig(string filePath)
        {
            try
            {
                string jsonContent = File.ReadAllText(filePath, Encoding.UTF8);
                AppConfig importedConfig = DeserializeConfig(jsonContent);
                
                if (importedConfig != null)
                {
                    // 更新最后打开路径
                    importedConfig.MainConfig.LastOpenPath = filePath;
                    
                    // 触发配置导入事件
                    ConfigImported?.Invoke(importedConfig);
                    
                    // 自动保存当前配置
                    SaveConfigImmediately();
                }
                
                return importedConfig;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导入配置失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }
        }

        /// <summary>
        /// 自动保存配置到程序目录
        /// </summary>
        public void AutoSaveConfig()
        {
            try
            {
                // 标记有待保存的更改
                _hasPendingChanges = true;
                
                // 如果距离上次保存时间过短，则延迟保存
                if ((DateTime.Now - _lastSaveTime).TotalMilliseconds < SAVE_DELAY)
                {
                    Task.Run(async () =>
                    {
                        await Task.Delay(SAVE_DELAY);
                        SaveConfigIfNeeded();
                    });
                }
                else
                {
                    SaveConfigIfNeeded();
                }
            }
            catch (Exception ex)
            {
                // 忽略自动保存准备失败的异常
            }
        }
        
        /// <summary>
        /// 如果有需要，则保存配置
        /// </summary>
        private void SaveConfigIfNeeded()
        {
            if (!_hasPendingChanges) return;
            
            lock (_saveLock)
            {
                if (!_hasPendingChanges) return;
                
                try
                {
                    string jsonContent = SerializeConfig();

                    // 检查文件是否存在，如果存在则设置文件的属性为隐藏
                    if (File.Exists(_configFilePath))
                    {
                        File.SetAttributes(_configFilePath, FileAttributes.Hidden);
                    }

                    // 使用文件流的方式写入，截断文件内容
                    using (FileStream fs = new FileStream(_configFilePath, FileMode.OpenOrCreate, FileAccess.Write))
                    {
                        fs.SetLength(0); // 清空文件内容，避免追加
                        using (StreamWriter sw = new StreamWriter(fs, Encoding.UTF8))
                        {
                            sw.Write(jsonContent);
                        }
                    }
                    
                    _lastSaveTime = DateTime.Now;
                    _hasPendingChanges = false;
                }
                catch (Exception ex)
                {
                    // 忽略自动保存失败的异常
                }
            }
        }
        
        /// <summary>
        /// 立即保存配置，不延迟
        /// </summary>
        public void SaveConfigImmediately()
        {
            try
            {
                lock (_saveLock)
                {
                    string jsonContent = SerializeConfig();

                    // 检查文件是否存在，如果存在则设置文件的属性为隐藏
                    if (File.Exists(_configFilePath))
                    {
                        File.SetAttributes(_configFilePath, FileAttributes.Hidden);
                    }

                    // 使用文件流的方式写入，截断文件内容
                    using (FileStream fs = new FileStream(_configFilePath, FileMode.OpenOrCreate, FileAccess.Write))
                    {
                        fs.SetLength(0); // 清空文件内容，避免追加
                        using (StreamWriter sw = new StreamWriter(fs, Encoding.UTF8))
                        {
                            sw.Write(jsonContent);
                        }
                    }
                    
                    _lastSaveTime = DateTime.Now;
                    _hasPendingChanges = false;
                }
            }
            catch (Exception ex)
            {
                // 忽略立即保存失败的异常
            }
        }

        /// <summary>
        /// 加载自动保存的配置
        /// </summary>
        /// <returns>加载的配置对象，如果失败则返回null</returns>
        public AppConfig LoadAutoSavedConfig()
        {
            try
            {
                if (File.Exists(_configFilePath))
                {
                    string jsonContent = File.ReadAllText(_configFilePath, Encoding.UTF8);
                    var config = DeserializeConfig(jsonContent);
                    
                    // 通知配置已导入
                    if (config != null)
                    {
                        ConfigImported?.Invoke(config);
                    }
                    
                    return config;
                }
                else
                {
                    // 配置文件不存在，将使用默认配置
                }
            }
            catch (Exception ex)
            {
                // 忽略加载配置失败的异常
            }
            
            return null;
        }

        /// <summary>
        /// 序列化配置对象为JSON字符串
        /// </summary>
        private string SerializeConfig()
        {
            // 获取最新的AppConfig
            AppConfig currentConfig = _getAppConfig();
            if (currentConfig == null)
            {
                throw new InvalidOperationException("无法获取有效的AppConfig对象");
            }
            
            // 使用自定义JSON序列化
            return SimpleJsonSerializer.Serialize(currentConfig);
        }

        /// <summary>
        /// 反序列化JSON字符串为配置对象
        /// </summary>
        private AppConfig DeserializeConfig(string jsonContent)
        {
            // 使用自定义JSON反序列化
            return SimpleJsonSerializer.Deserialize<AppConfig>(jsonContent);
        }
    }

    /// <summary>
    /// 简单JSON序列化/反序列化类，不依赖外部库
    /// </summary>
    public static class SimpleJsonSerializer
    {
        /// <summary>
        /// 序列化对象为JSON字符串
        /// </summary>
        public static string Serialize(object obj)
        {
            if (obj == null) return "null";

            StringBuilder sb = new StringBuilder();
            SerializeValue(obj, sb, 0);
            return sb.ToString();
        }

        /// <summary>
        /// 反序列化JSON字符串为指定类型的对象
        /// </summary>
        public static T Deserialize<T>(string json) where T : new()
        {
            if (string.IsNullOrEmpty(json)) return default(T);

            try
            {
                // 尝试解析JSON
                Dictionary<string, object> data = ParseJson(json);
                return MapToDictionary<T>(data);
            }
            catch (Exception ex)
            {
                // 忽略JSON反序列化失败的异常
                return default(T);
            }
        }

        /// <summary>
        /// 序列化值到StringBuilder
        /// </summary>
        private static void SerializeValue(object value, StringBuilder sb, int indent)
        {
            if (value == null)
            {
                sb.Append("null");
                return;
            }

            Type type = value.GetType();

            if (type == typeof(string))
            {
                SerializeString((string)value, sb);
            }
            else if (type == typeof(int) || type == typeof(int?) ||
                     type == typeof(long) || type == typeof(long?) ||
                     type == typeof(double) || type == typeof(double?) ||
                     type == typeof(float) || type == typeof(float?) ||
                     type == typeof(decimal) || type == typeof(decimal?))
            {
                sb.Append(Convert.ToString(value, CultureInfo.InvariantCulture));
            }
            else if (type == typeof(bool) || type == typeof(bool?))
            {
                sb.Append(value.ToString().ToLower());
            }
            else if (type.IsEnum)
            {
                SerializeString(value.ToString(), sb);
            }
            else if (typeof(System.Collections.IList).IsAssignableFrom(type))
            {
                SerializeList((System.Collections.IList)value, sb, indent);
            }
            else if (typeof(System.Collections.IDictionary).IsAssignableFrom(type))
            {
                SerializeDictionary((System.Collections.IDictionary)value, sb, indent);
            }
            else
            {
                SerializeObject(value, sb, indent);
            }
        }

        /// <summary>
        /// 序列化字符串，处理转义字符
        /// </summary>
        private static void SerializeString(string value, StringBuilder sb)
        {
            sb.Append('"');

            foreach (var c in value)
            {
                switch (c)
                {
                    case '"': sb.Append("\\\""); break;
                    case '\\': sb.Append("\\\\"); break;
                    case '\b': sb.Append("\\b"); break;
                    case '\f': sb.Append("\\f"); break;
                    case '\n': sb.Append("\\n"); break;
                    case '\r': sb.Append("\\r"); break;
                    case '\t': sb.Append("\\t"); break;
                    default:
                        if (c < ' ')
                        {
                            sb.AppendFormat("\\u{0:X4}", (int)c);
                        }
                        else
                        {
                            sb.Append(c);
                        }
                        break;
                }
            }

            sb.Append('"');
        }

        /// <summary>
        /// 序列化列表
        /// </summary>
        private static void SerializeList(System.Collections.IList list, StringBuilder sb, int indent)
        {
            sb.Append('[');

            bool first = true;
            foreach (var item in list)
            {
                if (!first) sb.Append(',');
                if (indent > 0)
                {
                    sb.AppendLine();
                    AddIndent(sb, indent + 1);
                }
                SerializeValue(item, sb, indent + 1);
                first = false;
            }

            if (list.Count > 0 && indent > 0)
            {
                sb.AppendLine();
                AddIndent(sb, indent);
            }
            sb.Append(']');
        }

        /// <summary>
        /// 序列化字典
        /// </summary>
        private static void SerializeDictionary(System.Collections.IDictionary dict, StringBuilder sb, int indent)
        {
            sb.Append('{');

            bool first = true;
            foreach (System.Collections.DictionaryEntry kvp in dict)
            {
                if (!first) sb.Append(',');
                if (indent > 0)
                {
                    sb.AppendLine();
                    AddIndent(sb, indent + 1);
                }
                
                SerializeString(kvp.Key.ToString(), sb);
                sb.Append(':');
                if (indent > 0) sb.Append(' ');
                SerializeValue(kvp.Value, sb, indent + 1);
                
                first = false;
            }

            if (dict.Count > 0 && indent > 0)
            {
                sb.AppendLine();
                AddIndent(sb, indent);
            }
            sb.Append('}');
        }

        /// <summary>
        /// 序列化对象
        /// </summary>
        private static void SerializeObject(object obj, StringBuilder sb, int indent)
        {
            sb.Append('{');

            Type type = obj.GetType();
            PropertyInfo[] properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);

            bool first = true;
            foreach (PropertyInfo prop in properties)
            {
                // 跳过不可读的属性
                if (!prop.CanRead) continue;

                // 获取属性值
                object value = prop.GetValue(obj);
                if (value == null) continue; // 跳过null值

                if (!first) sb.Append(',');
                if (indent > 0)
                {
                    sb.AppendLine();
                    AddIndent(sb, indent + 1);
                }
                
                SerializeString(prop.Name, sb);
                sb.Append(':');
                if (indent > 0) sb.Append(' ');
                SerializeValue(value, sb, indent + 1);
                
                first = false;
            }

            if (properties.Length > 0 && indent > 0 && !first)
            {
                sb.AppendLine();
                AddIndent(sb, indent);
            }
            sb.Append('}');
        }

        /// <summary>
        /// 添加缩进
        /// </summary>
        private static void AddIndent(StringBuilder sb, int indent)
        {
            for (int i = 0; i < indent * 2; i++)
            {
                sb.Append(' ');
            }
        }

        /// <summary>
        /// 解析JSON字符串为字典
        /// </summary>
        private static Dictionary<string, object> ParseJson(string json)
        {
            json = json.Trim();
            if (!json.StartsWith("{") || !json.EndsWith("}"))
            {
                throw new ArgumentException("Invalid JSON format. Must be an object.");
            }

            return ParseObject(ref json);
        }

        /// <summary>
        /// 解析JSON对象
        /// </summary>
        private static Dictionary<string, object> ParseObject(ref string json)
        {
            Dictionary<string, object> dict = new Dictionary<string, object>();
            
            // 去掉开头的{
            json = json.Substring(1).TrimStart();
            
            // 空对象检查
            if (json.StartsWith("}"))
            {
                json = json.Substring(1);
                return dict;
            }

            while (true)
            {
                // 获取属性名
                if (!json.StartsWith("\""))
                {
                    throw new ArgumentException("Expected property name in JSON object");
                }
                
                string key = ParseString(ref json);
                
                // 检查冒号
                json = json.TrimStart();
                if (!json.StartsWith(":"))
                {
                    throw new ArgumentException("Expected : after property name in JSON object");
                }
                
                // 移除冒号
                json = json.Substring(1).TrimStart();
                
                // 解析值
                object value = ParseValue(ref json);
                dict[key] = value;
                
                // 检查是否还有更多属性
                json = json.TrimStart();
                if (json.StartsWith("}"))
                {
                    json = json.Substring(1);
                    break;
                }
                
                if (!json.StartsWith(","))
                {
                    throw new ArgumentException("Expected , between properties in JSON object");
                }
                
                json = json.Substring(1).TrimStart();
            }

            return dict;
        }

        /// <summary>
        /// 解析JSON数组
        /// </summary>
        private static List<object> ParseArray(ref string json)
        {
            List<object> list = new List<object>();
            
            // 去掉开头的[
            json = json.Substring(1).TrimStart();
            
            // 空数组检查
            if (json.StartsWith("]"))
            {
                json = json.Substring(1);
                return list;
            }

            while (true)
            {
                // 解析值
                object value = ParseValue(ref json);
                list.Add(value);
                
                // 检查是否还有更多元素
                json = json.TrimStart();
                if (json.StartsWith("]"))
                {
                    json = json.Substring(1);
                    break;
                }
                
                if (!json.StartsWith(","))
                {
                    throw new ArgumentException("Expected , between elements in JSON array");
                }
                
                json = json.Substring(1).TrimStart();
            }

            return list;
        }

        /// <summary>
        /// 解析JSON值
        /// </summary>
        private static object ParseValue(ref string json)
        {
            json = json.TrimStart();
            
            if (json.StartsWith("{"))
            {
                return ParseObject(ref json);
            }
            else if (json.StartsWith("["))
            {
                return ParseArray(ref json);
            }
            else if (json.StartsWith("\""))
            {
                return ParseString(ref json);
            }
            else if (json.StartsWith("true"))
            {
                json = json.Substring(4);
                return true;
            }
            else if (json.StartsWith("false"))
            {
                json = json.Substring(5);
                return false;
            }
            else if (json.StartsWith("null"))
            {
                json = json.Substring(4);
                return null;
            }
            else
            {
                return ParseNumber(ref json);
            }
        }

        /// <summary>
        /// 解析JSON字符串
        /// </summary>
        private static string ParseString(ref string json)
        {
            // 去掉开头的"
            json = json.Substring(1);
            
            StringBuilder sb = new StringBuilder();
            bool escape = false;
            
            while (true)
            {
                if (json.Length == 0)
                {
                    throw new ArgumentException("Unterminated string in JSON");
                }
                
                char c = json[0];
                json = json.Substring(1);
                
                if (escape)
                {
                    switch (c)
                    {
                        case '"': sb.Append('"'); break;
                        case '\\': sb.Append('\\'); break;
                        case '/': sb.Append('/'); break;
                        case 'b': sb.Append('\b'); break;
                        case 'f': sb.Append('\f'); break;
                        case 'n': sb.Append('\n'); break;
                        case 'r': sb.Append('\r'); break;
                        case 't': sb.Append('\t'); break;
                        case 'u':
                            if (json.Length < 4)
                            {
                                throw new ArgumentException("Invalid Unicode escape sequence in JSON");
                            }
                            
                            string hex = json.Substring(0, 4);
                            json = json.Substring(4);
                            
                            int unicode = int.Parse(hex, NumberStyles.HexNumber);
                            sb.Append((char)unicode);
                            break;
                        default:
                            sb.Append(c);
                            break;
                    }
                    
                    escape = false;
                }
                else if (c == '\\')
                {
                    escape = true;
                }
                else if (c == '"')
                {
                    break;
                }
                else
                {
                    sb.Append(c);
                }
            }
            
            return sb.ToString();
        }

        /// <summary>
        /// 解析JSON数字
        /// </summary>
        private static object ParseNumber(ref string json)
        {
            int i = 0;
            bool isNegative = false;
            bool isDecimal = false;
            
            // 处理负号
            if (json[i] == '-')
            {
                isNegative = true;
                i++;
            }
            
            // 读取整数部分
            while (i < json.Length && char.IsDigit(json[i]))
            {
                i++;
            }
            
            // 读取小数部分
            if (i < json.Length && json[i] == '.')
            {
                isDecimal = true;
                i++;
                
                while (i < json.Length && char.IsDigit(json[i]))
                {
                    i++;
                }
            }
            
            // 读取科学计数法部分
            if (i < json.Length && (json[i] == 'e' || json[i] == 'E'))
            {
                isDecimal = true;
                i++;
                
                if (i < json.Length && (json[i] == '+' || json[i] == '-'))
                {
                    i++;
                }
                
                while (i < json.Length && char.IsDigit(json[i]))
                {
                    i++;
                }
            }
            
            string numStr = json.Substring(0, i);
            json = json.Substring(i);
            
            if (isDecimal)
            {
                double result = double.Parse(numStr, CultureInfo.InvariantCulture);
                return result;
            }
            else
            {
                int result = int.Parse(numStr, CultureInfo.InvariantCulture);
                return result;
            }
        }

        /// <summary>
        /// 将字典映射到指定类型的对象
        /// </summary>
        private static T MapToDictionary<T>(Dictionary<string, object> dict) where T : new()
        {
            T obj = new T();
            Type type = typeof(T);
            
            foreach (var kvp in dict)
            {
                PropertyInfo prop = type.GetProperty(kvp.Key, BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                if (prop != null && prop.CanWrite)
                {
                    SetPropertyValue(prop, obj, kvp.Value);
                }
            }
            
            return obj;
        }

        /// <summary>
        /// 设置属性值
        /// </summary>
        private static void SetPropertyValue(PropertyInfo prop, object obj, object value)
        {
            Type propType = prop.PropertyType;
            
            // 处理空值
            if (value == null)
            {
                if (propType.IsValueType && Nullable.GetUnderlyingType(propType) == null)
                {
                    // 不能将null赋值给非可空值类型
                    return;
                }
                
                prop.SetValue(obj, null);
                return;
            }
            
            if (propType.IsGenericType && propType.GetGenericTypeDefinition() == typeof(Nullable<>))
            {
                propType = Nullable.GetUnderlyingType(propType);
            }
            
            if (propType.IsEnum)
            {
                if (value is string)
                {
                    prop.SetValue(obj, Enum.Parse(propType, (string)value, true));
                }
                return;
            }
            
            // 处理基本类型
            if (propType == typeof(string))
            {
                prop.SetValue(obj, value.ToString());
            }
            else if (propType == typeof(int) || propType == typeof(int?))
            {
                if (value is double)
                {
                    prop.SetValue(obj, (int)(double)value);
                }
                else
                {
                    prop.SetValue(obj, Convert.ToInt32(value));
                }
            }
            else if (propType == typeof(long) || propType == typeof(long?))
            {
                if (value is double)
                {
                    prop.SetValue(obj, (long)(double)value);
                }
                else
                {
                    prop.SetValue(obj, Convert.ToInt64(value));
                }
            }
            else if (propType == typeof(double) || propType == typeof(double?))
            {
                prop.SetValue(obj, Convert.ToDouble(value, CultureInfo.InvariantCulture));
            }
            else if (propType == typeof(float) || propType == typeof(float?))
            {
                if (value is double)
                {
                    prop.SetValue(obj, (float)(double)value);
                }
                else
                {
                    prop.SetValue(obj, Convert.ToSingle(value, CultureInfo.InvariantCulture));
                }
            }
            else if (propType == typeof(decimal) || propType == typeof(decimal?))
            {
                if (value is double)
                {
                    prop.SetValue(obj, (decimal)(double)value);
                }
                else
                {
                    prop.SetValue(obj, Convert.ToDecimal(value, CultureInfo.InvariantCulture));
                }
            }
            else if (propType == typeof(bool) || propType == typeof(bool?))
            {
                if (value is bool)
                {
                    prop.SetValue(obj, value);
                }
                else
                {
                    prop.SetValue(obj, Convert.ToBoolean(value));
                }
            }
            // 处理字典类型
            else if (typeof(System.Collections.IDictionary).IsAssignableFrom(propType) && value is Dictionary<string, object>)
            {
                Type keyType = typeof(string);
                Type valueType = typeof(object);
                
                // 获取字典的泛型参数
                if (propType.IsGenericType)
                {
                    Type[] genericArgs = propType.GetGenericArguments();
                    if (genericArgs.Length == 2)
                    {
                        keyType = genericArgs[0];
                        valueType = genericArgs[1];
                    }
                }
                
                // 创建字典实例
                System.Collections.IDictionary dictInstance;
                if (propType.IsInterface || propType.IsAbstract)
                {
                    Type dictType = typeof(Dictionary<,>).MakeGenericType(keyType, valueType);
                    dictInstance = (System.Collections.IDictionary)Activator.CreateInstance(dictType);
                }
                else
                {
                    dictInstance = (System.Collections.IDictionary)Activator.CreateInstance(propType);
                }
                
                // 填充字典
                foreach (var kvp in (Dictionary<string, object>)value)
                {
                    object dictKey = kvp.Key;
                    object dictValue = kvp.Value;
                    
                    // 转换键值类型
                    if (keyType != typeof(string))
                    {
                        dictKey = Convert.ChangeType(kvp.Key, keyType);
                    }
                    
                    if (valueType.IsClass && valueType != typeof(string) && dictValue is Dictionary<string, object>)
                    {
                        // 递归处理复杂对象
                        dictValue = MapDictionaryToType((Dictionary<string, object>)dictValue, valueType);
                    }
                    
                    dictInstance.Add(dictKey, dictValue);
                }
                
                prop.SetValue(obj, dictInstance);
            }
            // 处理列表类型
            else if (typeof(System.Collections.IList).IsAssignableFrom(propType) && value is List<object>)
            {
                Type elementType = typeof(object);
                
                // 获取列表的泛型参数
                if (propType.IsGenericType)
                {
                    Type[] genericArgs = propType.GetGenericArguments();
                    if (genericArgs.Length == 1)
                    {
                        elementType = genericArgs[0];
                    }
                }
                
                // 创建列表实例
                System.Collections.IList listInstance;
                if (propType.IsInterface || propType.IsAbstract)
                {
                    Type listType = typeof(List<>).MakeGenericType(elementType);
                    listInstance = (System.Collections.IList)Activator.CreateInstance(listType);
                }
                else
                {
                    listInstance = (System.Collections.IList)Activator.CreateInstance(propType);
                }
                
                // 填充列表
                foreach (object item in (List<object>)value)
                {
                    if (elementType.IsClass && elementType != typeof(string) && item is Dictionary<string, object>)
                    {
                        // 递归处理复杂对象
                        listInstance.Add(MapDictionaryToType((Dictionary<string, object>)item, elementType));
                    }
                    else
                    {
                        if (item == null)
                        {
                            listInstance.Add(null);
                        }
                        else if (elementType.IsEnum && item is string)
                        {
                            listInstance.Add(Enum.Parse(elementType, (string)item, true));
                        }
                        else
                        {
                            try
                            {
                                listInstance.Add(Convert.ChangeType(item, elementType));
                            }
                            catch
                            {
                                listInstance.Add(item);
                            }
                        }
                    }
                }
                
                prop.SetValue(obj, listInstance);
            }
            // 处理复杂对象
            else if (value is Dictionary<string, object>)
            {
                object nestedObj = MapDictionaryToType((Dictionary<string, object>)value, propType);
                prop.SetValue(obj, nestedObj);
            }
        }

        /// <summary>
        /// 将字典映射到指定类型
        /// </summary>
        private static object MapDictionaryToType(Dictionary<string, object> dict, Type type)
        {
            if (type.IsClass && type != typeof(string))
            {
                object instance = Activator.CreateInstance(type);
                
                foreach (var kvp in dict)
                {
                    PropertyInfo prop = type.GetProperty(kvp.Key, BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                    if (prop != null && prop.CanWrite)
                    {
                        SetPropertyValue(prop, instance, kvp.Value);
                    }
                }
                
                return instance;
            }
            
            return Convert.ChangeType(dict, type);
        }
    }
} 