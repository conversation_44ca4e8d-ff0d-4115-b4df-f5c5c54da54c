using System;
using System.Collections.Generic;
using System.Windows.Forms;
using System.ComponentModel;
using System.Reflection;
using System.Linq;

namespace CalculateOffset
{
    /// <summary>
    /// DataGridView管理类
    /// </summary>
    public class DgvManager
    {
        // DataGridView控件引用
        private DataGridView _dgvTasks;
        
        // 应用程序配置
        private AppConfig _appConfig;
        
        // 数据IO管理器
        private DataIO _dataIO;
        
        // 绑定列表
        private BindingList<TaskItem> _taskList;
        
        // 是否正在更新
        private bool _isUpdating = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dgvTasks">DataGridView控件</param>
        /// <param name="appConfig">应用程序配置</param>
        /// <param name="dataIO">数据IO管理器</param>
        public DgvManager(DataGridView dgvTasks, AppConfig appConfig, DataIO dataIO)
        {
            _dgvTasks = dgvTasks;
            _appConfig = appConfig;
            _dataIO = dataIO;
            
            // 初始化绑定列表
            _taskList = new BindingList<TaskItem>();
            _taskList.ListChanged += TaskList_ListChanged;
            
            // 初始化DataGridView
            InitializeDgv();
            
            // 加载现有数据
            LoadTasks();
        }
        
        /// <summary>
        /// 加载任务数据
        /// </summary>
        private void LoadTasks()
        {
            try
            {
                _isUpdating = true;
                
                // 清空现有列表
                _taskList.Clear();
                
                // 添加现有任务
                if (_appConfig.Tasks != null)
                {
                    foreach (var task in _appConfig.Tasks)
                    {
                        _taskList.Add(task);
                    }
                }
                
                // 确保所有任务都有正确的TaskIndex
                EnsureTaskIndexAssignment();
                
                // 刷新序号
                RefreshRowIndices();
                
                // 更新Offset是否可编辑
                UpdateOffsetEditability();
                
                _isUpdating = false;
            }
            catch (Exception ex)
            {
                _isUpdating = false;
            }
        }

        /// <summary>
        /// 初始化DataGridView
        /// </summary>
        private void InitializeDgv()
        {
            // 启用双缓冲以提高性能
            EnableDoubleBuffering(_dgvTasks);
            
            // 清空所有列
            _dgvTasks.Columns.Clear();
            
            // 设置基本属性
            _dgvTasks.AutoGenerateColumns = false;
            _dgvTasks.AllowUserToAddRows = true;
            _dgvTasks.AllowUserToDeleteRows = false;
            _dgvTasks.MultiSelect = false;
            _dgvTasks.SelectionMode = DataGridViewSelectionMode.CellSelect; // 改为单元格选择模式，支持单独复制单元格内容
            _dgvTasks.RowHeadersVisible = false;
            _dgvTasks.AllowUserToResizeRows = false;
            _dgvTasks.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
            _dgvTasks.EditMode = DataGridViewEditMode.EditOnEnter;
            
            // 添加序号列
            DataGridViewTextBoxColumn colIndex = new DataGridViewTextBoxColumn
            {
                HeaderText = "序号",
                Name = "colIndex",
                ReadOnly = true,
                SortMode = DataGridViewColumnSortMode.NotSortable,
                Width = GetColumnWidth("colIndex", 50),
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter },
                HeaderCell = { Style = { Alignment = DataGridViewContentAlignment.MiddleCenter } }
            };
            _dgvTasks.Columns.Add(colIndex);
            
            // 添加使能列
            DataGridViewCheckBoxColumn colEnabled = new DataGridViewCheckBoxColumn
            {
                HeaderText = "使能",
                Name = "colEnabled",
                DataPropertyName = "Enabled",
                SortMode = DataGridViewColumnSortMode.NotSortable,
                Width = GetColumnWidth("colEnabled", 50),
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter },
                HeaderCell = { Style = { Alignment = DataGridViewContentAlignment.MiddleCenter } }
            };
            _dgvTasks.Columns.Add(colEnabled);
            
            // 添加任务名列
            DataGridViewTextBoxColumn colTaskName = new DataGridViewTextBoxColumn
            {
                HeaderText = "任务名称",
                Name = "colTaskName",
                DataPropertyName = "TaskName",
                SortMode = DataGridViewColumnSortMode.NotSortable,
                Width = GetColumnWidth("colTaskName", 300),
                // 任务名列不需要居中，保持默认左对齐
                HeaderCell = { Style = { Alignment = DataGridViewContentAlignment.MiddleCenter } }
            };
            _dgvTasks.Columns.Add(colTaskName);
            
            // 添加周期列
            DataGridViewTextBoxColumn colPeriod = new DataGridViewTextBoxColumn
            {
                HeaderText = "周期(ms)",
                Name = "colPeriod",
                DataPropertyName = "PeriodUs", // 使用内部属性名
                SortMode = DataGridViewColumnSortMode.NotSortable,
                Width = GetColumnWidth("colPeriod", 70),
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter },
                HeaderCell = { Style = { Alignment = DataGridViewContentAlignment.MiddleCenter } }
            };
            _dgvTasks.Columns.Add(colPeriod);
            
            // 添加平均执行时间列
			/*
            DataGridViewTextBoxColumn colAvgExecutionTime = new DataGridViewTextBoxColumn
            {
                HeaderText = "平均执行时间(ms)",
                Name = "colAvgExecutionTime",
                DataPropertyName = "AverageExecutionTimeUs", // 使用内部属性名
                SortMode = DataGridViewColumnSortMode.NotSortable,
                Width = GetColumnWidth("colAvgExecutionTime", 120),
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter },
                HeaderCell = { Style = { Alignment = DataGridViewContentAlignment.MiddleCenter } }
            };
            _dgvTasks.Columns.Add(colAvgExecutionTime);
            */
			
            // 添加最大执行时间列
            DataGridViewTextBoxColumn colMaxExecutionTime = new DataGridViewTextBoxColumn
            {
                HeaderText = "最大执行时间(ms)",
                Name = "colMaxExecutionTime",
                DataPropertyName = "ExecutionTimeUs", // 使用内部属性名
                SortMode = DataGridViewColumnSortMode.NotSortable,
                Width = GetColumnWidth("colMaxExecutionTime", 120),
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter },
                HeaderCell = { Style = { Alignment = DataGridViewContentAlignment.MiddleCenter } }
            };
            _dgvTasks.Columns.Add(colMaxExecutionTime);
            
            // 添加优先级列
            DataGridViewTextBoxColumn colPriority = new DataGridViewTextBoxColumn
            {
                HeaderText = "优先级",
                Name = "colPriority",
                DataPropertyName = "Priority",
                SortMode = DataGridViewColumnSortMode.NotSortable,
                Width = GetColumnWidth("colPriority", 70),
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter },
                HeaderCell = { Style = { Alignment = DataGridViewContentAlignment.MiddleCenter } }
            };
            _dgvTasks.Columns.Add(colPriority);
            
            // 添加不可抢占列（移动到优先级之后）
            DataGridViewCheckBoxColumn colNonPreemptive = new DataGridViewCheckBoxColumn
            {
                HeaderText = "不可抢占",
                Name = "colNonPreemptive",
                DataPropertyName = "NonPreemptive",
                SortMode = DataGridViewColumnSortMode.NotSortable,
                Width = GetColumnWidth("colNonPreemptive", 70),
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter },
                HeaderCell = { Style = { Alignment = DataGridViewContentAlignment.MiddleCenter } }
            };
            _dgvTasks.Columns.Add(colNonPreemptive);
            
            // 添加前置任务列
            DataGridViewTextBoxColumn colPrerequisiteTasks = new DataGridViewTextBoxColumn
            {
                HeaderText = "前置任务",
                Name = "colPrerequisiteTasks",
                DataPropertyName = "PrerequisiteTasks",
                SortMode = DataGridViewColumnSortMode.NotSortable,
                Width = GetColumnWidth("colPrerequisiteTasks", 150),
                // 前置任务列保持左对齐，便于阅读任务名
                HeaderCell = { Style = { Alignment = DataGridViewContentAlignment.MiddleCenter } }
            };
            _dgvTasks.Columns.Add(colPrerequisiteTasks);
            
            // 添加Offset列（移动到前置任务后）
            DataGridViewTextBoxColumn colOffset = new DataGridViewTextBoxColumn
            {
                HeaderText = "Offset(ms)",
                Name = "colOffset",
                DataPropertyName = "OffsetUs", // 使用内部属性名
                SortMode = DataGridViewColumnSortMode.NotSortable,
                Width = GetColumnWidth("colOffset", 70),
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter, Format = "F3" },
                HeaderCell = { Style = { Alignment = DataGridViewContentAlignment.MiddleCenter } }
            };
            _dgvTasks.Columns.Add(colOffset);
            
            // 添加手动输入列（移动到Offset后）
            DataGridViewCheckBoxColumn colManualInput = new DataGridViewCheckBoxColumn
            {
                HeaderText = "手动输入",
                Name = "colManualInput",
                DataPropertyName = "ManualInput",
                SortMode = DataGridViewColumnSortMode.NotSortable,
                Width = GetColumnWidth("colManualInput", 70),
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter },
                HeaderCell = { Style = { Alignment = DataGridViewContentAlignment.MiddleCenter } }
            };
            _dgvTasks.Columns.Add(colManualInput);
            
            // 添加清除按钮列
            DataGridViewButtonColumn colClear = new DataGridViewButtonColumn
            {
                HeaderText = "清除",
                Name = "colClear",
                Text = "清除",
                UseColumnTextForButtonValue = true,
                SortMode = DataGridViewColumnSortMode.NotSortable,
                Width = GetColumnWidth("colClear", 60),
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter },
                HeaderCell = { Style = { Alignment = DataGridViewContentAlignment.MiddleCenter } }
            };
            _dgvTasks.Columns.Add(colClear);
            
            // 添加删除按钮列
            DataGridViewButtonColumn colDelete = new DataGridViewButtonColumn
            {
                HeaderText = "删除",
                Name = "colDelete",
                Text = "删除",
                UseColumnTextForButtonValue = true,
                SortMode = DataGridViewColumnSortMode.NotSortable,
                Width = GetColumnWidth("colDelete", 60),
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter },
                HeaderCell = { Style = { Alignment = DataGridViewContentAlignment.MiddleCenter } }
            };
            _dgvTasks.Columns.Add(colDelete);
            
            // 设置数据源
            _dgvTasks.DataSource = _taskList;
            
            // 绑定事件
            _dgvTasks.CellValueChanged += DgvTasks_CellValueChanged;
            _dgvTasks.CellContentClick += DgvTasks_CellContentClick;
            _dgvTasks.CellClick += DgvTasks_CellClick; // 添加CellClick事件处理按钮点击
            _dgvTasks.KeyDown += DgvTasks_KeyDown; // 添加键盘事件处理Ctrl+C复制
            _dgvTasks.ColumnWidthChanged += DgvTasks_ColumnWidthChanged;
            _dgvTasks.RowStateChanged += DgvTasks_RowStateChanged;
            _dgvTasks.DataBindingComplete += DgvTasks_DataBindingComplete;
            _dgvTasks.DefaultValuesNeeded += DgvTasks_DefaultValuesNeeded;
            _dgvTasks.CurrentCellDirtyStateChanged += DgvTasks_CurrentCellDirtyStateChanged;
            _dgvTasks.CellEndEdit += DgvTasks_CellEndEdit;
            _dgvTasks.RowValidated += DgvTasks_RowValidated;
            _dgvTasks.CellFormatting += DgvTasks_CellFormatting;
            _dgvTasks.CellParsing += DgvTasks_CellParsing; // 添加单元格解析事件处理用户输入
            _dgvTasks.RowEnter += DgvTasks_RowEnter; // 重新启用行进入事件
        }
        
        /// <summary>
        /// 为DataGridView启用双缓冲
        /// </summary>
        /// <param name="dgv">要启用双缓冲的DataGridView控件</param>
        private void EnableDoubleBuffering(DataGridView dgv)
        {
            try
            {
                // 通过反射设置DoubleBuffered属性
                if (dgv.GetType().GetProperty("DoubleBuffered", BindingFlags.Instance | BindingFlags.NonPublic) is PropertyInfo pi)
                {
                    pi.SetValue(dgv, true, null);
                }
            }
            catch (Exception ex)
            {
                // Ignore double buffering errors
            }
        }
        
        /// <summary>
        /// 单元格结束编辑事件
        /// </summary>
        private void DgvTasks_CellEndEdit(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                // 避免在更新过程中处理
                if (_isUpdating)
                {
                    return;
                }
                
                // 检查行索引的有效性
                if (e.RowIndex < 0 || e.RowIndex >= _dgvTasks.Rows.Count)
                {
                    return;
                }
                
                // 检查是否是新行
                if (_dgvTasks.Rows[e.RowIndex].IsNewRow)
                {
                    return;
                }
                
                // 确保数据立刻更新到绑定的对象
                _dgvTasks.EndEdit();
                
                // 更新App配置
                UpdateAppConfig();
            }
            catch (Exception ex)
            {
                // Handle error silently
            }
        }
        
        /// <summary>
        /// 行验证事件，当行编辑完成时触发
        /// </summary>
        private void DgvTasks_RowValidated(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (!_isUpdating && e.RowIndex >= 0 && e.RowIndex < _dgvTasks.Rows.Count && !_dgvTasks.Rows[e.RowIndex].IsNewRow)
                {
                    // 更新App配置
                    UpdateAppConfig();
                }
            }
            catch (Exception ex)
            {
                // Handle error silently
            }
        }

        /// <summary>
        /// 单元格脏状态变更事件，用于立即提交复选框的更改
        /// </summary>
        private void DgvTasks_CurrentCellDirtyStateChanged(object sender, EventArgs e)
        {
            try
            {
                // 如果当前单元格是复选框且状态为脏，立即提交值
                if (_dgvTasks.CurrentCell is DataGridViewCheckBoxCell && _dgvTasks.IsCurrentCellDirty)
                {
                    _dgvTasks.CommitEdit(DataGridViewDataErrorContexts.Commit);
                }
            }
            catch (Exception ex)
            {
                // Handle error silently
            }
        }

        /// <summary>
        /// 设置新行的默认值
        /// </summary>
        private void DgvTasks_DefaultValuesNeeded(object sender, DataGridViewRowEventArgs e)
        {
            try
            {
                // 当DataGridView需要新行的默认值时，确保BindingList中有对应的TaskItem
                // 这通常发生在用户开始在新行中输入数据时
                if (e.Row.Index >= _taskList.Count)
                {
                    // 创建新的TaskItem对象
                    var newTask = new TaskItem
                    {
                        TaskIndex = GetNextAvailableTaskIndex(),
                        TaskName = "",
                        PeriodUs = 0,
                        OffsetUs = 0,
                        ExecutionTimeUs = 0,
                        Enabled = true,
                        ManualInput = false,
                        Priority = 0,
                        PrerequisiteTasks = "",
                        NonPreemptive = false,
                        MaxOffsetUs = 0  // 默认值，在计算时会统一设置
                    };
                    
                    // 设置更新标志，避免递归事件
                    _isUpdating = true;
                    try
                    {
                        _taskList.Add(newTask);
                    }
                    finally
                    {
                        _isUpdating = false;
                    }
                }
                
                // 设置默认值到单元格
                SetCellDefaultValue(e.Row, "colManualInput", false);
                SetCellDefaultValue(e.Row, "colNonPreemptive", false);
                SetCellDefaultValue(e.Row, "colOffset", 0);
                SetCellDefaultValue(e.Row, "colEnabled", true);
                SetCellDefaultValue(e.Row, "colPeriod", 0);
                SetCellDefaultValue(e.Row, "colMaxExecutionTime", 0);
                SetCellDefaultValue(e.Row, "colPriority", 0);
            }
            catch (Exception ex)
            {
                // Handle error silently
            }
        }

        /// <summary>
        /// 安全地设置单元格默认值
        /// </summary>
        private void SetCellDefaultValue(DataGridViewRow row, string columnName, object value)
        {
            try
            {
                if (row.Cells.Cast<DataGridViewCell>().Any(c => c.OwningColumn?.Name == columnName))
                {
                    row.Cells[columnName].Value = value;
                }
            }
            catch (Exception ex)
            {
                // Handle error silently
            }
        }

        /// <summary>
        /// 列表变更事件
        /// </summary>
        private void TaskList_ListChanged(object sender, ListChangedEventArgs e)
        {
            try
            {
                if (_isUpdating) 
                {
                    return;
                }
                
                // 特殊处理ItemAdded事件，这通常表示用户在新行中输入了数据
                if (e.ListChangedType == ListChangedType.ItemAdded)
                {
                    // 为新添加的任务分配TaskIndex
                    if (e.NewIndex >= 0 && e.NewIndex < _taskList.Count)
                    {
                        var newTask = _taskList[e.NewIndex];
                        if (newTask.TaskIndex <= 0)
                        {
                            newTask.TaskIndex = GetNextAvailableTaskIndex();
                        }
                    }
                    
                    // 强制DataGridView刷新以确保同步
                    _dgvTasks.Invalidate();
                    _dgvTasks.Refresh();
                    
                    // 刷新序号（新增项目后序号可能需要更新）
                    RefreshRowIndices();
                    
                    // 确保有新行可用
                    Application.DoEvents(); // 让UI处理完成
                    EnsureNewRowAvailable();
                }
                
                // 更新App配置
                UpdateAppConfig();
                
                // 刷新序号
                RefreshRowIndices();
                
                // 更新Offset是否可编辑
                UpdateOffsetEditability();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"列表变更处理错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        /// <summary>
        /// 更新AppConfig中的任务列表
        /// </summary>
        private void UpdateAppConfig()
        {
            try
            {
                if (_isUpdating) return;
                
                // 更新AppConfig中的任务列表
                _appConfig.Tasks.Clear();
                
                foreach (var item in _taskList)
                {
                    if (item != null)
                    {
                        _appConfig.Tasks.Add(item);
                    }
                }
                
                // 触发自动保存
                _dataIO.SaveConfigImmediately();
            }
            catch (Exception ex)
            {
                // Handle error silently
            }
        }

        /// <summary>
        /// 单元格值变更事件
        /// </summary>
        private void DgvTasks_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (_isUpdating) 
                {
                    return;
                }
                
                if (e.RowIndex >= 0 && e.ColumnIndex >= 0 && e.RowIndex < _dgvTasks.Rows.Count && !_dgvTasks.Rows[e.RowIndex].IsNewRow)
                {
                    // 更新Offset是否可编辑
                    UpdateOffsetEditability();
                    
                    // 更新App配置
                    UpdateAppConfig();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"单元格值变更处理错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 单元格内容点击事件（现在不处理按钮点击，避免文本选择时误触发）
        /// </summary>
        private void DgvTasks_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            // 不再在这里处理按钮点击，改用CellClick事件
        }

        /// <summary>
        /// 单元格点击事件（处理按钮点击）
        /// </summary>
        private void DgvTasks_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                // 确保点击的是有效的行，并且不是新行
                if (e.RowIndex < 0 || e.RowIndex >= _dgvTasks.Rows.Count || _dgvTasks.Rows[e.RowIndex].IsNewRow) 
                {
                    return;
                }
                
                if (_dgvTasks.Columns.Count <= e.ColumnIndex)
                    return;

                // 获取列名
                string columnName = _dgvTasks.Columns[e.ColumnIndex].Name;
                
                // 只处理按钮列的点击
                if (columnName == "colClear")
                {
                    ClearRow(e.RowIndex);
                }
                else if (columnName == "colDelete")
                {
                    DeleteRow(e.RowIndex);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"单元格点击处理错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 键盘按下事件（处理Ctrl+C复制和Ctrl+V粘贴）
        /// </summary>
        private void DgvTasks_KeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                // 处理Ctrl+C复制当前单元格内容
                if (e.Control && e.KeyCode == Keys.C)
                {
                    if (_dgvTasks.CurrentCell != null && _dgvTasks.CurrentCell.Value != null)
                    {
                        // 如果当前单元格正在编辑状态，获取编辑控件中的选中文本
                        if (_dgvTasks.IsCurrentCellInEditMode && _dgvTasks.EditingControl is TextBox textBox)
                        {
                            if (!string.IsNullOrEmpty(textBox.SelectedText))
                            {
                                // 复制选中的文本
                                Clipboard.SetText(textBox.SelectedText);
                            }
                            else
                            {
                                // 复制整个单元格内容
                                Clipboard.SetText(textBox.Text);
                            }
                        }
                        else
                        {
                            // 复制当前单元格的值
                            string cellValue = _dgvTasks.CurrentCell.Value.ToString();
                            if (!string.IsNullOrEmpty(cellValue))
                            {
                                Clipboard.SetText(cellValue);
                            }
                        }
                        e.Handled = true; // 阻止默认的复制行为
                    }
                }
                // 处理Ctrl+V粘贴到当前单元格
                else if (e.Control && e.KeyCode == Keys.V)
                {
                    if (_dgvTasks.CurrentCell != null && !_dgvTasks.CurrentCell.ReadOnly)
                    {
                        try
                        {
                            string clipboardText = Clipboard.GetText();
                            if (!string.IsNullOrEmpty(clipboardText))
                            {
                                // 如果当前单元格正在编辑状态，粘贴到编辑控件
                                if (_dgvTasks.IsCurrentCellInEditMode && _dgvTasks.EditingControl is TextBox textBox)
                                {
                                    // 在光标位置插入文本
                                    int selectionStart = textBox.SelectionStart;
                                    textBox.Text = textBox.Text.Insert(selectionStart, clipboardText);
                                    textBox.SelectionStart = selectionStart + clipboardText.Length;
                                }
                                else
                                {
                                    // 直接设置单元格值
                                    _dgvTasks.CurrentCell.Value = clipboardText;
                                }
                                e.Handled = true; // 阻止默认的粘贴行为
                            }
                        }
                        catch (Exception ex)
                        {
                            // Handle error silently
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle error silently
            }
        }

        /// <summary>
        /// 列宽变更事件
        /// </summary>
        private void DgvTasks_ColumnWidthChanged(object sender, DataGridViewColumnEventArgs e)
        {
            try
            {
                // 保存列宽
                _appConfig.MainConfig.ColumnWidths[e.Column.Name] = e.Column.Width;
                
                // 触发自动保存
                _dataIO.SaveConfigImmediately();
            }
            catch (Exception ex)
            {
                // Handle error silently
            }
        }

        /// <summary>
        /// 行状态变更事件
        /// </summary>
        private void DgvTasks_RowStateChanged(object sender, DataGridViewRowStateChangedEventArgs e)
        {
            try
            {
                // 如果是新行，确保它不会被错误地添加到任务列表中
                if (e.Row.IsNewRow && e.StateChanged == DataGridViewElementStates.Selected)
                {
                    UpdateOffsetEditability();
                }
            }
            catch (Exception ex)
            {
                // Handle error silently
            }
        }

        /// <summary>
        /// 数据绑定完成事件
        /// </summary>
        private void DgvTasks_DataBindingComplete(object sender, DataGridViewBindingCompleteEventArgs e)
        {
            try
            {
                // 避免在更新过程中处理
                if (_isUpdating)
                {
                    return;
                }
                
                // 刷新序号
                RefreshRowIndices();
                
                // 更新Offset是否可编辑
                UpdateOffsetEditability();
                
                // 检查数据同步性并确保新行
                int taskCount = _taskList?.Count ?? 0;
                int dgvRowCount = _dgvTasks?.Rows?.Count ?? 0;
                bool hasNewRow = dgvRowCount > 0 && _dgvTasks.Rows[dgvRowCount - 1].IsNewRow;
                int dataRowCount = hasNewRow ? dgvRowCount - 1 : dgvRowCount;
                
                if (taskCount != dataRowCount)
                {
                    // 延迟修复，避免在绑定过程中修改
                    System.Windows.Forms.Timer fixTimer = new System.Windows.Forms.Timer();
                    fixTimer.Interval = 50;
                    fixTimer.Tick += (timerSender, timerE) =>
                    {
                        fixTimer.Stop();
                        fixTimer.Dispose();
                        try
                        {
                            if (!_isUpdating)
                            {
                                EnsureNewRowAvailable();
                                RefreshRowIndices(); // 确保延时修复后序号正确
                            }
                        }
                        catch (Exception timerEx)
                        {
                            // Handle error silently
                        }
                    };
                    fixTimer.Start();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"数据绑定完成处理错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 行进入事件，当用户进入最后一行时自动创建新行
        /// </summary>
        private void DgvTasks_RowEnter(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                // 避免在更新过程中执行
                if (_isUpdating)
                {
                    return;
                }
                
                // 检查行索引有效性
                if (e.RowIndex < 0 || e.RowIndex >= _dgvTasks.Rows.Count)
                {
                    return;
                }
                
                // 首先检查数据同步性
                int taskCount = _taskList?.Count ?? 0;
                int dgvRowCount = _dgvTasks?.Rows?.Count ?? 0;
                bool hasNewRow = dgvRowCount > 0 && _dgvTasks.Rows[dgvRowCount - 1].IsNewRow;
                int dataRowCount = hasNewRow ? dgvRowCount - 1 : dgvRowCount;
                
                // 如果数据不同步或缺少新行，需要修复
                if (taskCount != dataRowCount || !hasNewRow)
                {
                    // 使用定时器延迟执行，避免在RowEnter事件中直接修改DataGridView
                    System.Windows.Forms.Timer fixTimer = new System.Windows.Forms.Timer();
                    fixTimer.Interval = 10; // 很短的延迟
                    fixTimer.Tick += (timerSender, timerE) =>
                    {
                        fixTimer.Stop();
                        fixTimer.Dispose();
                        try
                        {
                            if (!_isUpdating)
                            {
                                EnsureNewRowAvailable();
                                RefreshRowIndices(); // 确保延时修复后序号正确
                            }
                        }
                        catch (Exception timerEx)
                        {
                            // Handle error silently
                        }
                    };
                    fixTimer.Start();
                }
            }
            catch (Exception ex)
            {
                // Handle error silently
            }
        }

        /// <summary>
        /// 刷新行序号
        /// </summary>
        private void RefreshRowIndices()
        {
            try
            {
                int sequenceNumber = 1;
                for (int i = 0; i < _dgvTasks.Rows.Count; i++)
                {
                    if (!_dgvTasks.Rows[i].IsNewRow)
                    {
                        DataGridViewCell cell = _dgvTasks.Rows[i].Cells["colIndex"];
                        if (cell != null)
                        {
                            cell.Value = sequenceNumber.ToString();
                            sequenceNumber++;
                        }
                    }
                    else
                    {
                        // 新行不设置序号
                        DataGridViewCell cell = _dgvTasks.Rows[i].Cells["colIndex"];
                        if (cell != null)
                        {
                            cell.Value = "";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle error silently
            }
        }

        /// <summary>
        /// 更新Offset列是否可编辑
        /// </summary>
        private void UpdateOffsetEditability()
        {
            try
            {
                foreach (DataGridViewRow row in _dgvTasks.Rows)
                {
                    if (row.IsNewRow) continue;
                    
                    // 获取是否手动输入的值
                    bool manualInput = false;
                    DataGridViewCell manualInputCell = row.Cells["colManualInput"];
                    if (manualInputCell != null && manualInputCell.Value != null)
                    {
                        bool.TryParse(manualInputCell.Value.ToString(), out manualInput);
                    }
                    
                    // 设置Offset列是否只读
                    DataGridViewCell offsetCell = row.Cells["colOffset"];
                    if (offsetCell != null)
                    {
                        offsetCell.ReadOnly = !manualInput;
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle error silently
            }
        }

        /// <summary>
        /// 清除行数据
        /// </summary>
        /// <param name="rowIndex">行索引</param>
        private void ClearRow(int rowIndex)
        {
            try
            {
                if (rowIndex >= 0 && rowIndex < _dgvTasks.Rows.Count && !_dgvTasks.Rows[rowIndex].IsNewRow && rowIndex < _taskList.Count)
                {
                    TaskItem item = _taskList[rowIndex];
                    string taskName = string.IsNullOrWhiteSpace(item.TaskName) ? $"第{rowIndex + 1}行" : item.TaskName;
                    
                    // 显示确认对话框
                    DialogResult result = MessageBox.Show(
                        $"确定要清除任务 \"{taskName}\" 的所有数据吗？\n\n此操作将清空该行的所有字段内容，但保留行本身。",
                        "确认清除",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question,
                        MessageBoxDefaultButton.Button2); // 默认选择"否"
                    
                    if (result == DialogResult.Yes)
                    {
                        _isUpdating = true;
                        
                        // 清空各字段，但保留对象引用
                        item.TaskName = null;
                        item.PeriodUs = null;
                        item.OffsetUs = null;
                        item.ManualInput = false;
                        item.ExecutionTimeUs = null;
                        item.Priority = null;
                        item.PrerequisiteTasks = null;
                        
                        _isUpdating = false;
                        
                        // 刷新显示
                        _dgvTasks.Refresh();
                        
                        // 更新App配置并保存
                        UpdateAppConfig();
                    }
                }
            }
            catch (Exception ex)
            {
                _isUpdating = false;
                MessageBox.Show($"清除行数据错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 删除行
        /// </summary>
        /// <param name="rowIndex">行索引</param>
        private void DeleteRow(int rowIndex)
        {
            try
            {
                if (rowIndex >= 0 && rowIndex < _dgvTasks.Rows.Count && !_dgvTasks.Rows[rowIndex].IsNewRow && rowIndex < _taskList.Count)
                {
                    TaskItem item = _taskList[rowIndex];
                    string taskName = string.IsNullOrWhiteSpace(item.TaskName) ? $"第{rowIndex + 1}行" : item.TaskName;
                    
                    // 显示确认对话框
                    DialogResult result = MessageBox.Show(
                        $"确定要删除任务 \"{taskName}\" 吗？\n\n此操作将永久删除该行及其所有数据，无法撤销。",
                        "确认删除",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Warning,
                        MessageBoxDefaultButton.Button2); // 默认选择"否"
                    
                    if (result == DialogResult.Yes)
                    {
                        _isUpdating = true;
                        
                        try
                        {
                            // 删除行
                            _taskList.RemoveAt(rowIndex);
                        }
                        finally
                        {
                            _isUpdating = false;
                        }
                        
                        // 等待DataGridView更新
                        Application.DoEvents();
                        
                        // 立即刷新序号
                        RefreshRowIndices();
                        
                        // 更新App配置并保存
                        UpdateAppConfig();
                        
                        // 检查并修复新行状态
                        // 使用延时执行确保DataGridView状态稳定
                        System.Windows.Forms.Timer timer = new System.Windows.Forms.Timer();
                        timer.Interval = 50; // 50ms延时
                        timer.Tick += (timerSender, timerE) =>
                        {
                            timer.Stop();
                            timer.Dispose();
                            
                            try
                            {
                                if (!_isUpdating)
                                {
                                    EnsureNewRowAvailable();
                                    RefreshRowIndices(); // 确保删除后序号正确
                                }
                            }
                            catch (Exception timerEx)
                            {
                                // Handle error silently
                            }
                        };
                        timer.Start();
                    }
                }
            }
            catch (Exception ex)
            {
                _isUpdating = false;
                MessageBox.Show($"删除行错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        

        
        /// <summary>
        /// 确保DataGridView有新行可用
        /// </summary>
        private void EnsureNewRowAvailable()
        {
            try
            {
                // 避免在更新过程中执行
                if (_isUpdating)
                {
                    return;
                }
                
                // 检查数据同步性
                int taskCount = _taskList?.Count ?? 0;
                int dgvRowCount = _dgvTasks?.Rows?.Count ?? 0;
                bool hasNewRow = dgvRowCount > 0 && _dgvTasks.Rows[dgvRowCount - 1].IsNewRow;
                int dataRowCount = hasNewRow ? dgvRowCount - 1 : dgvRowCount;
                
                // 如果数据不同步，需要修复
                if (taskCount != dataRowCount || !hasNewRow)
                {
                    _isUpdating = true;
                    try
                    {
                        // 方法1：重新绑定数据源
                        var tempList = new BindingList<TaskItem>();
                        foreach (var task in _taskList)
                        {
                            tempList.Add(task);
                        }
                        
                        // 取消事件订阅，避免循环触发
                        tempList.ListChanged -= TaskList_ListChanged;
                        
                        // 重新设置数据源
                        _dgvTasks.DataSource = null;
                        _dgvTasks.DataSource = tempList;
                        
                        // 恢复事件订阅
                        _taskList = tempList;
                        _taskList.ListChanged += TaskList_ListChanged;
                        
                        // 确保有新行
                        _dgvTasks.AllowUserToAddRows = false;
                        Application.DoEvents();
                        _dgvTasks.AllowUserToAddRows = true;
                        Application.DoEvents();
                        
                        // 重新绑定后必须刷新序号
                        RefreshRowIndices();
                        
                        // 更新Offset是否可编辑
                        UpdateOffsetEditability();
                    }
                    finally
                    {
                        _isUpdating = false;
                    }
                }
            }
            catch (Exception ex)
            {
                _isUpdating = false;
                // Handle error silently
            }
        }

        /// <summary>
        /// 添加新任务
        /// </summary>
        public void AddNewTask()
        {
            try
            {
                _isUpdating = true;
                
                // 创建新任务项
                TaskItem newTask = new TaskItem();
                
                // 为新任务分配唯一的TaskIndex
                newTask.TaskIndex = GetNextAvailableTaskIndex();
                
                // 添加到列表
                _taskList.Add(newTask);
                
                _isUpdating = false;
                
                // 刷新序号
                RefreshRowIndices();
                
                // 选中新行
                int newRowIndex = _taskList.Count - 1;
                if (newRowIndex >= 0 && newRowIndex < _dgvTasks.Rows.Count)
                {
                    if (_dgvTasks.Columns.Contains("colTaskName"))
                    {
                        _dgvTasks.CurrentCell = _dgvTasks.Rows[newRowIndex].Cells["colTaskName"];
                        _dgvTasks.BeginEdit(true);
                    }
                }
                
                // 更新App配置并保存
                UpdateAppConfig();
            }
            catch (Exception ex)
            {
                _isUpdating = false;
                MessageBox.Show($"添加新任务错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空所有任务
        /// </summary>
        public void ClearAllTasks()
        {
            try
            {
                _isUpdating = true;
                
                // 清空列表
                _taskList.Clear();
                
                _isUpdating = false;
                
                // 刷新显示
                _dgvTasks.Refresh();
                
                // 确保DataGridView有新行可用
                EnsureNewRowAvailable();
                
                // 刷新序号
                RefreshRowIndices();
                
                // 更新App配置并保存
                UpdateAppConfig();
            }
            catch (Exception ex)
            {
                _isUpdating = false;
                MessageBox.Show($"清空所有任务错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 获取列宽度
        /// </summary>
        /// <param name="columnName">列名</param>
        /// <param name="defaultWidth">默认宽度</param>
        /// <returns>列宽度</returns>
        private int GetColumnWidth(string columnName, int defaultWidth)
        {
            try
            {
                if (_appConfig.MainConfig.ColumnWidths.ContainsKey(columnName))
                {
                    return _appConfig.MainConfig.ColumnWidths[columnName];
                }
                
                // 如果没有保存过列宽，则使用默认值
                _appConfig.MainConfig.ColumnWidths[columnName] = defaultWidth;
                return defaultWidth;
            }
            catch
            {
                return defaultWidth;
            }
        }

        /// <summary>
        /// 更新绑定列表内容
        /// </summary>
        /// <param name="tasks">新的任务列表</param>
        public void UpdateTasks(List<TaskItem> tasks)
        {
            try
            {
                _isUpdating = true;
                
                // 清空现有列表
                _taskList.Clear();
                
                // 添加新的任务
                foreach (TaskItem task in tasks)
                {
                    _taskList.Add(task);
                }
                
                _isUpdating = false;
                
                // 刷新显示
                _dgvTasks.Refresh();
                
                // 刷新序号
                RefreshRowIndices();
                
                // 更新App配置并保存
                UpdateAppConfig();
            }
            catch (Exception ex)
            {
                _isUpdating = false;
                MessageBox.Show($"更新任务列表错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 只更新有效任务的Offset值，保留所有原始任务
        /// </summary>
        /// <param name="validTasks">优化后的有效任务列表</param>
        public void UpdateValidTasksOffset(List<TaskItem> validTasks)
        {
            try
            {
                _isUpdating = true;
                
                // 创建TaskIndex到OffsetUs的映射，使用GroupBy避免重复TaskIndex异常
                var offsetMap = validTasks.GroupBy(t => t.TaskIndex)
                    .ToDictionary(g => g.Key, g => g.First().OffsetUs);
                
                // 只更新存在于映射中的任务的OffsetUs值 - 保护手动输入的任务
                foreach (var task in _taskList)
                {
                    if (task.ManualInput)
                    {
                        continue;
                    }
                    
                    if (offsetMap.TryGetValue(task.TaskIndex, out var newOffsetUs))
                    {
                        task.OffsetUs = newOffsetUs;
                    }
                }
                
                _isUpdating = false;
                
                // 刷新显示
                _dgvTasks.Refresh();
                
                // 更新App配置并保存
                UpdateAppConfig();
            }
            catch (Exception ex)
            {
                _isUpdating = false;
                MessageBox.Show($"更新任务Offset错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 获取所有任务
        /// </summary>
        /// <returns>任务列表</returns>
        public List<TaskItem> GetAllTasks()
        {
            // 确保返回一个拷贝，避免引用问题
            List<TaskItem> result = new List<TaskItem>();
            foreach (var task in _taskList)
            {
                result.Add(task);
            }
            return result;
        }

        /// <summary>
        /// 单元格格式化事件，将内部微秒值转换为界面毫秒值显示
        /// </summary>
        private void DgvTasks_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            try
            {
                if (e.ColumnIndex >= 0 && e.RowIndex >= 0 && e.Value != null)
                {
                    string columnName = _dgvTasks.Columns[e.ColumnIndex].Name;
                    
                    // 处理需要单位转换的列（从微秒转换为毫秒显示）
                    if (columnName == "colPeriod" /*|| columnName == "colAvgExecutionTime" */|| columnName == "colMaxExecutionTime" || columnName == "colOffset")
                    {
                        if (e.Value is int usValue)
                        {
                            // 将微秒值转换为毫秒值显示
                            double msValue = TaskUtility.ConvertToMilliseconds(usValue);
                            if (columnName == "colPeriod" || columnName == "colOffset")
                            {
                                e.Value = $"{msValue:F1}"; // 周期和Offset显示1位小数
                            }
                            else if (/*columnName == "colAvgExecutionTime" ||*/ columnName == "colMaxExecutionTime")
                            {
                                e.Value = $"{msValue:F3}"; // 执行时间显示3位小数
                            }
                            e.FormattingApplied = true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle error silently
            }
        }

        /// <summary>
        /// 单元格解析事件，将用户输入的毫秒值转换为内部微秒值存储
        /// </summary>
        private void DgvTasks_CellParsing(object sender, DataGridViewCellParsingEventArgs e)
        {
            try
            {
                if (e.ColumnIndex >= 0 && e.RowIndex >= 0)
                {
                    string columnName = _dgvTasks.Columns[e.ColumnIndex].Name;
                    
                    // 处理需要单位转换的列（从毫秒转换为微秒存储）
                    if (columnName == "colPeriod" /*|| columnName == "colAvgExecutionTime"*/ || columnName == "colMaxExecutionTime" || columnName == "colOffset")
                    {
                        if (e.Value != null && double.TryParse(e.Value.ToString(), out double msValue))
                        {
                            // 将毫秒值转换为微秒值
                            int usValue = TaskUtility.ConvertToMicroseconds(msValue);
                            
                            // 只有Offset需要确保符合时基约束，执行时间和周期保持用户输入的精度
                            if (columnName == "colOffset")
                            {
                                usValue = TaskUtility.EnsureTimeBaseMultiple(usValue, TaskUtility.MIN_TIME_BASE_US);
                            }
                            
                            e.Value = usValue;
                            e.ParsingApplied = true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Handle error silently
            }
        }

        /// <summary>
        /// 确保所有任务都有正确的TaskIndex分配
        /// </summary>
        private void EnsureTaskIndexAssignment()
        {
            try
            {
                // 记录需要分配新TaskIndex的任务
                var tasksNeedNewIndex = new List<TaskItem>();
                var usedIndexes = new HashSet<int>();
                
                // 首先收集已经使用的TaskIndex
                foreach (var task in _taskList)
                {
                    if (task.TaskIndex > 0 && !usedIndexes.Contains(task.TaskIndex))
                    {
                        usedIndexes.Add(task.TaskIndex);
                    }
                    else
                    {
                        tasksNeedNewIndex.Add(task);
                    }
                }
                
                // 为需要新TaskIndex的任务分配唯一值
                int nextAvailableIndex = 1; // 从1开始分配TaskIndex
                foreach (var task in tasksNeedNewIndex)
                {
                    // 找到下一个可用的TaskIndex
                    while (usedIndexes.Contains(nextAvailableIndex))
                    {
                        nextAvailableIndex++;
                    }
                    
                    task.TaskIndex = nextAvailableIndex;
                    usedIndexes.Add(nextAvailableIndex);
                    nextAvailableIndex++;
                }
            }
            catch (Exception ex)
            {
                // Handle error silently
            }
        }

        /// <summary>
        /// 获取下一个可用的TaskIndex
        /// </summary>
        /// <returns>下一个可用的TaskIndex</returns>
        private int GetNextAvailableTaskIndex()
        {
            var usedIndexes = new HashSet<int>();
            
            // 收集所有已使用的TaskIndex
            foreach (var task in _taskList)
            {
                if (task.TaskIndex > 0)
                {
                    usedIndexes.Add(task.TaskIndex);
                }
            }
            
            // 从1开始找到第一个未使用的索引
            int nextIndex = 1;
            while (usedIndexes.Contains(nextIndex))
            {
                nextIndex++;
            }
            
            return nextIndex;
        }

        /// <summary>
        /// 刷新DataGridView显示
        /// </summary>
        public void RefreshDataGridView()
        {
            try
            {
                // 刷新DataGridView显示
                _dgvTasks.Refresh();
                _dgvTasks.Invalidate();
            }
            catch (Exception ex)
            {
                // Handle error silently
            }
        }
    }
} 