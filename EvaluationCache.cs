using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace CalculateOffset
{
    /// <summary>
    /// 解的唯一标识键，用于缓存评估结果
    /// </summary>
    public class SolutionKey : IEquatable<SolutionKey>
    {
        private readonly string _keyString;
        private readonly int _hashCode;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="offsetSolution">任务索引到Offset(微秒)的映射</param>
        public SolutionKey(Dictionary<int, int> offsetSolution)
        {
            if (offsetSolution == null || offsetSolution.Count == 0)
            {
                _keyString = "EMPTY";
                _hashCode = 0;
                return;
            }

            // 将解按TaskIndex排序，生成稳定的键字符串
            var sortedPairs = offsetSolution.OrderBy(kvp => kvp.Key).ToList();
            var keyBuilder = new StringBuilder();
            
            foreach (var pair in sortedPairs)
            {
                if (keyBuilder.Length > 0)
                    keyBuilder.Append("|");
                keyBuilder.Append($"{pair.Key}:{pair.Value}");
            }
            
            _keyString = keyBuilder.ToString();
            _hashCode = _keyString.GetHashCode();
        }

        /// <summary>
        /// 快速构造函数，直接从任务列表构造
        /// </summary>
        /// <param name="tasks">任务列表</param>
        public SolutionKey(List<TaskItem> tasks)
        {
            if (tasks == null || tasks.Count == 0)
            {
                _keyString = "EMPTY";
                _hashCode = 0;
                return;
            }

            // 过滤有效的任务并按TaskIndex排序
            var validTasks = tasks.Where(t => t.OffsetUs.HasValue)
                                 .OrderBy(t => t.TaskIndex)
                                 .ToList();

            if (validTasks.Count == 0)
            {
                _keyString = "EMPTY";
                _hashCode = 0;
                return;
            }

            var keyBuilder = new StringBuilder();
            foreach (var task in validTasks)
            {
                if (keyBuilder.Length > 0)
                    keyBuilder.Append("|");
                keyBuilder.Append($"{task.TaskIndex}:{task.OffsetUs.Value}");
            }
            
            _keyString = keyBuilder.ToString();
            _hashCode = _keyString.GetHashCode();
        }

        public bool Equals(SolutionKey other)
        {
            if (other == null) return false;
            return _keyString == other._keyString;
        }

        public override bool Equals(object obj)
        {
            return Equals(obj as SolutionKey);
        }

        public override int GetHashCode()
        {
            return _hashCode;
        }

        public override string ToString()
        {
            return _keyString;
        }
    }

    /// <summary>
    /// 缓存的评估结果
    /// </summary>
    public class CachedEvaluationResult
    {
        /// <summary>
        /// 成本值
        /// </summary>
        public double CostValue { get; set; }

        /// <summary>
        /// 完整的可调度性分析结果
        /// </summary>
        public SchedulabilityResult SchedulabilityResult { get; set; }

        /// <summary>
        /// 缓存时间
        /// </summary>
        public DateTime CachedAt { get; set; }

        /// <summary>
        /// 命中次数
        /// </summary>
        public long HitCount { get; set; }

        public CachedEvaluationResult(double costValue, SchedulabilityResult result)
        {
            CostValue = costValue;
            SchedulabilityResult = result;
            CachedAt = DateTime.Now;
            HitCount = 0;
        }
    }

    /// <summary>
    /// 高性能评估缓存管理器
    /// 支持线程安全、自动清理和性能统计
    /// </summary>
    public class EvaluationCache
    {
        #region 私有字段

        /// <summary>
        /// 线程安全的缓存字典
        /// </summary>
        private readonly ConcurrentDictionary<SolutionKey, CachedEvaluationResult> _cache;

        /// <summary>
        /// 缓存统计信息
        /// </summary>
        private long _hitCount = 0;
        private long _missCount = 0;
        private readonly object _statsLock = new object();

        /// <summary>
        /// 缓存配置参数
        /// </summary>
        private readonly int _maxCacheSize;
        private readonly TimeSpan _maxCacheAge;
        private readonly bool _enableStatistics;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="maxCacheSize">最大缓存条目数</param>
        /// <param name="maxCacheAgeMinutes">缓存最大存活时间(分钟)</param>
        /// <param name="enableStatistics">是否启用统计</param>
        public EvaluationCache(int maxCacheSize = 1000000, int maxCacheAgeMinutes = 30, bool enableStatistics = true)
        {
            _cache = new ConcurrentDictionary<SolutionKey, CachedEvaluationResult>();
            _maxCacheSize = maxCacheSize;
            _maxCacheAge = TimeSpan.FromMinutes(maxCacheAgeMinutes);
            _enableStatistics = enableStatistics;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 尝试从缓存获取评估结果
        /// </summary>
        /// <param name="solutionKey">解的键</param>
        /// <param name="result">缓存的结果</param>
        /// <returns>是否命中缓存</returns>
        public bool TryGetCachedResult(SolutionKey solutionKey, out CachedEvaluationResult result)
        {
            if (_cache.TryGetValue(solutionKey, out result))
            {
                // 检查缓存是否过期
                if (DateTime.Now - result.CachedAt > _maxCacheAge)
                {
                    _cache.TryRemove(solutionKey, out _);
                    result = null;
                    IncrementMissCount();
                    return false;
                }

                // 更新命中统计
                result.HitCount++;
                IncrementHitCount();
                return true;
            }

            IncrementMissCount();
            return false;
        }

        /// <summary>
        /// 添加评估结果到缓存
        /// </summary>
        /// <param name="solutionKey">解的键</param>
        /// <param name="costValue">成本值</param>
        /// <param name="schedulabilityResult">完整分析结果</param>
        public void AddToCache(SolutionKey solutionKey, double costValue, SchedulabilityResult schedulabilityResult)
        {
            // 如果缓存已满，清理过期条目
            if (_cache.Count >= _maxCacheSize)
            {
                CleanupExpiredEntries();
                
                // 如果清理后仍然满，移除最旧的条目
                if (_cache.Count >= _maxCacheSize)
                {
                    RemoveOldestEntries(_maxCacheSize / 4); // 移除25%的条目
                }
            }

            var cachedResult = new CachedEvaluationResult(costValue, schedulabilityResult);
            _cache.TryAdd(solutionKey, cachedResult);
        }

        /// <summary>
        /// 清空缓存
        /// </summary>
        public void Clear()
        {
            _cache.Clear();
            lock (_statsLock)
            {
                _hitCount = 0;
                _missCount = 0;
            }
        }

        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        /// <returns>缓存统计</returns>
        public CacheStatistics GetStatistics()
        {
            lock (_statsLock)
            {
                return new CacheStatistics
                {
                    CacheSize = _cache.Count,
                    HitCount = _hitCount,
                    MissCount = _missCount,
                    HitRate = _hitCount + _missCount > 0 ? (double)_hitCount / (_hitCount + _missCount) : 0.0,
                    MaxCacheSize = _maxCacheSize
                };
            }
        }

        /// <summary>
        /// 输出缓存统计报告
        /// </summary>
        public void PrintStatistics()
        {
            if (!_enableStatistics) return;

            var stats = GetStatistics();
            Console.WriteLine($"📊 [评估缓存统计]");
            Console.WriteLine($"   缓存大小: {stats.CacheSize}/{stats.MaxCacheSize}");
            Console.WriteLine($"   命中次数: {stats.HitCount}");
            Console.WriteLine($"   未命中次数: {stats.MissCount}");
            Console.WriteLine($"   命中率: {stats.HitRate:P2}");
            
            if (stats.HitCount > 0)
            {
                Console.WriteLine($"   💡 缓存节省了 {stats.HitCount} 次昂贵的WCRT分析！");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 清理过期的缓存条目
        /// </summary>
        private void CleanupExpiredEntries()
        {
            var expiredKeys = new List<SolutionKey>();
            var cutoffTime = DateTime.Now - _maxCacheAge;

            foreach (var kvp in _cache)
            {
                if (kvp.Value.CachedAt < cutoffTime)
                {
                    expiredKeys.Add(kvp.Key);
                }
            }

            foreach (var key in expiredKeys)
            {
                _cache.TryRemove(key, out _);
            }

            if (expiredKeys.Count > 0 && _enableStatistics)
            {
                Console.WriteLine($"🧹 [评估缓存] 清理了 {expiredKeys.Count} 个过期条目");
            }
        }

        /// <summary>
        /// 移除最旧的缓存条目
        /// </summary>
        /// <param name="countToRemove">要移除的条目数</param>
        private void RemoveOldestEntries(int countToRemove)
        {
            var sortedEntries = _cache.OrderBy(kvp => kvp.Value.CachedAt).Take(countToRemove).ToList();
            
            foreach (var entry in sortedEntries)
            {
                _cache.TryRemove(entry.Key, out _);
            }

            if (sortedEntries.Count > 0 && _enableStatistics)
            {
                Console.WriteLine($"🧹 [评估缓存] 移除了 {sortedEntries.Count} 个最旧条目");
            }
        }

        /// <summary>
        /// 增加命中计数
        /// </summary>
        private void IncrementHitCount()
        {
            if (_enableStatistics)
            {
                lock (_statsLock)
                {
                    _hitCount++;
                }
            }
        }

        /// <summary>
        /// 增加未命中计数
        /// </summary>
        private void IncrementMissCount()
        {
            if (_enableStatistics)
            {
                lock (_statsLock)
                {
                    _missCount++;
                }
            }
        }

        #endregion
    }

    /// <summary>
    /// 缓存统计信息
    /// </summary>
    public class CacheStatistics
    {
        /// <summary>
        /// 当前缓存大小
        /// </summary>
        public int CacheSize { get; set; }

        /// <summary>
        /// 命中次数
        /// </summary>
        public long HitCount { get; set; }

        /// <summary>
        /// 未命中次数
        /// </summary>
        public long MissCount { get; set; }

        /// <summary>
        /// 命中率
        /// </summary>
        public double HitRate { get; set; }

        /// <summary>
        /// 最大缓存大小
        /// </summary>
        public int MaxCacheSize { get; set; }
    }
}
