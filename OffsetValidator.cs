using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace CalculateOffset
{
    /// <summary>
    /// 偏移量校验器，使用抢占式调度时间线模拟验证计算得出的偏移量是否合理
    /// </summary>
    public class OffsetValidator
    {
        // 时基(微秒)
        private int _timeBaseUs;
        
        // 超周期长度
        private int _hyperPeriod;
        
        // 抢占式时间线（时间点 -> 正在执行的任务）
        private Dictionary<int, ExecutingTask> _preemptiveTimeline;
        
        // 可调度性分析器
        private SchedulabilityAnalyzer _analyzer;
        
        // 最新的模拟结果，用于jitter调整
        private PreemptiveSimulationResult _lastSimulationResult;
        
        // 任务列表，供内部方法使用
        private List<TaskItem> _tasks;
        
        // 构造函数
        public OffsetValidator(int timeBaseUs)
        {
            _timeBaseUs = Math.Max(TaskUtility.MIN_TIME_BASE_US, timeBaseUs);
            _preemptiveTimeline = new Dictionary<int, ExecutingTask>();
            _analyzer = new SchedulabilityAnalyzer(timeBaseUs);
        }
        
        /// <summary>
        /// 验证任务偏移量是否合理
        /// </summary>
        /// <param name="tasks">任务列表（包含最终的offset值）</param>
        /// <returns>校验结果对象</returns>
        public ValidationResult ValidateOffsets(List<TaskItem> tasks)
        {
            return ValidateOffsets(tasks, 0); // 使用默认超周期
        }
        
        /// <summary>
        /// 验证任务偏移量是否合理（支持自定义模拟时间长度）
        /// </summary>
        /// <param name="tasks">任务列表（包含最终的offset值）</param>
        /// <param name="customSimulationTimeUs">自定义模拟时间长度（微秒），0表示使用默认超周期</param>
        /// <returns>校验结果对象</returns>
        public ValidationResult ValidateOffsets(List<TaskItem> tasks, int customSimulationTimeUs)
        {
            // 存储任务列表供内部方法使用
            _tasks = tasks;
            
            // 过滤有效任务
            var validTasks = TaskUtility.GetValidTasks(tasks);
            
            if (validTasks.Count == 0)
            {
                return new ValidationResult
                {
                    IsValid = false,
                    Message = "没有有效任务可验证"
                };
            }
            
            // 使用SchedulabilityAnalyzer进行精确分析
            var schedulabilityResult = _analyzer.AnalyzeSchedulability(validTasks);
            
            // 计算超周期
            _hyperPeriod = TaskUtility.CalculateHyperPeriod(validTasks);
            
            // 如果指定了自定义模拟时间，使用更长的时间进行模拟
            int simulationTimeUs = customSimulationTimeUs > 0 ? customSimulationTimeUs : _hyperPeriod;
            
            // 超周期和模拟时间计算完成
            
            // 清空抢占式时间线
            _preemptiveTimeline.Clear();
            
            // 模拟抢占式调度
            var simulationResult = SimulatePreemptiveScheduling(validTasks, simulationTimeUs);
            
            // 保存模拟结果供时间轴生成使用
            _lastSimulationResult = simulationResult;
            
            // 分析验证结果
            var result = AnalyzeSchedulingResults(validTasks, schedulabilityResult, simulationResult);
            
            // 生成jitter分析报告
            GenerateJitterAnalysisReport(simulationResult.CompletedInstances);
            
            return result;
        }

        /// <summary>
        /// 生成详细的jitter分析报告
        /// </summary>
        /// <param name="completedInstances">完成的任务实例</param>
        private void GenerateJitterAnalysisReport(List<TaskInstance> completedInstances)
        {
            // 静默生成Jitter分析报告，不输出详细信息
            var taskGroups = completedInstances.GroupBy(i => i.TaskName).OrderBy(g => g.Key);
            
            foreach (var group in taskGroups)
            {
                var instances = group.ToList();
                var firstInstance = instances.First();
                
                int totalInstances = instances.Count;
                int totalJitter = instances.Sum(i => i.AccumulatedJitterUs);
                int totalSwitches = instances.Sum(i => i.ContextSwitchCount);
                
                // 计算旧方式的jitter（每个实例固定一次）
                int oldWayJitter = totalInstances * firstInstance.JitterTimeUs;
                
                // 计算改进效果
                double improvement = oldWayJitter > 0 ? ((double)(totalJitter - oldWayJitter) / oldWayJitter) * 100 : 0;
            }
            
            // 系统总体统计
            int systemTotalJitter = completedInstances.Sum(i => i.AccumulatedJitterUs);
            int systemTotalSwitches = completedInstances.Sum(i => i.ContextSwitchCount);
            int systemOldWayJitter = completedInstances.Sum(i => i.JitterTimeUs); // 每个实例一次
            
            double systemImprovement = systemOldWayJitter > 0 ? ((double)(systemTotalJitter - systemOldWayJitter) / systemOldWayJitter) * 100 : 0;
        }
        
        /// <summary>
        /// 模拟抢占式调度在超周期内的执行
        /// </summary>
        private PreemptiveSimulationResult SimulatePreemptiveScheduling(List<TaskItem> tasks)
        {
            return SimulatePreemptiveScheduling(tasks, _hyperPeriod);
        }
        
        /// <summary>
        /// 模拟抢占式调度在指定时间内的执行
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="simulationTimeUs">模拟时间长度（微秒）</param>
        private PreemptiveSimulationResult SimulatePreemptiveScheduling(List<TaskItem> tasks, int simulationTimeUs)
        {
            // 按优先级排序任务（优先级高的数值小）
            var sortedTasks = TaskUtility.SortTasksByPriorityAndPeriod(tasks);
            
            // 生成所有任务实例
            var taskInstances = GenerateTaskInstances(sortedTasks, simulationTimeUs);
            
            // 按时间点排序
            taskInstances.Sort((a, b) => 
            {
                int timeCompare = a.ArrivalTime.CompareTo(b.ArrivalTime);
                if (timeCompare != 0) return timeCompare;
                // 同时到达时按优先级排序（数字越大优先级越高）
                return b.Priority.CompareTo(a.Priority);
            });
            
            // 模拟抢占式调度
            var readyQueue = new List<TaskInstance>();
            var completedInstances = new List<TaskInstance>();
            var preemptions = new List<PreemptionEvent>();
            int currentTime = 0;
            TaskInstance currentExecuting = null;
            
            int instanceIndex = 0;
            
            // 主调度循环 - 使用高精度时间步长进行模拟
            int simulationStepUs = TaskUtility.US_STEP; // 使用10微秒精度
            while (currentTime < simulationTimeUs)
            {
                // 1. 处理新到达的任务实例
                while (instanceIndex < taskInstances.Count && 
                       taskInstances[instanceIndex].ArrivalTime <= currentTime)
                {
                    var newInstance = taskInstances[instanceIndex];
                    readyQueue.Add(newInstance);
                    instanceIndex++;
                    
                    // 检查是否需要抢占（优先级数字越大，优先级越高）
                    if (currentExecuting == null || newInstance.Priority > currentExecuting.Priority)
                    {
                        if (currentExecuting != null)
                        {
                            // 记录抢占事件（不在这里计算jitter，避免重复计算）
                            preemptions.Add(new PreemptionEvent
                            {
                                Time = currentTime,
                                PreemptedTask = currentExecuting.TaskName,
                                PreemptingTask = newInstance.TaskName,
                                TimeBaseUs = simulationStepUs
                            });
                            
                            // 新规则：仅记录被抢占次数，不在此刻累加jitter
                            currentExecuting.PreemptionCount++;
                            // 被抢占的任务重新放回就绪队列
                            readyQueue.Add(currentExecuting);
                        }
                        
                        // 新规则：开始/恢复时不计入jitter，只标记已开始
                        if (!newInstance.HasStarted)
                        {
                            newInstance.HasStarted = true;
                        }
                        
                        // 切换到高优先级任务
                        currentExecuting = newInstance;
                        readyQueue.Remove(newInstance);
                    }
                }
                
                // 2. 记录时间线状态（包括空闲时间）
                if (currentExecuting != null)
                {
                    // 记录执行中的任务
                    _preemptiveTimeline[currentTime] = new ExecutingTask
                    {
                        TaskName = currentExecuting.TaskName,
                        Priority = currentExecuting.Priority,
                        InstanceId = currentExecuting.InstanceId,
                        RemainingTime = currentExecuting.RemainingExecutionTime
                    };
                    
                    // 执行一个高精度时基单位
                    currentExecuting.RemainingExecutionTime -= simulationStepUs;
                    
                    // 检查任务是否完成
                    if (currentExecuting.RemainingExecutionTime <= 0)
                    {
                        currentExecuting.CompletionTime = currentTime + simulationStepUs;
                        // 新规则：在完成时一次性结算jitter
                        // jitter = JitterTimeUs × (1 + 被抢占次数)
                        var finalizedJitter = currentExecuting.JitterTimeUs * (1 + currentExecuting.PreemptionCount);
                        currentExecuting.AccumulatedJitterUs = finalizedJitter;
                        currentExecuting.ContextSwitchCount = 1 + currentExecuting.PreemptionCount;
                        // 响应时间 = 完成时间 - 到达时间 + 统一结算的jitter
                        currentExecuting.ResponseTime = currentExecuting.CompletionTime - currentExecuting.ArrivalTime + currentExecuting.AccumulatedJitterUs;
                        completedInstances.Add(currentExecuting);
                        
                        // 选择下一个最高优先级任务（优先级数字越大，优先级越高）
                        var nextTask = readyQueue.OrderByDescending(t => t.Priority).FirstOrDefault();
                        if (nextTask != null)
                        {
                            readyQueue.Remove(nextTask);
                            
                            // 新规则：开始/恢复时不计入jitter，只标记已开始
                            if (!nextTask.HasStarted)
                            {
                                nextTask.HasStarted = true;
                            }
                        }
                        currentExecuting = nextTask;
                    }
                }
                else
                {
                    // 记录空闲时间
                    _preemptiveTimeline[currentTime] = new ExecutingTask
                    {
                        TaskName = "", // 空字符串表示空闲
                        Priority = int.MaxValue, // 最低优先级
                        InstanceId = 0,
                        RemainingTime = 0
                    };
                }
                
                currentTime += simulationStepUs;
            }
            
            // 抢占模拟完成，jitter统计已完成

            return new PreemptiveSimulationResult
            {
                CompletedInstances = completedInstances,
                PreemptionEvents = preemptions,
                TotalSimulationTime = currentTime,
                TimelineSize = _preemptiveTimeline.Count
            };
        }
        
        /// <summary>
        /// 生成超周期内的所有任务实例
        /// </summary>
        private List<TaskInstance> GenerateTaskInstances(List<TaskItem> tasks)
        {
            return GenerateTaskInstances(tasks, _hyperPeriod);
        }
        
        /// <summary>
        /// 生成指定时间内的所有任务实例
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="simulationTimeUs">模拟时间长度（微秒）</param>
        private List<TaskInstance> GenerateTaskInstances(List<TaskItem> tasks, int simulationTimeUs)
        {
            var instances = new List<TaskInstance>();
            int instanceCounter = 0;
            
            foreach (var task in tasks)
            {
                int periodUs = task.PeriodUs.Value;
                int offsetUs = task.OffsetUs ?? 0;
                int executionTimeUs = task.ExecutionTimeUs ?? TaskUtility.MIN_EXECUTION_TIME_US;
                
                // 确保偏移量是时基的整数倍
                offsetUs = TaskUtility.EnsureTimeBaseMultiple(offsetUs, _timeBaseUs);
                
                // 生成该任务在指定时间内的所有实例
                for (int arrivalTime = offsetUs; arrivalTime < simulationTimeUs; arrivalTime += periodUs)
                {
                    instances.Add(new TaskInstance
                    {
                        InstanceId = ++instanceCounter,
                        TaskName = task.TaskName,
                        Priority = task.Priority ?? int.MaxValue,
                        ArrivalTime = arrivalTime,
                        ExecutionTime = executionTimeUs,  // 原始执行时间，不包含jitter
                        RemainingExecutionTime = executionTimeUs, // 剩余执行时间，jitter将动态累加
                        Deadline = arrivalTime + periodUs,
                        Period = periodUs,
                        JitterTimeUs = task.JitterTimeUs,   // 单次切换的jitter时间
                        AccumulatedJitterUs = 0,            // 累计jitter开销
                        ContextSwitchCount = 0,             // 上下文切换次数
                        HasStarted = false                  // 尚未开始执行
                    });
                }
            }
            
            return instances;
        }
        
        /// <summary>
        /// 获取详细的抢占式时间线数据，供时间轴生成器复用
        /// </summary>
        /// <returns>抢占式时间线数据</returns>
        public DetailedTimelineData GetDetailedTimelineData()
        {
            // 生成高精度时间点分布数据
            var distributionData = new List<PreemptiveTimePoint>();
            var taskSegments = new Dictionary<string, List<TaskSegment>>();
            
                        // 将内部时间线转换为外部格式，并计算抢占层级
            var taskLayers = new Dictionary<string, int>(); // 记录每个任务的当前层级
            var currentLayer = 0; // 当前执行层级
            
            foreach (var kvp in _preemptiveTimeline.OrderBy(kv => kv.Key))
            {
                int timeUs = kvp.Key;
                var executingTask = kvp.Value;
                string currentTaskName = executingTask.TaskName;
                
                int displayLayer = 0; // 显示层级
                
                if (!string.IsNullOrEmpty(currentTaskName))
                {
                    // 检查是否是新任务或任务切换
                    if (distributionData.Count > 0)
                    {
                        var prevPoint = distributionData.Last();
                        string prevTaskName = prevPoint.ExecutingTask;
                        int prevLayer = prevPoint.PreemptionLevel;
                        
                        if (prevTaskName != currentTaskName)
                        {
                            // 发生了任务切换
                            if (!string.IsNullOrEmpty(prevTaskName))
                            {
                                // 检查是否是抢占（被抢占的任务稍后还会执行且有剩余执行时间）
                                bool isPreemption = CheckIfTaskWillResumeExecution(timeUs - TaskUtility.US_STEP, prevTaskName);
                                
                                if (isPreemption)
                                {
                                    // 发生抢占：被抢占任务保持当前层级，抢占任务层级+1
                                    taskLayers[prevTaskName] = prevLayer; // 被抢占任务保持当前层级
                                    
                                    if (taskLayers.ContainsKey(currentTaskName))
                                    {
                                        // 如果抢占任务之前已经有层级，使用之前的层级
                                        displayLayer = taskLayers[currentTaskName];
                                    }
                                    else
                                    {
                                        // 新的抢占任务，层级 = 被抢占任务层级 + 1
                                        displayLayer = prevLayer + 1;
                                        taskLayers[currentTaskName] = displayLayer;
                                    }
                                    
                                    currentLayer = displayLayer;
                                }
                                else
                                {
                                    // 正常任务切换：前一个任务完成，新任务开始
                                    
                                    // 正常切换时，新任务应该从层级0开始（除非它之前被抢占过）
                                    if (taskLayers.ContainsKey(currentTaskName))
                                    {
                                        // 恢复之前被抢占的任务的层级
                                        displayLayer = taskLayers[currentTaskName];
                                    }
                                    else
                                    {
                                        // 新任务从层级0开始
                                        displayLayer = 0;
                                        taskLayers[currentTaskName] = displayLayer;
                                    }
                                    
                                    // 清理已完成任务的层级记录
                                    taskLayers.Remove(prevTaskName);
                                    currentLayer = displayLayer;
                                }
                            }
                            else
                            {
                                // 从空闲状态开始执行任务
                                if (taskLayers.ContainsKey(currentTaskName))
                                {
                                    // 恢复之前被抢占的任务
                                    displayLayer = taskLayers[currentTaskName];
                                }
                                else
                                {
                                    // 全新任务，从层级0开始
                                    displayLayer = 0;
                                    taskLayers[currentTaskName] = displayLayer;
                                }
                                currentLayer = displayLayer;
                            }
                        }
                        else
                        {
                            // 任务继续执行，保持当前层级
                            displayLayer = currentLayer;
                        }
                    }
                    else
                    {
                        // 第一个任务
                        displayLayer = 0;
                        taskLayers[currentTaskName] = displayLayer;
                        currentLayer = displayLayer;
                    }
                }
                else
                {
                    // 空闲状态：清空层级信息
                    taskLayers.Clear();
                    currentLayer = 0;
                    displayLayer = -1;
                }
                
                distributionData.Add(new PreemptiveTimePoint
                {
                    TimeUs = timeUs,
                    ExecutingTask = currentTaskName,
                    PreemptionLevel = displayLayer
                });
            }
            
            // 统计层级分布信息
            var layerStats = distributionData
                .Where(p => !string.IsNullOrEmpty(p.ExecutingTask))
                .GroupBy(p => p.PreemptionLevel)
                .ToDictionary(g => g.Key, g => g.Count());
            
            // 生成任务执行段（传递distributionData以获取正确的层级信息，包含jitter调整）
            GenerateTaskSegmentsWithJitter(taskSegments, distributionData, _tasks);
            
            return new DetailedTimelineData
            {
                DistributionData = distributionData,
                TaskSegments = taskSegments,
                HyperPeriodUs = _hyperPeriod,
                TimeBaseUs = TaskUtility.US_STEP // 返回实际的采样精度
            };
        }
        
        /// <summary>
        /// 检查任务在指定时间点后是否还会继续执行（用于判断是否被抢占）
        /// 修正：只有在任务执行时间未完成就被切换才算抢占，考虑模拟步长边界问题
        /// </summary>
        private bool CheckIfTaskWillResumeExecution(int checkTimeUs, string taskName)
        {
            // 获取检查时间点的执行任务信息
            if (!_preemptiveTimeline.TryGetValue(checkTimeUs, out var taskAtCheckTime))
                return false;
                
            // 如果检查时间点的任务不是指定的任务，则不需要检查
            if (taskAtCheckTime.TaskName != taskName)
                return false;
                
            // 关键修复：检查任务是否在下一个模拟步骤中会完成
            int nextStepTime = checkTimeUs + TaskUtility.US_STEP;
            int remainingTimeAtCheck = taskAtCheckTime.RemainingTime;
            
            // 如果剩余时间小于等于一个模拟步长，说明任务会在下一步完成，不是抢占
            if (remainingTimeAtCheck <= TaskUtility.US_STEP)
            {
                return false;
            }
            
            // 在检查时间点之后查找该任务是否还会执行
            var futureExecutions = _preemptiveTimeline
                .Where(kvp => kvp.Key > nextStepTime && kvp.Value.TaskName == taskName)
                .Take(1);
            
            bool willResume = futureExecutions.Any();
            return willResume;
        }

        /// <summary>
        /// 根据时间线生成任务执行段（包含jitter调整，基于distributionData，包含正确的层级信息）
        /// </summary>
        private void GenerateTaskSegmentsWithJitter(Dictionary<string, List<TaskSegment>> taskSegments, List<PreemptiveTimePoint> distributionData, List<TaskItem> tasks)
        {
            if (distributionData.Count == 0)
            {
                return;
            }
            
            // 统一/逐任务 jitter 与执行时间参数
            int defaultJitterUs = 0;
            if (tasks != null && tasks.Count > 0)
            {
                defaultJitterUs = tasks[0].JitterTimeUs;
            }
            if (defaultJitterUs <= 0 && _lastSimulationResult != null && _lastSimulationResult.CompletedInstances.Count > 0)
            {
                defaultJitterUs = _lastSimulationResult.CompletedInstances[0].JitterTimeUs;
            }
            var taskExecUs = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
            var taskJitterUs = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
            if (tasks != null)
            {
                foreach (var t in tasks)
                {
                    if (!string.IsNullOrWhiteSpace(t.TaskName))
                    {
                        // 适配：每任务独立jitter；若无输入则回退到默认/模拟值
                        taskExecUs[t.TaskName] = t.ExecutionTimeUs ?? TaskUtility.MIN_EXECUTION_TIME_US;
                        int perTaskJitter = t.JitterTimeUs;
                        if (perTaskJitter <= 0)
                        {
                            perTaskJitter = defaultJitterUs;
                        }
                        // 保证jitter为US_STEP的整数倍
                        perTaskJitter = TaskUtility.EnsureTimeBaseMultiple(perTaskJitter, TaskUtility.US_STEP);
                        taskJitterUs[t.TaskName] = perTaskJitter;
                    }
                }
            }
            // 每个任务当前实例已消耗的执行时间（不含jitter），用于精确补齐最终段
            var consumedExecUs = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);

            // Jitter绘制简化处理完成

            string currentTask = null;
            int currentStartUs = 0;
            int currentLevel = 0;
            int currentStartShiftUs = 0; // 记录当前段相对于模拟时间线起点的推迟量（由上一个段的完成jitter引入）
            int? nextSegmentStartOverrideUs = null; // 当上一个段完成时，下一段应从“完成+尾部jitter”开始

            for (int i = 0; i < distributionData.Count - 1; i++)
            {
                var point = distributionData[i];
                var next = distributionData[i + 1];

                string task = point.ExecutingTask;
                string nextTask = next.ExecutingTask;
                int level = point.PreemptionLevel;

                // 段开始：从空闲或不同任务进入
                if (string.IsNullOrEmpty(currentTask))
                {
                    if (!string.IsNullOrEmpty(task))
                    {
                        currentTask = task;
                        currentStartUs = point.TimeUs;
                        currentLevel = level;
                        currentStartShiftUs = 0;
                    }
                    continue;
                }

                // 正在一段中，检测是否结束
                bool taskSwitched = task != nextTask; // 仅在任务切换时结束段；层级变化不再拆段

                if (taskSwitched)
                {
                    // 结束边界为下一采样点时刻（基础边界），jitter仅归属段尾用于显示
                    int segmentEndUs = next.TimeUs;
                    int finalEndUs = segmentEndUs;
                    int segmentJitterUs = 0;
                    int executedUsThisSegment = 0; // 本段实际执行时间（不含jitter）

                    if (taskSwitched && !string.IsNullOrEmpty(task))
                    {
                        // 判断是抢占还是正常完成
                        bool isPreemption = CheckIfTaskWillResumeExecution(point.TimeUs, task);
                        int jitterUsForTask = taskJitterUs.ContainsKey(task) ? taskJitterUs[task] : defaultJitterUs;
                        int totalExecUsForTask = taskExecUs.ContainsKey(task) ? taskExecUs[task] : TaskUtility.MIN_EXECUTION_TIME_US;
                        int consumedSoFar = consumedExecUs.ContainsKey(task) ? consumedExecUs[task] : 0;
                        if (isPreemption)
                        {
                            // 抢占：最后一个步长视为jitter，不计入执行
                            segmentJitterUs = jitterUsForTask;
                            int baseDuration = Math.Max(0, segmentEndUs - currentStartUs);
                            executedUsThisSegment = Math.Max(0, baseDuration - segmentJitterUs);
                            // 不延长边界，保持 finalEndUs = segmentEndUs
                            nextSegmentStartOverrideUs = null;
                        }
                        else
                        {
                            // 正常完成：本段应补齐剩余执行时间 + jitter
                            int remainingExecUs = Math.Max(0, totalExecUsForTask - consumedSoFar);
                            executedUsThisSegment = remainingExecUs;
                            segmentJitterUs = jitterUsForTask;
                            finalEndUs = TaskUtility.EnsureTimeBaseMultiple(currentStartUs + executedUsThisSegment + segmentJitterUs, TaskUtility.US_STEP);
                            nextSegmentStartOverrideUs = finalEndUs; // 下一段从“完成+jitter”后开始
                            // 完成后重置该任务的已消耗计数，将在下一实例重新累计
                        }
                    }

                    // 输出当前段
                    if (!string.IsNullOrEmpty(currentTask))
                    {
                        List<TaskSegment> list;
                        if (!taskSegments.TryGetValue(currentTask, out list))
                        {
                            list = new List<TaskSegment>();
                            taskSegments[currentTask] = list;
                        }
                        list.Add(new TaskSegment
                        {
                            TaskName = currentTask,
                            StartTimeUs = currentStartUs,
                            EndTimeUs = finalEndUs,
                            PreemptionLevel = currentLevel,
                            JitterTimeUs = segmentJitterUs
                        });
                        // 更新该任务当前实例的执行累计
                        if (!string.IsNullOrEmpty(currentTask))
                        {
                            if (!consumedExecUs.ContainsKey(currentTask))
                            {
                                consumedExecUs[currentTask] = 0;
                            }
                            consumedExecUs[currentTask] += executedUsThisSegment;
                            // 若是正常完成，下一段属于新实例，重置累计
                            if (nextSegmentStartOverrideUs.HasValue)
                            {
                                consumedExecUs[currentTask] = 0;
                            }
                        }
                    }

                    // 开启下一段（若下一个时刻有任务）。
                    // 起始点严格使用 next.TimeUs，确保恢复段从上一个任务的“结束（含jitter后的显示边界）”处开始。
                    if (!string.IsNullOrEmpty(nextTask))
                    {
                        currentTask = nextTask;
                        currentStartUs = nextSegmentStartOverrideUs.HasValue ? nextSegmentStartOverrideUs.Value : next.TimeUs;
                        currentStartShiftUs = nextSegmentStartOverrideUs.HasValue ? (nextSegmentStartOverrideUs.Value - next.TimeUs) : 0;
                        currentLevel = next.PreemptionLevel;
                        nextSegmentStartOverrideUs = null; // 只影响紧邻的下一段
                }
                else
                {
                        currentTask = null;
                        nextSegmentStartOverrideUs = null;
                    }
                }
            }

            // 收尾：如果最后仍在执行某个任务，补一段到末尾（不追加jitter，因未真正完成）
            var lastPoint = distributionData.Last();
            if (!string.IsNullOrEmpty(currentTask))
            {
                List<TaskSegment> list;
                if (!taskSegments.TryGetValue(currentTask, out list))
                {
                    list = new List<TaskSegment>();
                    taskSegments[currentTask] = list;
                }
                list.Add(new TaskSegment
                {
                    TaskName = currentTask,
                    StartTimeUs = currentStartUs,
                    EndTimeUs = lastPoint.TimeUs + TaskUtility.US_STEP,
                    PreemptionLevel = currentLevel,
                    JitterTimeUs = 0
                });
            }

            // 调试输出
            //Console.WriteLine("📊 [Jitter绘制简化] 生成任务段完成:");
            //foreach (var kvp in taskSegments)
            //{
            //    var name = kvp.Key;
            //    var segs = kvp.Value;
            //    Console.WriteLine($"   {name}: {segs.Count}段");
            //    foreach (var seg in segs.Take(3))
            //    {
            //        Console.WriteLine($"     {TaskUtility.ConvertToMilliseconds(seg.StartTimeUs):F3}-{TaskUtility.ConvertToMilliseconds(seg.EndTimeUs):F3}ms (L{seg.PreemptionLevel}, jitter={TaskUtility.ConvertToMilliseconds(seg.JitterTimeUs):F3}ms)");
            //    }
            //    if (segs.Count > 3)
            //    {
            //        Console.WriteLine($"     ... 还有{segs.Count - 3}段");
            //    }
            //}
        }
        
        /// <summary>
        /// 根据时间线生成任务执行段（基于distributionData，包含正确的层级信息）
        /// </summary>
        private void GenerateTaskSegments(Dictionary<string, List<TaskSegment>> taskSegments, List<PreemptiveTimePoint> distributionData)
        {
            if (distributionData.Count == 0) return;
            
            string currentTaskName = null;
            int segmentStartTime = 0;
            int currentPreemptionLevel = 0;
            
            foreach (var point in distributionData)
            {
                string taskName = point.ExecutingTask;
                int timeUs = point.TimeUs;
                int preemptionLevel = point.PreemptionLevel;
                
                // 跳过空闲时间段，只处理实际任务执行段
                if (string.IsNullOrEmpty(taskName))
                {
                    // 如果当前有任务正在执行，结束该段
                    if (currentTaskName != null && !string.IsNullOrEmpty(currentTaskName))
                    {
                        if (!taskSegments.ContainsKey(currentTaskName))
                            taskSegments[currentTaskName] = new List<TaskSegment>();
                        
                        taskSegments[currentTaskName].Add(new TaskSegment
                        {
                            TaskName = currentTaskName,
                            StartTimeUs = segmentStartTime,
                            EndTimeUs = timeUs,
                            PreemptionLevel = currentPreemptionLevel
                        });
                        currentTaskName = null;
                    }
                    continue;
                }
                
                // 检查任务或层级是否发生切换
                if (currentTaskName != taskName || currentPreemptionLevel != preemptionLevel)
                {
                    // 结束前一个段
                    if (currentTaskName != null && !string.IsNullOrEmpty(currentTaskName))
                    {
                        if (!taskSegments.ContainsKey(currentTaskName))
                            taskSegments[currentTaskName] = new List<TaskSegment>();
                        
                        taskSegments[currentTaskName].Add(new TaskSegment
                        {
                            TaskName = currentTaskName,
                            StartTimeUs = segmentStartTime,
                            EndTimeUs = timeUs,
                            PreemptionLevel = currentPreemptionLevel
                        });
                    }
                    
                    // 开始新的段
                    currentTaskName = taskName;
                    segmentStartTime = timeUs;
                    currentPreemptionLevel = preemptionLevel;
                    
                    // 确保新任务在字典中存在
                    if (!taskSegments.ContainsKey(taskName))
                        taskSegments[taskName] = new List<TaskSegment>();
                }
            }
            
            // 处理最后一个段
            if (currentTaskName != null && !string.IsNullOrEmpty(currentTaskName))
            {
                var lastPoint = distributionData.Last();
                if (!taskSegments.ContainsKey(currentTaskName))
                    taskSegments[currentTaskName] = new List<TaskSegment>();
                    
                taskSegments[currentTaskName].Add(new TaskSegment
                {
                    TaskName = currentTaskName,
                    StartTimeUs = segmentStartTime,
                    EndTimeUs = lastPoint.TimeUs + TaskUtility.US_STEP, // 使用高精度步长
                    PreemptionLevel = currentPreemptionLevel
                });
            }
            
            // 调试输出段信息
            Console.WriteLine("📊 [OffsetValidator] 生成任务段完成:");
            foreach (var kvp in taskSegments)
            {
                string taskName = kvp.Key;
                var segments = kvp.Value;
                Console.WriteLine($"   {taskName}: {segments.Count}个段");
                foreach (var segment in segments.Take(3)) // 只显示前3个段
                {
                    Console.WriteLine($"     {TaskUtility.ConvertToMilliseconds(segment.StartTimeUs):F2}-{TaskUtility.ConvertToMilliseconds(segment.EndTimeUs):F2}ms (层级{segment.PreemptionLevel})");
                }
                if (segments.Count > 3)
                {
                    Console.WriteLine($"     ... 还有{segments.Count - 3}个段");
                }
            }
        }

        /// <summary>
        /// 分析调度结果并生成验证结果
        /// </summary>
        private ValidationResult AnalyzeSchedulingResults(List<TaskItem> tasks, 
            SchedulabilityResult schedulabilityResult, PreemptiveSimulationResult simulationResult)
        {
            var result = new ValidationResult
            {
                TimeBaseUs = _timeBaseUs
            };
            
            // 1. 基本统计信息
            result.TotalTimePoints = _hyperPeriod / _timeBaseUs;
            result.UsedTimePoints = simulationResult.TimelineSize;
            
            // 使用SchedulabilityAnalyzer的CPU利用率计算结果（确保一致性）
            result.CpuUsage = schedulabilityResult.Statistics?.CpuUsage ?? 0;
            
            // 2. 抢占统计
            // 计算抢占比例：发生抢占的任务实例数 / 总任务实例数
            int totalTaskInstances = simulationResult.CompletedInstances.Count;
            
            if (totalTaskInstances > 0)
            {
                // 统计哪些任务实例经历了抢占（被抢占）
                var preemptedInstances = new HashSet<int>();
                
                // 为每个抢占事件找到对应的被抢占任务实例
                foreach (var preemption in simulationResult.PreemptionEvents)
                {
                    // 在抢占时间点查找正在执行的任务实例
                    var preemptedInstance = simulationResult.CompletedInstances
                        .Where(inst => inst.TaskName == preemption.PreemptedTask && 
                               inst.ArrivalTime <= preemption.Time && 
                               inst.CompletionTime >= preemption.Time)
                        .FirstOrDefault();
                    
                    if (preemptedInstance != null)
                    {
                        preemptedInstances.Add(preemptedInstance.InstanceId);
                    }
                }
                
                // 计算抢占比例
                result.PreemptionRatio = (double)preemptedInstances.Count / totalTaskInstances * 100;
            }
            else
            {
                result.PreemptionRatio = 0;
            }
            result.PreemptionEvents = simulationResult.PreemptionEvents.Take(10).ToList(); // 显示前10个
            
            // 3. 检查截止时间违反（简化版本）
            var deadlineMisses = new List<DeadlineMiss>();
            
            // 按任务分组检查截止时间
            var taskGroups = simulationResult.CompletedInstances.GroupBy(i => i.TaskName);
            
            foreach (var group in taskGroups)
            {
                var instances = group.ToList();
                var task = tasks.FirstOrDefault(t => t.TaskName == group.Key);
                if (task != null)
                {
                    int periodUs = task.PeriodUs.Value;
                    var maxResponseTime = instances.Max(i => i.ResponseTime);
                    
                    // 检查截止时间违反
                    var missedInstances = instances.Where(i => i.ResponseTime > periodUs).ToList();
                    if (missedInstances.Count > 0)
                    {
                        deadlineMisses.Add(new DeadlineMiss
                        {
                            TaskName = group.Key,
                            Period = periodUs,
                            ExecutionTime = task.ExecutionTimeUs ?? TaskUtility.MIN_EXECUTION_TIME_US,
                            TimeDifference = maxResponseTime - periodUs,
                            MissedInstances = missedInstances.Count,
                            TotalInstances = instances.Count,
                            TimeBaseUs = _timeBaseUs
                        });
                    }
                }
            }
            
            result.DeadlineMisses = deadlineMisses;
            
            // 4. 可调度性结果
            result.IsSchedulable = schedulabilityResult.IsSchedulable;
            result.CostValue = schedulabilityResult.CostValue;
            
            // 调试输出：确保可调度性判定正确
            //Console.WriteLine($"🔍 [OffsetValidator] CPU利用率: {result.CpuUsage:F2}%, 可调度性: {(result.IsSchedulable ? "✓ 可调度" : "✗ 不可调度")}");
            
            // 5. 设置整体验证状态
            bool hasDeadlineMisses = deadlineMisses.Count > 0;
            bool hasHighCost = schedulabilityResult.CostValue > 1.0;
            
            if (hasDeadlineMisses)
            {
                result.IsValid = false;
                result.Message = $"发现{deadlineMisses.Count}个任务有截止时间违反";
            }
            else if (hasHighCost)
            {
                result.IsValid = false;
                result.Message = $"系统成本过高({schedulabilityResult.CostValue:F3})，可能存在调度风险";
            }
            else if (!result.IsSchedulable)
            {
                result.IsValid = false;
                result.Message = "系统不可调度";
            }
            else
            {
                result.IsValid = true;
                result.Message = "所有任务调度验证通过";
            }
            
            return result;
        }
        
        /// <summary>
        /// 生成完整的验证报告
        /// </summary>
        public string GenerateValidationReport(ValidationResult result)
        {
            StringBuilder sb = new StringBuilder();
            
            sb.AppendLine("====== 偏移量校验报告 ======");
            sb.AppendLine();
            
            // 添加时基信息
            sb.AppendLine($"时基: {TaskUtility.ConvertToMilliseconds(result.TimeBaseUs):F3}ms");
            sb.AppendLine();
            
            // 计算总时间
            int totalTimeUs = result.TotalTimePoints * result.TimeBaseUs;
            double totalTimeMs = TaskUtility.ConvertToMilliseconds(totalTimeUs);
            string totalTimeStr = FormatTimeDisplay(totalTimeMs);
            
            // 基本统计信息
            sb.AppendLine($"仿真时间: {totalTimeStr}");
            sb.AppendLine($"CPU利用率: {result.CpuUsage:F2}%");
            sb.AppendLine($"抢占比例: {result.PreemptionRatio:F4}%");
            sb.AppendLine($"可调度性: {(result.IsSchedulable ? "✓ 可调度" : "✗ 不可调度")}");
            sb.AppendLine($"系统成本: {result.CostValue:F3}");
            sb.AppendLine();
            
            // 抢占事件（简化显示）
            if (result.PreemptionEvents.Count > 0)
            {
                sb.AppendLine($"抢占事件（显示前{Math.Min(result.PreemptionEvents.Count, 5)}个，共{result.PreemptionEvents.Count}个）:");
                foreach (var preemption in result.PreemptionEvents.Take(5))
                {
                    string timeDisplay = preemption.GetFormattedTime();
                    sb.AppendLine($"- {timeDisplay}: {preemption.PreemptingTask} 抢占 {preemption.PreemptedTask}");
                }
                sb.AppendLine();
            }
            
            // 截止时间违反
            if (result.DeadlineMisses.Count > 0)
            {
                sb.AppendLine("截止时间违反:");
                foreach (var miss in result.DeadlineMisses)
                {
                    string periodStr = miss.GetFormattedPeriod();
                    string diffStr = miss.GetFormattedTimeDifference();
                    
                    sb.AppendLine($"- 任务 {miss.TaskName}: 周期({periodStr}), 超时 {diffStr}");
                    sb.AppendLine($"  违反实例: {miss.MissedInstances}/{miss.TotalInstances}");
                }
                sb.AppendLine();
            }
            
            // 最终结论
            sb.AppendLine("验证结论:");
            if (result.IsValid)
            {
                sb.AppendLine("✓ 偏移量校验通过，所有任务满足调度要求");
            }
            else
            {
                sb.AppendLine($"✗ {result.Message}");
            }
            
            return sb.ToString();
        }
        
        /// <summary>
        /// 格式化时间显示
        /// </summary>
        private string FormatTimeDisplay(double timeMs)
        {
            if (timeMs >= 60000) // >= 1分钟
            {
                double minutes = timeMs / 60000.0;
                return $"{minutes:F2}分钟 ({timeMs:F3}ms)";
            }
            else if (timeMs >= 1000) // >= 1秒
            {
                double seconds = timeMs / 1000.0;
                return $"{seconds:F3}秒 ({timeMs:F3}ms)";
            }
            else
            {
                return $"{timeMs:F3}ms";
            }
        }
    }
    
    /// <summary>
    /// 验证结果类
    /// </summary>
    public class ValidationResult
    {
        // 验证是否通过
        public bool IsValid { get; set; }
        
        // 验证消息
        public string Message { get; set; }
        
        // 总时间点数
        public int TotalTimePoints { get; set; }
        
        // 使用的时间点数
        public int UsedTimePoints { get; set; }
        
        // CPU使用率
        public double CpuUsage { get; set; }
        
        // 抢占事件列表
        public List<PreemptionEvent> PreemptionEvents { get; set; } = new List<PreemptionEvent>();
        
        // 截止时间错误列表
        public List<DeadlineMiss> DeadlineMisses { get; set; } = new List<DeadlineMiss>();
        
        // 时基(微秒)
        public int TimeBaseUs { get; set; }
        
        // 抢占比例 (发生抢占的任务实例数 / 总任务实例数)
        public double PreemptionRatio { get; set; }
        
        // 可调度性
        public bool IsSchedulable { get; set; }
        
        // 系统成本
        public double CostValue { get; set; }

        /// <summary>
        /// 统一挑选阶段的额外指标（供下游展示/HTML复用，避免重复计算）
        /// </summary>
        public SelectionExtraMetrics ExtraMetrics { get; set; }
    }
    
    /// <summary>
    /// 截止时间错误类
    /// </summary>
    public class DeadlineMiss
    {
        // 任务名称
        public string TaskName { get; set; }
        
        // 周期(微秒)
        public int Period { get; set; }
        
        // 执行时间(微秒)
        public int ExecutionTime { get; set; }
        
        // 时间差(执行时间-周期，微秒)
        public int TimeDifference { get; set; }
        
        // 时基值(微秒)
        public int TimeBaseUs { get; set; }
        
        // 违反实例数
        public int MissedInstances { get; set; }
        
        // 总实例数
        public int TotalInstances { get; set; }
        
        /// <summary>
        /// 获取格式化后的周期值
        /// </summary>
        public string GetFormattedPeriod()
        {
            return FormatTimeValue(Period);
        }
        
        /// <summary>
        /// 获取格式化后的执行时间值
        /// </summary>
        public string GetFormattedExecutionTime()
        {
            return FormatTimeValue(ExecutionTime);
        }
        
        /// <summary>
        /// 获取格式化后的时间差值
        /// </summary>
        public string GetFormattedTimeDifference()
        {
            return FormatTimeValue(TimeDifference);
        }
        
        /// <summary>
        /// 格式化时间值，确保是时基的整数倍
        /// </summary>
        private string FormatTimeValue(int timeUs)
        {
            // 确保微秒值是时基的整数倍
            timeUs = TaskUtility.EnsureTimeBaseMultiple(timeUs, TimeBaseUs);
            
            if (timeUs >= 1000)
            {
                // 转换为毫秒并保持三位小数精度
                double msTime = TaskUtility.ConvertToMilliseconds(timeUs);
                return $"{msTime:F3}ms ({timeUs}us)";
            }
            return $"{timeUs}us";
        }
    }
    
    /// <summary>
    /// 任务实例类（用于调度模拟）
    /// </summary>
    public class TaskInstance
    {
        public int InstanceId { get; set; }
        public string TaskName { get; set; }
        public int Priority { get; set; }
        public int ArrivalTime { get; set; }
        public int ExecutionTime { get; set; }
        public int RemainingExecutionTime { get; set; }
        public int Deadline { get; set; }
        public int Period { get; set; }
        public int CompletionTime { get; set; }
        public int ResponseTime { get; set; }
        
        // 抖动相关字段
        public int JitterTimeUs { get; set; }          // 单次任务切换的jitter时间
        public int AccumulatedJitterUs { get; set; }   // 累计的jitter开销
        public int ContextSwitchCount { get; set; }    // 上下文切换次数
        public bool HasStarted { get; set; }           // 是否已经开始执行过
            public int PreemptionCount { get; set; }       // 被抢占次数（新规则）
    }
    
    /// <summary>
    /// 正在执行的任务信息
    /// </summary>
    public class ExecutingTask
    {
        public string TaskName { get; set; }
        public int Priority { get; set; }
        public int InstanceId { get; set; }
        public int RemainingTime { get; set; }
    }
    
    /// <summary>
    /// 抢占事件类
    /// </summary>
    public class PreemptionEvent
    {
        public int Time { get; set; }
        public string PreemptedTask { get; set; }
        public string PreemptingTask { get; set; }
        public int TimeBaseUs { get; set; }
        
        /// <summary>
        /// 获取格式化的时间显示
        /// </summary>
        public string GetFormattedTime()
        {
            return FormatTimeValue(Time);
        }
        
        private string FormatTimeValue(int timeUs)
        {
            // 确保微秒值是时基的整数倍
            timeUs = TaskUtility.EnsureTimeBaseMultiple(timeUs, TimeBaseUs);
            
            if (timeUs >= 1000)
            {
                double msTime = TaskUtility.ConvertToMilliseconds(timeUs);
                return $"{msTime:F3}ms ({timeUs}us)";
            }
            return $"{timeUs}us";
        }
    }
    
    /// <summary>
    /// 抢占式模拟结果
    /// </summary>
    public class PreemptiveSimulationResult
    {
        public List<TaskInstance> CompletedInstances { get; set; } = new List<TaskInstance>();
        public List<PreemptionEvent> PreemptionEvents { get; set; } = new List<PreemptionEvent>();
        public int TotalSimulationTime { get; set; }
        public int TimelineSize { get; set; }
    }

    /// <summary>
    /// 详细时间线数据，供TimelineGenerator复用
    /// </summary>
    public class DetailedTimelineData
    {
        /// <summary>
        /// 时间点分布数据
        /// </summary>
        public List<PreemptiveTimePoint> DistributionData { get; set; } = new List<PreemptiveTimePoint>();

        /// <summary>
        /// 任务执行段数据
        /// </summary>
        public Dictionary<string, List<TaskSegment>> TaskSegments { get; set; } = new Dictionary<string, List<TaskSegment>>();

        /// <summary>
        /// 超周期长度（微秒）
        /// </summary>
        public int HyperPeriodUs { get; set; }

        /// <summary>
        /// 时基（微秒）
        /// </summary>
        public int TimeBaseUs { get; set; }
    }

    /// <summary>
    /// 抢占式时间点
    /// </summary>
    public class PreemptiveTimePoint
    {
        /// <summary>
        /// 时间点（微秒）
        /// </summary>
        public int TimeUs { get; set; }

        /// <summary>
        /// 正在执行的任务名称，null表示空闲
        /// </summary>
        public string ExecutingTask { get; set; }

        /// <summary>
        /// 抢占级别（优先级），0表示空闲
        /// </summary>
        public int PreemptionLevel { get; set; }
    }

    /// <summary>
    /// 任务执行段
    /// </summary>
    public class TaskSegment
    {
        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; set; }

        /// <summary>
        /// 段开始时间（微秒）
        /// </summary>
        public int StartTimeUs { get; set; }

        /// <summary>
        /// 段结束时间（微秒）
        /// </summary>
        public int EndTimeUs { get; set; }

        /// <summary>
        /// 抢占层级（0=基础层级，数字越大层级越高）
        /// </summary>
        public int PreemptionLevel { get; set; }

        /// <summary>
        /// 该段包含的jitter时间（微秒）
        /// </summary>
        public int JitterTimeUs { get; set; } = 0;
    }
} 
