using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CalculateOffset
{
    /// <summary>
    /// 智能调度引擎 - 第三层：执行层和智能决策引擎
    /// 实现渐进式最大Offset搜索策略和双轨并行优化
    /// </summary>
    public class IntelligentSchedulingEngine
    {
        #region 私有字段

        private readonly int _timeBaseUs;
        private readonly SchedulabilityAnalyzer _analyzer;
        private static readonly int _staticTimeBaseUs = TaskUtility.MIN_TIME_BASE_US;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="timeBaseUs">时基值(微秒)</param>
        public IntelligentSchedulingEngine(int timeBaseUs)
        {
            _timeBaseUs = timeBaseUs;
            _analyzer = new SchedulabilityAnalyzer(timeBaseUs);
        }

        #endregion

        #region 主要接口

        /// <summary>
        /// 执行智能调度优化 - 渐进式最大Offset搜索
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="maxOffsetUs">用户设定的最大Offset值(微秒)</param>
        /// <param name="parameters">优化参数</param>
        /// <returns>智能调度结果</returns>
        public IntelligentSchedulingResult RunIntelligentScheduling(List<TaskItem> tasks, 
            int maxOffsetUs, IntelligentSchedulingParameters parameters = null)
        {
            parameters = parameters ?? new IntelligentSchedulingParameters();
            var result = new IntelligentSchedulingResult
            {
                StartTime = DateTime.Now,
                OriginalMaxOffsetUs = maxOffsetUs,
                Parameters = parameters
            };

            try
            {
                // 生成渐进式Offset测试序列
                var offsetSequence = GenerateOffsetSequence(maxOffsetUs);
                result.OffsetSequence = offsetSequence;

                // 预检查：在第一个（最大）offset值下测试启发式种子生成是否可行
                if (offsetSequence.Count > 0)
                {
                    var firstOffsetValue = offsetSequence[0];
                    var preCheckResult = CheckHeuristicSeedFeasibility(tasks, firstOffsetValue);
                    
                    if (!preCheckResult.Success)
                    {
                        // 启发式种子生成失败，直接返回错误
                        result.Success = false;
                        result.ErrorMessage = preCheckResult.ErrorMessage;
                        result.TotalTime = DateTime.Now - result.StartTime;
                        //Console.WriteLine($"❌ [智能调度预检查] {preCheckResult.ErrorMessage}");
                        return result;
                    }
                    
                    //Console.WriteLine($"✅ [智能调度预检查] 在最大offset({TaskUtility.ConvertToMilliseconds(firstOffsetValue):F3}ms)下启发式种子生成可行，开始渐进式搜索");
                }

                // 对每个Offset值执行双轨优化
                foreach (var offsetValue in offsetSequence)
                {
                    var stepResult = ExecuteDualTrackOptimization(tasks, offsetValue, parameters);
                    result.StepResults.Add(stepResult);

                    // 检查是否所有多起点策略都失败（offset限制过小）
                    if (!stepResult.BestResult.Success && 
                        stepResult.BestResult.ErrorMessage != null &&
                        stepResult.BestResult.ErrorMessage.Contains("当前offset限制") &&
                        stepResult.BestResult.ErrorMessage.Contains("过小"))
                    {
                        //Console.WriteLine($"⚠️ [智能调度] offset限制({TaskUtility.ConvertToMilliseconds(offsetValue):F3}ms)过小，所有启发式策略都失败，停止尝试更低的offset值");
                        result.EarlyStoppedAt = offsetValue;
                        break;
                    }

                    // 智能早停策略：平衡性能搜索和计算效率
                    if (parameters.UseEarlyStoppingForPerfectSolution)
                    {
                        // 策略1：连续失败表明已接近最小可行Offset下限
                        var recentResults = result.StepResults.Skip(Math.Max(0, result.StepResults.Count - 3)).ToList();
                        if (recentResults.Count >= 3 && recentResults.All(r => !r.BestResult.Success))
                        {
                            result.EarlyStoppedAt = offsetValue;
                            break;
                        }
                        
                        // 策略2：如果已经有足够的测试数据（至少5个步骤）且性能不再改善
                        if (result.StepResults.Count >= 5)
                        {
                            var successfulResults = result.StepResults
                                .Where(r => r.BestResult.Success)
                                .ToList();
                                
                            if (successfulResults.Count >= 3)
                            {
                                // 检查最近3个成功结果的性能是否趋于稳定
                                var recentSuccessful = successfulResults.Skip(Math.Max(0, successfulResults.Count - 3)).ToList();
                                var maxRatios = recentSuccessful.Select(r => 
                                    r.BestResult.SchedulabilityResult?.Statistics?.MaxResponseTimeRatio ?? 1.0).ToList();
                                    
                                var maxVariation = maxRatios.Max() - maxRatios.Min();
                                
                                // 如果性能变化很小（<1%）且当前步骤失败，可以停止
                                if (maxVariation < 0.01 && !stepResult.BestResult.Success)
                                {
                                    result.EarlyStoppedAt = offsetValue;
                                    break;
                                }
                            }
                        }
                    }
                }

                // 从所有结果中选择最优解
                result.FinalBestResult = SelectGlobalBestSolution(result.StepResults, result.OriginalMaxOffsetUs);
                result.OptimizationDecision = GenerateOptimizationDecision(result);

                result.Success = true;
                result.TotalTime = DateTime.Now - result.StartTime;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = $"智能调度优化失败: {ex.Message}";
                result.TotalTime = DateTime.Now - result.StartTime;
            }

            return result;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 生成渐进式Offset测试序列
        /// </summary>
        /// <param name="maxOffsetUs">用户设定的最大Offset值(微秒)</param>
        /// <returns>Offset测试序列（严格不超过用户设定的最大值，微秒）</returns>
        private List<int> GenerateOffsetSequence(int maxOffsetUs)
        {
            var sequence = new List<int>();

            // 确保不超过用户设定的最大值
            int userMaxOffsetUs = maxOffsetUs;
            
            // 第一阶段：从用户设定的maxOffset开始，按10ms(10000us)递减到10ms（但不超过用户限制）
            for (int offsetUs = userMaxOffsetUs; offsetUs >= 10000; offsetUs -= 10000)
            {
                sequence.Add(offsetUs);
            }

            // 第二阶段：从min(10ms, userMaxOffset)开始，按1ms(1000us)递减到1ms（但不超过用户限制）
            int secondPhaseStart = Math.Min(10000, userMaxOffsetUs);
            for (int offsetUs = secondPhaseStart; offsetUs >= 1000; offsetUs -= 1000)
            //for (int offsetUs = secondPhaseStart; offsetUs >= 100000; offsetUs -= 1000)
            {
                sequence.Add(offsetUs);
            }

            // 去重、过滤超出用户限制的值，并排序（从大到小）
            return sequence.Distinct()
                          .Where(x => x <= userMaxOffsetUs)
                          .OrderByDescending(x => x)
                          .ToList();
        }

        /// <summary>
        /// 执行双轨并行优化
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="maxOffsetUs">当前最大Offset值(微秒)</param>
        /// <param name="parameters">参数</param>
        /// <returns>双轨优化结果</returns>
        private DualTrackOptimizationResult ExecuteDualTrackOptimization(List<TaskItem> tasks, 
            int maxOffsetUs, IntelligentSchedulingParameters parameters)
        {
            var result = new DualTrackOptimizationResult
            {
                MaxOffsetUs = maxOffsetUs,
                StartTime = DateTime.Now
            };

            // **优化：预计算共享启发式种子，避免重复OffsetCalculator调用**
            SharedHeuristicSeeds sharedSeeds = null;
            try
            {
                sharedSeeds = PrecomputeSharedHeuristicSeeds(tasks, maxOffsetUs);
                //Console.WriteLine($"📦 [共享种子] 成功预计算启发式种子，避免SA和GA重复调用");
            }
            catch (Exception ex)
            {
                //Console.WriteLine($"⚠️ [共享种子] 预计算失败: {ex.Message}，回退到独立计算模式");
                sharedSeeds = null;
            }

            // 克隆任务列表用于并行优化
            var tasksForSA = CloneTaskList(tasks);
            var tasksForGA = CloneTaskList(tasks);

            // 并行执行两种算法，传递共享种子
            var saTask = Task.Run(() => RunSimulatedAnnealingWithSharedSeeds(tasksForSA, maxOffsetUs, parameters, sharedSeeds));
            var gaTask = Task.Run(() => RunGeneticAlgorithmWithSharedSeeds(tasksForGA, maxOffsetUs, parameters, sharedSeeds));

            // 等待两个算法完成
            Task.WaitAll(saTask, gaTask);

            result.SimulatedAnnealingResult = saTask.Result;
            result.GeneticAlgorithmResult = gaTask.Result;

            // 新逻辑：收集所有策略结果到统一集合
            var allStrategyCandidates = new List<StrategyCandidateResult>();

            // 收集SA的所有策略结果
            if (result.SimulatedAnnealingResult.Success)
            {
                if (result.SimulatedAnnealingResult.MultiStartDetails?.StrategyResults != null)
                {
                    // 多起点SA - 收集所有策略结果
                    foreach (var strategyResult in result.SimulatedAnnealingResult.MultiStartDetails.StrategyResults)
                    {
                        if (strategyResult.Success)
                        {
                            allStrategyCandidates.Add(new StrategyCandidateResult
                            {
                                Algorithm = "SimulatedAnnealing",
                                Strategy = strategyResult.Strategy.ToString(),
                                Cost = strategyResult.FinalCost,
                                Solution = strategyResult.FinalSolution,
                                OriginalResult = result.SimulatedAnnealingResult,
                                SourceStrategyResult = strategyResult
                            });
                        }
                    }
                }
                else
                {
                    // 单起点SA
                    var solution = ExtractOffsetSolution(result.SimulatedAnnealingResult);
                    if (solution.Count > 0)
                    {
                        allStrategyCandidates.Add(new StrategyCandidateResult
                        {
                            Algorithm = "SimulatedAnnealing", 
                            Strategy = "SingleStart",
                            Cost = result.SimulatedAnnealingResult.BestCost,
                            Solution = solution,
                            OriginalResult = result.SimulatedAnnealingResult
                        });
                    }
                }
            }

            // 收集GA的所有策略结果
            if (result.GeneticAlgorithmResult.Success)
            {
                if (result.GeneticAlgorithmResult.MultiStartDetails?.StrategyResults != null)
                {
                    // 多起点GA - 收集所有策略结果
                    foreach (var strategyResult in result.GeneticAlgorithmResult.MultiStartDetails.StrategyResults)
                    {
                        if (strategyResult.Success)
                        {
                            allStrategyCandidates.Add(new StrategyCandidateResult
                            {
                                Algorithm = "GeneticAlgorithm",
                                Strategy = strategyResult.Strategy.ToString(),
                                Cost = strategyResult.FinalCost,
                                Solution = strategyResult.FinalSolution,
                                OriginalResult = result.GeneticAlgorithmResult,
                                SourceStrategyResult = strategyResult
                            });
                        }
                    }
                }
                else
                {
                    // 单起点GA
                    var solution = ExtractOffsetSolution(result.GeneticAlgorithmResult);
                    if (solution.Count > 0)
                    {
                        allStrategyCandidates.Add(new StrategyCandidateResult
                        {
                            Algorithm = "GeneticAlgorithm",
                            Strategy = "SingleStart", 
                            Cost = result.GeneticAlgorithmResult.BestCost,
                            Solution = solution,
                            OriginalResult = result.GeneticAlgorithmResult
                        });
                    }
                }
            }

            // 从统一集合中选择最优解
            if (allStrategyCandidates.Count > 0)
            {
                var selectedCandidate = SelectBestFromUnifiedCandidates(allStrategyCandidates);
                result.BestResult = selectedCandidate.OriginalResult;
                
                // 更新算法名称以反映选择的策略
                if (selectedCandidate.OriginalResult.MultiStartDetails != null)
                {
                    result.BestResult.Algorithm = $"{selectedCandidate.Algorithm}({selectedCandidate.Strategy})";
                }
                else
                {
                    result.BestResult.Algorithm = selectedCandidate.Algorithm;
                }
            }
            else
            {
                // 如果没有成功的候选结果，回退到原逻辑
                result.BestResult = SelectBestSolution(result.SimulatedAnnealingResult, result.GeneticAlgorithmResult);
            }

            result.Decision = GenerateDecisionReason(result);
            result.TotalTime = DateTime.Now - result.StartTime;
            return result;
        }

        /// <summary>
        /// 从统一的候选结果集合中选择最优解
        /// 新的选择逻辑：优先冲突率 -> 再均值和方差
        /// </summary>
        private StrategyCandidateResult SelectBestFromUnifiedCandidates(List<StrategyCandidateResult> candidates)
        {
            if (candidates.Count == 1)
                return candidates.First();

            //Console.WriteLine($"📊 [统一选择] 收集到 {candidates.Count} 个候选策略结果");

            // 第一步：找到最佳成本
            var bestCost = candidates.Min(c => c.Cost);

            // 引入微小容差，允许几乎相等的成本同时进入候选，避免因为浮点误差错过等价解
            const double COST_TOLERANCE = 1e-6;

            // 第二步：筛选成本最佳的候选结果（带容差）
            var bestCostCandidates = candidates
                .Where(c => Math.Abs(c.Cost - bestCost) <= COST_TOLERANCE)
                .ToList();

            //Console.WriteLine($"📊 [统一选择] 最佳成本: {bestCost:F6}");
            //Console.WriteLine($"📊 [统一选择] 最佳成本候选结果: {bestCostCandidates.Count} 个");

            if (bestCostCandidates.Count == 1)
            {
                var selected = bestCostCandidates.First();
                //Console.WriteLine($"🎯 [统一选择] 唯一最佳: {selected.Algorithm}({selected.Strategy}), 成本={selected.Cost:F6}");
                return selected;
            }

            // 使用新的统一选择逻辑
            return SelectBestByNewCriteria(bestCostCandidates);
        }

        /// <summary>
        /// 统一的最优解选择逻辑（临时移除均值/方差的影响）
        /// 1. 首要：动态忙比例（越小越好）
        /// 2. 次要：启动重叠率（越小越好）
        /// 3. 其后：最大offset（越小越好）→ 静态重叠率（越小越好）→ 冲突率（越小越好）
        /// 4. 最后：算法/策略（稳定性）
        /// </summary>
        public static StrategyCandidateResult SelectBestByNewCriteria(List<StrategyCandidateResult> candidates)
        {
            if (candidates.Count == 1)
                return candidates.First();

            // 计算每个候选结果的统计信息、冲突率、重叠率与启动重叠率
            var candidatesWithAnalysis = candidates.Select(c => new
            {
                Candidate = c,
                Stats = CalculateOffsetStatistics(c.Solution),
                ConflictRate = CalculateConflictRate(c.Solution),
                OverlapRate = CalculateOverlapRate(c.Solution, c.OriginalResult?.OptimizedTasks),
                StartOverlapRate = (c.OriginalResult?.OptimizedTasks != null)
                    ? SchedulabilityAnalyzer.ComputeStartOverlapRateForTasks(c.OriginalResult.OptimizedTasks)
                    : 0.0,
                PriorityAlignmentCost = CalculatePriorityAlignmentCostForTasks(c.OriginalResult?.SchedulabilityResult?.TaskResults),
                // 动态启动忙碌比例（基于抢占式时间线）
                DynamicBusyAtStartRatio = CalculateDynamicBusyAtStartRatioStatic(_staticTimeBaseUs, c.OriginalResult?.OptimizedTasks),
                StartClustering = CalculateStartClusteringScore(_staticTimeBaseUs, c.OriginalResult?.OptimizedTasks, out int peak, out double variance),
                PeakStartConcurrency = peak,
                StartConcurrencyVariance = variance,
                MaxOffsetUs = (c.OriginalResult?.OptimizedTasks != null && c.OriginalResult.OptimizedTasks.Count > 0)
                    ? c.OriginalResult.OptimizedTasks.Where(t => t.OffsetUs.HasValue).Select(t => t.OffsetUs.Value).DefaultIfEmpty(0).Max()
                    : (c.Solution != null && c.Solution.Count > 0 ? c.Solution.Values.Max() : 0)
            }).ToList();

            // 将计算出的指标回填缓存，便于Form1/HTML复用
            foreach (var item in candidatesWithAnalysis)
            {
                if (item.Candidate != null)
                {
                    item.Candidate.ExtraMetrics = new SelectionExtraMetrics
                    {
                        ConflictRate = item.ConflictRate,
                        StaticOverlapRate = item.OverlapRate,
                        StartOverlapRate = item.StartOverlapRate,
                        DynamicBusyAtStartRatio = item.DynamicBusyAtStartRatio,
                        MaxOffsetUs = item.MaxOffsetUs,
                        PriorityAlignmentCost = item.PriorityAlignmentCost,
                        StartClusteringScore = item.StartClustering,
                        PeakStartConcurrency = item.PeakStartConcurrency,
                        StartConcurrencyVariance = item.StartConcurrencyVariance
                    };

                    // 若来源于多起点策略，尽量挂回到策略结果对象（仅用于调试/报告）
                    if (item.Candidate.SourceStrategyResult != null)
                    {
                        // 暂不写回，保留在候选缓存中即可
                    }
                }
            }

            // 新的排序逻辑（不使用均值/方差）：
            // 1. 动态忙比例（升序，越小越好）
            // 2. 启动聚集度（升序，越小越好）
            // 3. 启动重叠率（升序，越小越好）
            // 4. 优先级对齐成本（升序，越小越好）
            // 5. 最大offset（升序，越小越好）
            // 6. 静态重叠率（升序，越小越好）
            // 7. 冲突率（升序，越小越好）
            // 8. 算法名称（确保稳定性）
            // 9. 策略名称（确保稳定性）
            var sortedCandidates = candidatesWithAnalysis
                .OrderBy(c => c.DynamicBusyAtStartRatio)   // 第一：动态启动忙碌比例（越小越好）
                .ThenBy(c => c.StartClustering)            // 第二：启动聚集度
                .ThenBy(c => c.StartOverlapRate)           // 第三：静态启动重叠率
                .ThenBy(c => c.PriorityAlignmentCost)      // 第四：优先级对齐成本
                .ThenBy(c => c.MaxOffsetUs)                // 第五：最大offset（越小越好）
                .ThenBy(c => c.OverlapRate)                // 第六：静态重叠率
                .ThenBy(c => c.ConflictRate)               // 第七：冲突率
                //.ThenBy(c => c.Stats.Mean)                 // 第六：均值
                //.ThenByDescending(c => c.Stats.Variance)   // 第七：方差
                .ThenBy(c => c.Candidate.Algorithm)        // 第八：算法名称
                .ThenBy(c => c.Candidate.Strategy)         // 第九：策略名称
                .ToList();

            var selectedCandidate = sortedCandidates.First();

            try
            {
                // 将选中候选的指标写回最终结果，供Form1和HTML直接复用
                if (selectedCandidate?.Candidate?.OriginalResult != null)
                {
                    selectedCandidate.Candidate.OriginalResult.ExtraMetrics = selectedCandidate.Candidate.ExtraMetrics;
                }
            }
            catch { }

            // 分析选择原因
            var hasConflicts = candidatesWithAnalysis.Any(c => c.ConflictRate > 0.001);
            var hasOverlaps = candidatesWithAnalysis.Any(c => c.OverlapRate > 0.001);
            var conflictLevels = candidatesWithAnalysis.GroupBy(c => Math.Round(c.ConflictRate, 3)).Count();
            var overlapLevels = candidatesWithAnalysis.GroupBy(c => Math.Round(c.OverlapRate, 3)).Count();

            // 降噪：仅打印少量关键行
                Console.WriteLine($"[决策] 选择: {selectedCandidate.Candidate.Algorithm}({selectedCandidate.Candidate.Strategy}), " +
                              $"动态忙比例={selectedCandidate.DynamicBusyAtStartRatio:F3}, 启动聚集度={selectedCandidate.StartClustering:F4}, 启动重叠率={selectedCandidate.StartOverlapRate:F3}, 优先级对齐成本={selectedCandidate.PriorityAlignmentCost:F6}, 重叠率={selectedCandidate.OverlapRate:F3}, 冲突率={selectedCandidate.ConflictRate:F3}");

            // 详细的选择原因分析
            // 优先按动态忙比例给出原因
            double minDynamic = candidatesWithAnalysis.Min(c => c.DynamicBusyAtStartRatio);
            double minStartOverlap = candidatesWithAnalysis.Min(c => c.StartOverlapRate);
            double minStartClustering = candidatesWithAnalysis.Min(c => c.StartClustering);
            double minAlign = candidatesWithAnalysis.Min(c => c.PriorityAlignmentCost);
            const double EPS = 1e-9;

            if (selectedCandidate.DynamicBusyAtStartRatio <= minDynamic + EPS)
            {
                Console.WriteLine($"[决策] 原因: 动态忙比例最低（{selectedCandidate.DynamicBusyAtStartRatio:F3}）");
            }
            else if (selectedCandidate.StartClustering <= minStartClustering + EPS)
            {
                Console.WriteLine($"[决策] 原因: 启动聚集度最低（{selectedCandidate.StartClustering:F4}）");
            }
            else if (selectedCandidate.StartOverlapRate <= minStartOverlap + EPS)
            {
                Console.WriteLine($"[决策] 原因: 启动重叠率最低（{selectedCandidate.StartOverlapRate:F3}）");
            }
            else if (selectedCandidate.PriorityAlignmentCost <= minAlign + EPS)
            {
                Console.WriteLine($"[决策] 原因: 优先级对齐成本最低（{selectedCandidate.PriorityAlignmentCost:F6}）");
            }
            else if (hasOverlaps)
            {
                if (selectedCandidate.OverlapRate < 0.001)
                {
                    Console.WriteLine($"[决策] 原因: 无冲突且无重叠（重叠率={selectedCandidate.OverlapRate:F3}）");
                }
                else
                {
                    Console.WriteLine($"[决策] 原因: 重叠率最低（{selectedCandidate.OverlapRate:F3}）");
                }
            }
            else
            {
                Console.WriteLine($"[决策] 原因: 冲突=0且无重叠，按算法/策略稳定性");
            }

            try
            {
                var tasksForDetail = selectedCandidate.Candidate.OriginalResult?.OptimizedTasks;
                if (tasksForDetail != null && tasksForDetail.Count > 0)
                {
                    var valid = tasksForDetail
                        .Where(t => t != null && t.Enabled && t.OffsetUs.HasValue && t.ExecutionTimeUs.HasValue && t.PeriodUs.HasValue)
                        .ToList();

                    int totalStarts = valid.Count;
                    int overlappedStarts = 0;

                    Func<TaskItem, List<string>> getOverlappers = (ti) =>
                    {
                        var overlappedBy = new List<string>();
                        foreach (var tj in valid)
                        {
                            if (ti.TaskIndex == tj.TaskIndex)
                            {
                                continue;
                            }
                            int rel = ti.OffsetUs.Value - tj.OffsetUs.Value;
                            int mod = rel % tj.PeriodUs.Value;
                            if (mod < 0)
                            {
                                mod += tj.PeriodUs.Value;
                            }
                            if (mod < tj.ExecutionTimeUs.Value)
                            {
                                overlappedBy.Add(string.IsNullOrWhiteSpace(tj.TaskName) ? tj.TaskIndex.ToString() : tj.TaskName);
                            }
                        }
                        return overlappedBy;
                    };

                    foreach (var ti in valid)
                    {
                        var overlappedBy = getOverlappers(ti);
                        if (overlappedBy.Count > 0)
                        {
                            overlappedStarts++;
                        }
                    }

                    double ratio = totalStarts > 0 ? (double)overlappedStarts / totalStarts : 0.0;
                    Console.WriteLine($"[启动重叠详情] 统计: overlappedStarts={overlappedStarts}/{totalStarts}, 比例={ratio:F3}");

                    var dTask = valid.FirstOrDefault(t => string.Equals(t.TaskName, "D", StringComparison.OrdinalIgnoreCase)) ??
                                valid.FirstOrDefault(t => t.TaskIndex == 4);
                    var eTask = valid.FirstOrDefault(t => string.Equals(t.TaskName, "E", StringComparison.OrdinalIgnoreCase)) ??
                                valid.FirstOrDefault(t => t.TaskIndex == 5);

                    if (dTask != null)
                    {
                        var overlappers = getOverlappers(dTask);
                        Console.WriteLine($"    [D] start={TaskUtility.ConvertToMilliseconds(dTask.OffsetUs.Value):F3}ms, exec={TaskUtility.ConvertToMilliseconds(dTask.ExecutionTimeUs.Value):F3}ms, period={TaskUtility.ConvertToMilliseconds(dTask.PeriodUs.Value):F3}ms, overlappedBy=[{string.Join(",", overlappers)}]");
                    }
                    if (eTask != null)
                    {
                        var overlappers = getOverlappers(eTask);
                        Console.WriteLine($"    [E] start={TaskUtility.ConvertToMilliseconds(eTask.OffsetUs.Value):F3}ms, exec={TaskUtility.ConvertToMilliseconds(eTask.ExecutionTimeUs.Value):F3}ms, period={TaskUtility.ConvertToMilliseconds(eTask.PeriodUs.Value):F3}ms, overlappedBy=[{string.Join(",", overlappers)}]");
                    }
                }
            }
            catch { }

            // 输出所有候选结果的分析信息
            // 仅打印最多前5个候选，避免日志爆炸
            int printCount = Math.Min(sortedCandidates.Count, 5);
            for (int i = 0; i < printCount; i++)
            {
                var item = sortedCandidates[i];
                Console.WriteLine($"    [{i+1}] {item.Candidate.Algorithm}({item.Candidate.Strategy}): 成本={item.Candidate.Cost:F6}, 动态忙比例={item.DynamicBusyAtStartRatio:F3}, 启动聚集度={item.StartClustering:F4}, 启动重叠率={item.StartOverlapRate:F3}, 优先级对齐成本={item.PriorityAlignmentCost:F6}, 最大offset={item.MaxOffsetUs}us, 重叠率={item.OverlapRate:F3}, 冲突率={item.ConflictRate:F3}");
            }
            foreach (var item in sortedCandidates)
            {
                Console.WriteLine($"    {item.Candidate.Algorithm}({item.Candidate.Strategy}): 成本={item.Candidate.Cost:F6}, 动态忙比例={item.DynamicBusyAtStartRatio:F3}, 启动聚集度={item.StartClustering:F4}, 启动重叠率={item.StartOverlapRate:F3}, 优先级对齐成本={item.PriorityAlignmentCost:F6}, 最大offset={item.MaxOffsetUs}us, 重叠率={item.OverlapRate:F3}, 冲突率={item.ConflictRate:F3}");
            }

            return selectedCandidate.Candidate;
        }

        /// <summary>
        /// 计算启动聚集度指标（越小越好）及峰值并发与并发方差。
        /// 定义：StartClustering = Σ_t C(k(t),2) / C(totalStarts,2)
        /// 其中 k(t) 为超周期内在同一时刻 t 启动的任务数。
        /// </summary>
        public static double CalculateStartClusteringScore(int timeBaseUs, List<TaskItem> tasks, out int peakStartConcurrency, out double startConcurrencyVariance)
        {
            peakStartConcurrency = 0;
            startConcurrencyVariance = 0.0;
            if (tasks == null || tasks.Count == 0)
            {
                return 0.0;
            }
            try
            {
                var valid = tasks.Where(t => t.Enabled && t.OffsetUs.HasValue && t.PeriodUs.HasValue && t.PeriodUs.Value > 0).ToList();
                if (valid.Count == 0)
                {
                    return 0.0;
                }
                int hyper = TaskUtility.CalculateHyperPeriod(valid);
                if (hyper <= 0)
                {
                    return 0.0;
                }
                var counts = new Dictionary<int, int>();
                long totalStarts = 0;
                foreach (var t in valid)
                {
                    int period = t.PeriodUs.Value;
                    int first = t.OffsetUs.Value % period;
                    if (first < 0) { first += period; }
                    for (int s = first; s < hyper; s += period)
                    {
                        totalStarts++;
                        int key = s; // 精确到微秒，不做对齐
                        int c;
                        if (counts.TryGetValue(key, out c))
                        {
                            counts[key] = c + 1;
                        }
                        else
                        {
                            counts[key] = 1;
                        }
                    }
                }
                if (totalStarts < 2)
                {
                    return 0.0;
                }
                // 计算聚集度（同一时刻的成对比例）
                long sameTimePairs = 0;
                foreach (var kv in counts)
                {
                    int k = kv.Value;
                    if (k > 1)
                    {
                        sameTimePairs += (long)k * (k - 1) / 2;
                    }
                    if (k > peakStartConcurrency) { peakStartConcurrency = k; }
                }
                long totalPairs = totalStarts * (totalStarts - 1) / 2;
                double score = totalPairs > 0 ? (double)sameTimePairs / totalPairs : 0.0;

                // 并发方差（仅对出现启动的时刻集合）
                if (counts.Count > 0)
                {
                    double mean = counts.Values.Average();
                    double var = counts.Values.Select(v => Math.Pow(v - mean, 2)).Average();
                    startConcurrencyVariance = var;
                }
                return score;
            }
            catch
            {
                return 0.0;
            }
        }

        /// <summary>
        /// 计算优先级对齐成本（用于统一挑选阶段）：
        /// 若高优先级任务的Offset大于低优先级任务的Offset，则累加差值（毫秒）乘以轻微系数（沿用原TIE_BREAKER_WEIGHT默认值）
        /// </summary>
        public static double CalculatePriorityAlignmentCostForTasks(List<TaskAnalysisResult> taskResults)
        {
            if (taskResults == null || taskResults.Count == 0)
            {
                return 0.0;
            }
            const double TIE_BREAKER_WEIGHT = 0.0001; // 与原实现保持一致
            double alignmentCost = 0.0;
            var sortedByPriority = taskResults.OrderByDescending(r => r.Priority).ToList();
            for (int i = 0; i < sortedByPriority.Count - 1; i++)
            {
                var higherTask = sortedByPriority[i];
                var lowerTask = sortedByPriority[i + 1];
                if (higherTask.OffsetUs > lowerTask.OffsetUs)
                {
                    int offsetDifferenceUs = higherTask.OffsetUs - lowerTask.OffsetUs;
                    alignmentCost += TaskUtility.ConvertToMilliseconds(offsetDifferenceUs) * TIE_BREAKER_WEIGHT;
                }
            }
            return alignmentCost;
        }

        /// <summary>
        /// 计算动态启动忙碌比例：在抢占式时间线上，任务每个启动时刻是否“被他人占用”（不含自身执行）的比例
        /// 使用 OffsetValidator 进行一次超周期内的抢占式模拟，不做降采样
        /// </summary>
        public static double CalculateDynamicBusyAtStartRatioStatic(int timeBaseUs, List<TaskItem> tasks)
        {
            if (tasks == null || tasks.Count == 0)
            {
                return 0.0;
            }

            try
            {
                // 使用与系统一致的时基构造校验器；这里用最小步长常量以保证一致性
                var validator = new OffsetValidator(timeBaseUs);
                var cloned = TaskUtility.CloneTasks(tasks);
                var result = validator.ValidateOffsets(cloned); // 模拟一个超周期

                // 从校验器获取详细时间线
                var detail = validator.GetDetailedTimelineData();
                if (detail == null || detail.DistributionData == null || detail.DistributionData.Count == 0)
                {
                    return 0.0;
                }

                int stepUs = detail.TimeBaseUs > 0 ? detail.TimeBaseUs : TaskUtility.US_STEP;
                int hyper = detail.HyperPeriodUs > 0 ? detail.HyperPeriodUs : (cloned.Max(t => t.PeriodUs ?? 0));
                if (hyper <= 0)
                {
                    return 0.0;
                }

                // 将分布数据索引化，便于快速查询某时刻的执行任务名（空闲ExecutingTask为空）
                var execMap = new Dictionary<int, string>();
                foreach (var p in detail.DistributionData)
                {
                    execMap[p.TimeUs] = p.ExecutingTask ?? string.Empty;
                }

                int totalStarts = 0;
                int busyByOthersStarts = 0;

                foreach (var t in cloned)
                {
                    if (!t.Enabled || !t.OffsetUs.HasValue || !t.PeriodUs.HasValue || !t.ExecutionTimeUs.HasValue)
                    {
                        continue;
                    }

                    int period = t.PeriodUs.Value;
                    if (period <= 0)
                    {
                        continue;
                    }

                    // 规范化首启动
                    int first = t.OffsetUs.Value % period;
                    if (first < 0)
                    {
                        first += period;
                    }

                    for (int start = first; start < hyper; start += period)
                    {
                        totalStarts++;
                        // 对齐到时间线步长
                        int aligned = (start / stepUs) * stepUs;
                        string executingAtStart = string.Empty;
                        if (execMap.TryGetValue(aligned, out string nameAtAligned))
                        {
                            executingAtStart = nameAtAligned;
                        }
                        else
                        {
                            // 若缺精确点，向后取最近采样点
                            int next = aligned + stepUs;
                            if (execMap.TryGetValue(next, out string nameAtNext))
                            {
                                executingAtStart = nameAtNext;
                            }
                        }

                        // 仅统计“被他人占用”
                        string thisTaskName = string.IsNullOrWhiteSpace(t.TaskName) ? string.Empty : t.TaskName;
                        bool busyByOthers = !string.IsNullOrEmpty(executingAtStart) &&
                                            !string.Equals(executingAtStart, thisTaskName, StringComparison.OrdinalIgnoreCase);
                        if (busyByOthers)
                        {
                            busyByOthersStarts++;
                        }
                    }
                }

                if (totalStarts == 0)
                {
                    return 0.0;
                }
                return (double)busyByOthersStarts / totalStarts;
            }
            catch
            {
                return 0.0;
            }
        }

        /// <summary>
        /// 运行模拟退火优化（集成多起点策略）
        /// </summary>
        private OptimizationResultSummary RunSimulatedAnnealing(List<TaskItem> tasks, 
            int maxOffsetUs, IntelligentSchedulingParameters parameters)
        {
            // 过滤出可优化的任务（非手动输入的任务）
            var optimizableTasks = tasks.Where(t => !t.ManualInput).ToList();
            
            // 如果没有可优化的任务，直接返回成功结果
            if (optimizableTasks.Count == 0)
            {
                return new OptimizationResultSummary
                {
                    Algorithm = "SimulatedAnnealing",
                    Success = true,
                    BestCost = 0,
                    TotalIterations = 0,
                    OptimizationTime = TimeSpan.Zero,
                    OptimizedTasks = tasks, // 返回原始任务列表
                    SchedulabilityResult = _analyzer.AnalyzeSchedulability(tasks),
                    ErrorMessage = "所有任务都已手动设置Offset，无需优化"
                };
            }

            // 检查是否使用多起点策略
            if (parameters.UseMultiStartStrategies)
            {
                return RunMultiStartSimulatedAnnealing(tasks, maxOffsetUs, parameters);
            }
            else
            {
                // 传统单起点模拟退火
                return RunSingleStartSimulatedAnnealing(tasks, maxOffsetUs, parameters);
            }
        }

        /// <summary>
        /// 运行多起点模拟退火优化
        /// </summary>
        private OptimizationResultSummary RunMultiStartSimulatedAnnealing(List<TaskItem> tasks, 
            int maxOffsetUs, IntelligentSchedulingParameters parameters)
        {
            //Console.WriteLine($"[调试警告检查] RunMultiStartSimulatedAnnealing - 最大offset限制: {TaskUtility.ConvertToMilliseconds(maxOffsetUs):F3}ms");
            
            var multiStartParams = new MultiStartParameters
            {
                UseBasicStrategies = parameters.UseBasicStrategies,
                UseExtendedStrategies = parameters.UseExtendedStrategies,
                UseAdvancedStrategies = parameters.UseAdvancedStrategies,
                UseSimulatedAnnealing = true,
                UseGeneticAlgorithm = false, // 只用SA
            };

            // 应用确定性参数
            if (parameters.EnableDeterministicMode && parameters.RandomSeed.HasValue)
            {
                multiStartParams.SAParameters.RandomSeed = parameters.RandomSeed.Value;
            }

            var multiStartOptimizer = new MultiStartOptimizer(_timeBaseUs, maxOffsetUs, multiStartParams);
            var result = multiStartOptimizer.Optimize(tasks);
            
            // 检查结果是否超出限制
            if (result.Success && result.BestSolution != null)
            {

                // 将最佳解应用到任务列表
                var optimizedTasks = CloneTaskList(tasks);
                foreach (var task in optimizedTasks)
                {
                    if (result.BestSolution.TryGetValue(task.TaskIndex, out int offsetUs))
                    {
                        task.OffsetUs = offsetUs;
                    }
                }

                return new OptimizationResultSummary
                {
                    Algorithm = $"MultiStart-SimulatedAnnealing({result.BestStrategy})",
                    Success = true,
                    BestCost = result.BestCost,
                    TotalIterations = result.TotalStrategiesUsed,
                    OptimizationTime = result.OptimizationTime,
                    OptimizedTasks = optimizedTasks,
                    SchedulabilityResult = _analyzer.AnalyzeSchedulability(optimizedTasks),
                    ErrorMessage = null,
                    MultiStartDetails = result  // 保存完整的多起点结果
                };
            }
            else
            {
                return new OptimizationResultSummary
                {
                    Algorithm = "MultiStart-SimulatedAnnealing",
                    Success = false,
                    BestCost = double.MaxValue,
                    TotalIterations = 0,
                    OptimizationTime = result.OptimizationTime,
                    OptimizedTasks = tasks,
                    SchedulabilityResult = null,
                    ErrorMessage = result.ErrorMessage
                };
            }
        }

        /// <summary>
        /// 运行单起点模拟退火优化
        /// </summary>
        private OptimizationResultSummary RunSingleStartSimulatedAnnealing(List<TaskItem> tasks, 
            int maxOffsetUs, IntelligentSchedulingParameters parameters)
        {
            //Console.WriteLine($"[调试警告检查] RunSingleStartSimulatedAnnealing - 最大offset限制: {TaskUtility.ConvertToMilliseconds(maxOffsetUs):F3}ms");
            
            var saParameters = new OptimizationParameters
            {
                MaxIterations = parameters.SAMaxIterations,
                MaxRestarts = parameters.SAMaxRestarts,
                UseStrategyAdaptation = parameters.UseIntelligentSA,
                UseDynamicInnerLoop = parameters.UseIntelligentSA,
                UseAdaptiveCooling = parameters.UseIntelligentSA,
                UseEarlyStopping = parameters.UseEarlyStopping,
                UseAdvancedPerturbation = parameters.UseIntelligentSA
            };

            // 应用确定性参数
            if (parameters.EnableDeterministicMode && parameters.RandomSeed.HasValue)
            {
                saParameters.RandomSeed = parameters.RandomSeed.Value;
            }

            var optimizer = new SimulatedAnnealingOptimizer(_timeBaseUs, maxOffsetUs, saParameters);
            var saResult = optimizer.Optimize(tasks); // 传入完整任务列表，算法内部会处理ManualInput
            try
            {
                double overlapRate = SchedulabilityAnalyzer.ComputeStartOverlapRateForTasks(saResult.OptimizedTasks ?? tasks);
                Console.WriteLine($"[SA单起点] 静态启动重叠率: {overlapRate:F6}");
            }
            catch { }
            

            return new OptimizationResultSummary
            {
                Algorithm = "SimulatedAnnealing",
                Success = saResult.Success,
                BestCost = saResult.BestCost,
                TotalIterations = saResult.Statistics?.TotalIterations ?? 0,
                OptimizationTime = saResult.Statistics?.OptimizationTime ?? TimeSpan.Zero,
                OptimizedTasks = saResult.OptimizedTasks,
                SchedulabilityResult = saResult.SchedulabilityResult,
                ErrorMessage = saResult.ErrorMessage
            };
        }

        /// <summary>
        /// 运行遗传算法优化（集成多起点策略）
        /// </summary>
        private OptimizationResultSummary RunGeneticAlgorithm(List<TaskItem> tasks, 
            int maxOffsetUs, IntelligentSchedulingParameters parameters)
        {
            // 过滤出可优化的任务（非手动输入的任务）
            var optimizableTasks = tasks.Where(t => !t.ManualInput).ToList();
            
            // 如果没有可优化的任务，直接返回成功结果
            if (optimizableTasks.Count == 0)
            {
                return new OptimizationResultSummary
                {
                    Algorithm = "GeneticAlgorithm",
                    Success = true,
                    BestCost = 0,
                    TotalIterations = 0,
                    OptimizationTime = TimeSpan.Zero,
                    OptimizedTasks = tasks, // 返回原始任务列表
                    SchedulabilityResult = _analyzer.AnalyzeSchedulability(tasks),
                    ErrorMessage = "所有任务都已手动设置Offset，无需优化"
                };
            }

            // 检查是否使用多起点策略
            if (parameters.UseMultiStartStrategies)
            {
                return RunMultiStartGeneticAlgorithm(tasks, maxOffsetUs, parameters);
            }
            else
            {
                // 传统单起点遗传算法
                return RunSingleStartGeneticAlgorithm(tasks, maxOffsetUs, parameters);
            }
        }

        /// <summary>
        /// 运行多起点遗传算法优化
        /// </summary>
        private OptimizationResultSummary RunMultiStartGeneticAlgorithm(List<TaskItem> tasks, 
            int maxOffsetUs, IntelligentSchedulingParameters parameters)
        {
            //Console.WriteLine($"[调试警告检查] RunMultiStartGeneticAlgorithm - 最大offset限制: {TaskUtility.ConvertToMilliseconds(maxOffsetUs):F3}ms");
            
            var multiStartParams = new MultiStartParameters
            {
                UseBasicStrategies = parameters.UseBasicStrategies,
                UseExtendedStrategies = parameters.UseExtendedStrategies,
                UseAdvancedStrategies = parameters.UseAdvancedStrategies,
                UseSimulatedAnnealing = false, // 只用GA
                UseGeneticAlgorithm = true,
            };

            // 应用确定性参数
            if (parameters.EnableDeterministicMode && parameters.RandomSeed.HasValue)
            {
                multiStartParams.GAParameters.RandomSeed = parameters.RandomSeed.Value;
            }

            var multiStartOptimizer = new MultiStartOptimizer(_timeBaseUs, maxOffsetUs, multiStartParams);
            var result = multiStartOptimizer.Optimize(tasks);
            
            // 检查结果是否超出限制
            if (result.Success && result.BestSolution != null)
            {
                // 将最佳解应用到任务列表
                var optimizedTasks = CloneTaskList(tasks);
                foreach (var task in optimizedTasks)
                {
                    if (result.BestSolution.TryGetValue(task.TaskIndex, out int offsetUs))
                    {
                        task.OffsetUs = offsetUs;
                    }
                }

                return new OptimizationResultSummary
                {
                    Algorithm = $"MultiStart-GeneticAlgorithm({result.BestStrategy})",
                    Success = true,
                    BestCost = result.BestCost,
                    TotalIterations = result.TotalStrategiesUsed,
                    OptimizationTime = result.OptimizationTime,
                    OptimizedTasks = optimizedTasks,
                    SchedulabilityResult = _analyzer.AnalyzeSchedulability(optimizedTasks),
                    ErrorMessage = null,
                    MultiStartDetails = result  // 保存完整的多起点结果
                };
            }
            else
            {
                return new OptimizationResultSummary
                {
                    Algorithm = "MultiStart-GeneticAlgorithm",
                    Success = false,
                    BestCost = double.MaxValue,
                    TotalIterations = 0,
                    OptimizationTime = result.OptimizationTime,
                    OptimizedTasks = tasks,
                    SchedulabilityResult = null,
                    ErrorMessage = result.ErrorMessage
                };
            }
        }

        /// <summary>
        /// 运行单起点遗传算法优化
        /// </summary>
        private OptimizationResultSummary RunSingleStartGeneticAlgorithm(List<TaskItem> tasks, 
            int maxOffsetUs, IntelligentSchedulingParameters parameters)
        {
            //Console.WriteLine($"[调试警告检查] RunSingleStartGeneticAlgorithm - 最大offset限制: {TaskUtility.ConvertToMilliseconds(maxOffsetUs):F3}ms");
            
            var gaParameters = new GAOptimizationParameters
            {
                PopulationSize = parameters.GAPopulationSize,
                MaxGenerations = parameters.GAMaxGenerations,
                // 改进遗传算法特性配置
                SelectionMethod = parameters.UseImprovedGA ? SelectionType.Tournament : SelectionType.RouletteWheel,
                CrossoverMethod = parameters.UseImprovedGA ? CrossoverType.Uniform : CrossoverType.SinglePoint,
                BoundaryHandlingMethod = parameters.UseImprovedGA ? BoundaryHandlingType.Reflection : BoundaryHandlingType.Clamping,
                EnvironmentalSelectionMethod = parameters.UseImprovedGA ? EnvironmentalSelectionType.PlusSelection : EnvironmentalSelectionType.ElitistWithEnhanced,
                UseHeuristicSeeding = parameters.UseImprovedGA,
                UseAdaptiveMutation = parameters.UseImprovedGA,
                UseHeuristicMutation = parameters.UseImprovedGA
            };

            // 应用确定性参数
            if (parameters.EnableDeterministicMode && parameters.RandomSeed.HasValue)
            {
                gaParameters.RandomSeed = parameters.RandomSeed.Value;
            }

            var optimizer = new GeneticAlgorithmOptimizer(_timeBaseUs, maxOffsetUs, gaParameters);
            var gaResult = optimizer.Optimize(tasks); // 传入完整任务列表，算法内部会处理ManualInput
            try
            {
                double overlapRate = SchedulabilityAnalyzer.ComputeStartOverlapRateForTasks(tasks);
                Console.WriteLine($"[GA单起点] 静态启动重叠率: {overlapRate:F6}");
            }
            catch { }
            

            return new OptimizationResultSummary
            {
                Algorithm = "GeneticAlgorithm",
                Success = gaResult.Success,
                BestCost = gaResult.BestCost,
                TotalIterations = gaResult.Statistics?.TotalGenerations ?? 0,
                OptimizationTime = gaResult.Statistics?.OptimizationTime ?? TimeSpan.Zero,
                OptimizedTasks = tasks, // 返回优化后的任务列表（已在Optimize方法中应用了最佳Offset）
                SchedulabilityResult = gaResult.SchedulabilityResult,
                ErrorMessage = gaResult.ErrorMessage
            };
        }

        /// <summary>
        /// 从两个优化结果中选择最优解
        /// 多维度对比裁决标准
        /// </summary>
        private OptimizationResultSummary SelectBestSolution(OptimizationResultSummary saResult, 
            OptimizationResultSummary gaResult)
        {
            // 如果任一算法失败，选择成功的
            if (!saResult.Success && gaResult.Success) return gaResult;
            if (saResult.Success && !gaResult.Success) return saResult;
            if (!saResult.Success && !gaResult.Success) return saResult; // 都失败时返回SA结果

            // 第一标准：错过截止时间的任务数（硬约束）
            var saMissCount = saResult.SchedulabilityResult?.Statistics?.DeadlineMissCount ?? int.MaxValue;
            var gaMissCount = gaResult.SchedulabilityResult?.Statistics?.DeadlineMissCount ?? int.MaxValue;

            if (saMissCount != gaMissCount)
            {
                return saMissCount < gaMissCount ? saResult : gaResult;
            }

            // 第二标准：最大响应时间比率（主要目标）
            var saMaxRatio = saResult.SchedulabilityResult?.Statistics?.MaxResponseTimeRatio ?? double.MaxValue;
            var gaMaxRatio = gaResult.SchedulabilityResult?.Statistics?.MaxResponseTimeRatio ?? double.MaxValue;

            const double RATIO_TOLERANCE = 0.001; // 1‰的容差
            if (Math.Abs(saMaxRatio - gaMaxRatio) > RATIO_TOLERANCE)
            {
                return saMaxRatio < gaMaxRatio ? saResult : gaResult;
            }

            // 第三标准：平均响应时间比率（次要目标）
            var saAvgRatio = saResult.SchedulabilityResult?.Statistics?.AverageResponseTimeRatio ?? double.MaxValue;
            var gaAvgRatio = gaResult.SchedulabilityResult?.Statistics?.AverageResponseTimeRatio ?? double.MaxValue;

            if (Math.Abs(saAvgRatio - gaAvgRatio) > RATIO_TOLERANCE)
            {
                return saAvgRatio < gaAvgRatio ? saResult : gaResult;
            }

            // 第四标准：响应时间比率方差（稳定性）
            var saVariance = saResult.SchedulabilityResult?.Statistics?.ResponseTimeRatioVariance ?? double.MaxValue;
            var gaVariance = gaResult.SchedulabilityResult?.Statistics?.ResponseTimeRatioVariance ?? double.MaxValue;

            if (Math.Abs(saVariance - gaVariance) > RATIO_TOLERANCE)
            {
                return saVariance < gaVariance ? saResult : gaResult;
            }

            // 第五标准：最大Offset值（越小越好）
            var saMaxOffsetUs = CalculateMaxOffsetUs(saResult.OptimizedTasks);
            var gaMaxOffsetUs = CalculateMaxOffsetUs(gaResult.OptimizedTasks);

            // 设定允许一个时基的容差
            if (Math.Abs(saMaxOffsetUs - gaMaxOffsetUs) > _timeBaseUs)
            {
                return saMaxOffsetUs < gaMaxOffsetUs ? saResult : gaResult;
            }

            // 如果所有标准都相等，优先选择遗传算法（通常更稳定）
            return gaResult;
        }

        /// <summary>
        /// 从所有步骤结果中选择全局最优解
        /// 优先原则：性能优先，在性能相同的情况下应用综合筛选策略（均值和方差各占50%权重）
        /// 丢弃超出用户限制的解，如果没有有效解则报错
        /// </summary>
        /// <param name="stepResults">所有步骤的优化结果</param>
        /// <param name="userMaxOffsetUs">用户设定的最大offset限制(微秒)</param>
        private OptimizationResultSummary SelectGlobalBestSolution(List<DualTrackOptimizationResult> stepResults, int userMaxOffsetUs)
        {
            if (stepResults.Count == 0) return null;

            var validSteps = stepResults.Where(r => r.BestResult.Success).ToList();
            if (validSteps.Count == 0) return stepResults.First().BestResult; // 返回第一个（可能失败的）结果

            // 检查并过滤超出用户限制的解决方案
            //Console.WriteLine($"[调试警告检查] SelectGlobalBestSolution - 用户设定限制: {TaskUtility.ConvertToMilliseconds(userMaxOffsetUs):F3}ms");
            
            // 过滤出所有任务offset都在用户限制内的步骤
            var compliantSteps = new List<DualTrackOptimizationResult>();
            
            foreach (var step in validSteps)
            {
                bool isCompliant = true;
                if (step.BestResult.OptimizedTasks != null)
                {
                    foreach (var task in step.BestResult.OptimizedTasks)
                    {
                        // 使用该任务的独立最大offset限制，如果未设置则使用全局限制
                        int taskMaxOffsetUs = task.MaxOffsetUs > 0 ? task.MaxOffsetUs : userMaxOffsetUs;
                        
                        if (task.OffsetUs.HasValue && task.OffsetUs.Value > taskMaxOffsetUs + 1) // 允许1us误差
                        {
                            //Console.WriteLine($"🚨 [全局选择] 丢弃超出任务限制的解! 测试offset={TaskUtility.ConvertToMilliseconds(step.MaxOffsetUs):F3}ms, TaskIndex={task.TaskIndex}, TaskName={task.TaskName}, Offset={TaskUtility.ConvertToMilliseconds(task.OffsetUs.Value):F3}ms, 任务限制={TaskUtility.ConvertToMilliseconds(taskMaxOffsetUs):F3}ms");
                            isCompliant = false;
                            break;
                        }
                    }
                }
                
                if (isCompliant)
                {
                    compliantSteps.Add(step);
                }
            }

            // 检查是否有符合要求的解
            if (compliantSteps.Count == 0)
            {
                var errorMessage = $"所有优化结果都超出了用户设定的最大offset限制({TaskUtility.ConvertToMilliseconds(userMaxOffsetUs):F3}ms)。" +
                                 $"请考虑适当增加最大offset值以获得可行的调度方案。" +
                                 $"建议将最大offset设置为至少 {TaskUtility.ConvertToMilliseconds(validSteps.Min(s => s.MaxOffsetUs)):F1}ms 以上。";
                
                //Console.WriteLine($"❌ [全局选择错误] {errorMessage}");
                
                return new OptimizationResultSummary
                {
                    Success = false,
                    ErrorMessage = errorMessage,
                    Algorithm = "GlobalSelection",
                    BestCost = double.MaxValue
                };
            }

            //Console.WriteLine($"✅ [全局选择] 找到 {compliantSteps.Count} 个符合用户限制的解决方案");

            // 收集所有符合限制的步骤结果到统一集合
            var allGlobalCandidates = new List<StrategyCandidateResult>();
            
            foreach (var step in compliantSteps)
            {
                var solution = ExtractOffsetSolution(step.BestResult);
                if (solution.Count > 0)
                {
                    var strategy = "Unknown";
                    if (step.BestResult.MultiStartDetails != null)
                    {
                        strategy = step.BestResult.MultiStartDetails.BestStrategy.ToString();
                    }
                    
                    allGlobalCandidates.Add(new StrategyCandidateResult
                    {
                        Algorithm = step.BestResult.Algorithm.Split('(')[0], // 提取基础算法名称
                        Strategy = strategy,
                        Cost = step.BestResult.BestCost,
                        Solution = solution,
                        OriginalResult = step.BestResult
                    });
                }
            }

            // 使用统一选择逻辑
            if (allGlobalCandidates.Count > 0)
            {
                var selectedCandidate = SelectBestFromUnifiedCandidates(allGlobalCandidates);
                var globalBest = selectedCandidate.OriginalResult;
                // 统一选择路径下，ExtraMetrics已由SelectBestByNewCriteria回填
                
                //Console.WriteLine($"🎯 [全局选择] 最终选择: 成本={globalBest.BestCost:F6}, 算法={globalBest.Algorithm}");
                return globalBest;
            }
            else
            {
                // 回退到第一个有效结果
                var globalBest = compliantSteps.First().BestResult;
                // 回退路径：计算并填充ExtraMetrics，避免下游空值
                try
                {
                    if (globalBest != null && globalBest.ExtraMetrics == null)
                    {
                        var solution = ExtractOffsetSolution(globalBest);
                        var tasks = globalBest.OptimizedTasks;
                        var metrics = new SelectionExtraMetrics
                        {
                            ConflictRate = CalculateConflictRate(solution),
                            StaticOverlapRate = CalculateOverlapRate(solution, tasks),
                            StartOverlapRate = tasks != null ? SchedulabilityAnalyzer.ComputeStartOverlapRateForTasks(tasks) : 0.0,
                            DynamicBusyAtStartRatio = CalculateDynamicBusyAtStartRatioStatic(_staticTimeBaseUs, tasks),
                            MaxOffsetUs = (tasks != null && tasks.Count > 0)
                                ? tasks.Where(t => t.OffsetUs.HasValue).Select(t => t.OffsetUs.Value).DefaultIfEmpty(0).Max()
                                : (solution != null && solution.Count > 0 ? solution.Values.Max() : 0)
                        };
                        globalBest.ExtraMetrics = metrics;
                    }
                }
                catch { }
                //Console.WriteLine($"🎯 [全局选择] 回退选择: 成本={globalBest.BestCost:F6}, 算法={globalBest.Algorithm}");
                return globalBest;
            }
        }
        
        /// <summary>
        /// 判断两个解决方案是否在性能上等价
        /// </summary>
        private bool IsSolutionEquivalent(OptimizationResultSummary solution1, OptimizationResultSummary solution2)
        {
            if (!solution1.Success || !solution2.Success) return false;
            
            const double TOLERANCE = 0.001; // 1‰的容差
            
            // 比较关键性能指标
            var stats1 = solution1.SchedulabilityResult?.Statistics;
            var stats2 = solution2.SchedulabilityResult?.Statistics;
            
            if (stats1 == null || stats2 == null) return false;
            
            // 1. 错过截止时间的任务数必须相同
            if (stats1.DeadlineMissCount != stats2.DeadlineMissCount) return false;
            
            // 2. 最大响应时间比率差异在容差范围内
            if (Math.Abs(stats1.MaxResponseTimeRatio - stats2.MaxResponseTimeRatio) > TOLERANCE) return false;
            
            // 3. 平均响应时间比率差异在容差范围内
            if (Math.Abs(stats1.AverageResponseTimeRatio - stats2.AverageResponseTimeRatio) > TOLERANCE) return false;
            
            // 4. 响应时间比率方差差异在容差范围内
            if (Math.Abs(stats1.ResponseTimeRatioVariance - stats2.ResponseTimeRatioVariance) > TOLERANCE) return false;
            
            return true; // 所有关键指标都相同，认为是等价的解
        }

        /// <summary>
        /// 生成决策理由
        /// </summary>
        private string GenerateDecisionReason(DualTrackOptimizationResult result)
        {
            var sa = result.SimulatedAnnealingResult;
            var ga = result.GeneticAlgorithmResult;
            var best = result.BestResult;

            if (!sa.Success && !ga.Success)
                return "两种算法都执行失败";

            if (!sa.Success)
                return "选择遗传算法结果：模拟退火执行失败";

            if (!ga.Success)
                return "选择模拟退火结果：遗传算法执行失败";

            var reason = $"选择{best.Algorithm}结果：";
            
            // 检查决策标准
            var saMissCount = sa.SchedulabilityResult?.Statistics?.DeadlineMissCount ?? int.MaxValue;
            var gaMissCount = ga.SchedulabilityResult?.Statistics?.DeadlineMissCount ?? int.MaxValue;

            if (saMissCount != gaMissCount)
            {
                reason += $"错过截止时间任务数更少({Math.Min(saMissCount, gaMissCount)} vs {Math.Max(saMissCount, gaMissCount)})";
                return reason;
            }

            var saMaxRatio = sa.SchedulabilityResult?.Statistics?.MaxResponseTimeRatio ?? double.MaxValue;
            var gaMaxRatio = ga.SchedulabilityResult?.Statistics?.MaxResponseTimeRatio ?? double.MaxValue;

            if (Math.Abs(saMaxRatio - gaMaxRatio) > 0.001)
            {
                reason += $"最大响应时间比率更低({Math.Min(saMaxRatio, gaMaxRatio):F3} vs {Math.Max(saMaxRatio, gaMaxRatio):F3})";
                return reason;
            }

            reason += "在关键指标相近的情况下，综合性能更优";
            return reason;
        }

        /// <summary>
        /// 生成优化决策报告
        /// </summary>
        private OptimizationDecision GenerateOptimizationDecision(IntelligentSchedulingResult result)
        {
            var decision = new OptimizationDecision
            {
                FinalMaxOffsetUs = result.FinalBestResult?.SchedulabilityResult?.Statistics?.MaxOffsetUs ?? 0,
                OriginalMaxOffsetUs = result.OriginalMaxOffsetUs,
                OffsetReductionUs = result.OriginalMaxOffsetUs - (result.FinalBestResult?.SchedulabilityResult?.Statistics?.MaxOffsetUs ?? 0),
                TestedOffsetCount = result.StepResults.Count,
                TotalOptimizationTime = result.TotalTime
            };

            // 性能改进分析
            var firstResult = result.StepResults.FirstOrDefault()?.BestResult;
            var bestResult = result.FinalBestResult;

            if (firstResult != null && bestResult != null && firstResult.Success && bestResult.Success)
            {
                var firstMaxRatio = firstResult.SchedulabilityResult?.Statistics?.MaxResponseTimeRatio ?? 0;
                var bestMaxRatio = bestResult.SchedulabilityResult?.Statistics?.MaxResponseTimeRatio ?? 0;
                
                decision.PerformanceImprovement = (firstMaxRatio - bestMaxRatio) / firstMaxRatio * 100;
                decision.OptimizationEffectiveness = decision.PerformanceImprovement > 0 ? "显著改进" : "保持最优";
            }

            // 生成决策总结
            decision.DecisionSummary = GenerateDecisionSummary(result, decision);

            return decision;
        }

        /// <summary>
        /// 生成决策总结
        /// </summary>
        private string GenerateDecisionSummary(IntelligentSchedulingResult result, OptimizationDecision decision)
        {
            var summary = new System.Text.StringBuilder();
            
            summary.AppendLine("=== 智能调度决策总结 ===");
            summary.AppendLine($"测试了{decision.TestedOffsetCount}个不同的最大Offset限制");
            summary.AppendLine($"最优解的最大Offset: {TaskUtility.ConvertToMilliseconds(decision.FinalMaxOffsetUs):F1}ms (原始: {TaskUtility.ConvertToMilliseconds(decision.OriginalMaxOffsetUs):F1}ms)");
            
            if (decision.OffsetReductionUs > 0)
            {
                summary.AppendLine($"✓ 成功减少Offset范围: {TaskUtility.ConvertToMilliseconds(decision.OffsetReductionUs):F1}ms");
            }

            if (result.FinalBestResult != null && result.FinalBestResult.Success)
            {
                var stats = result.FinalBestResult.SchedulabilityResult?.Statistics;
                if (stats != null)
                {
                    summary.AppendLine($"✓ 所有任务满足截止时间要求");
                    summary.AppendLine($"✓ 最大响应时间比率: {stats.MaxResponseTimeRatio:F3}");
                    summary.AppendLine($"✓ 平均响应时间比率: {stats.AverageResponseTimeRatio:F3}");
                }
                
                // 分析是否找到了性能和Offset的最佳平衡
                var allSuccessfulSteps = result.StepResults.Where(s => s.BestResult.Success).ToList();
                if (allSuccessfulSteps.Count > 1)
                {
                    var bestPerformanceStep = allSuccessfulSteps
                        .OrderBy(s => s.BestResult.SchedulabilityResult?.Statistics?.MaxResponseTimeRatio ?? 1.0)
                        .First();
                    var finalStep = allSuccessfulSteps
                        .FirstOrDefault(s => Math.Abs(s.MaxOffsetUs - decision.FinalMaxOffsetUs) < 1);
                        
                    if (finalStep != null && finalStep != bestPerformanceStep)
                    {
                        // 使用新的选择逻辑描述（去除均值/方差）
                        summary.AppendLine($"✓ 优化策略: 性能优先；在性能相近时按‘动态忙比例 → 启动重叠率 → 最大offset → 静态重叠率 → 冲突率 → 算法/策略’排序选择");
                        summary.AppendLine($"  最佳性能解: Offset={TaskUtility.ConvertToMilliseconds(bestPerformanceStep.MaxOffsetUs):F1}ms, 响应时间比率={bestPerformanceStep.BestResult.SchedulabilityResult?.Statistics?.MaxResponseTimeRatio:F3}");
                    }
                    else
                    {
                        summary.AppendLine($"✓ 优化结果: 找到性能与Offset的最佳平衡点");
                    }
                }
            }

            summary.AppendLine($"总优化时间: {decision.TotalOptimizationTime.TotalSeconds:F2}秒");
            
            if (result.EarlyStoppedAt.HasValue)
            {
                summary.AppendLine($"注意: 在Offset={TaskUtility.ConvertToMilliseconds(result.EarlyStoppedAt.Value):F1}ms处智能提前停止");
            }
            
            return summary.ToString();
        }

        /// <summary>
        /// 计算任务列表中的最大Offset值
        /// </summary>
        private int CalculateMaxOffsetUs(List<TaskItem> tasks)
        {
            if (tasks == null || tasks.Count == 0) return 0;
            return tasks.Where(t => t.OffsetUs.HasValue).Max(t => t.OffsetUs.Value);
        }

        /// <summary>
        /// 克隆任务列表（已重构为使用TaskUtility公共方法）
        /// </summary>
        private List<TaskItem> CloneTaskList(List<TaskItem> tasks)
        {
            return TaskUtility.CloneTasks(tasks);
        }

        /// <summary>
        /// 从优化结果中提取offset解决方案
        /// </summary>
        private Dictionary<int, int> ExtractOffsetSolution(OptimizationResultSummary result)
        {
            var solution = new Dictionary<int, int>();
            if (result?.OptimizedTasks != null)
            {
                foreach (var task in result.OptimizedTasks)
                {
                    if (task.OffsetUs.HasValue)
                    {
                        solution[task.TaskIndex] = task.OffsetUs.Value;
                    }
                }
            }
            return solution;
        }

        /// <summary>
        /// 计算offset值的统计特征
        /// </summary>
        /// <param name="offsets">offset字典</param>
        /// <returns>统计特征(均值, 方差, 标准差)</returns>
        public static (double Mean, double Variance, double StdDev) CalculateOffsetStatistics(Dictionary<int, int> offsets)
        {
            if (offsets == null || offsets.Count == 0)
                return (0, 0, 0);
                
            // 在源头将微秒转换为毫秒，便于报告显示
            var valuesMs = offsets.Values.Select(v => v / 1000.0).ToArray();
            var meanMs = valuesMs.Average();
            var varianceMs2 = valuesMs.Sum(v => Math.Pow(v - meanMs, 2)) / valuesMs.Length;
            var stdDevMs = Math.Sqrt(varianceMs2);
            
            return (meanMs, varianceMs2, stdDevMs);
        }

        /// <summary>
        /// 计算offset配置的冲突率
        /// </summary>
        /// <param name="offsets">offset字典</param>
        /// <returns>冲突率（0-1范围）</returns>
        public static double CalculateConflictRate(Dictionary<int, int> offsets)
        {
            if (offsets == null || offsets.Count <= 1)
                return 0.0;

            var offsetValues = offsets.Values.ToList();
            var totalPairs = offsetValues.Count * (offsetValues.Count - 1) / 2;
            
            if (totalPairs == 0)
                return 0.0;

            int conflictPairs = 0;
            const int tolerance = 1; // 1微秒容差

            for (int i = 0; i < offsetValues.Count; i++)
            {
                for (int j = i + 1; j < offsetValues.Count; j++)
                {
                    if (Math.Abs(offsetValues[i] - offsetValues[j]) < tolerance)
                    {
                        conflictPairs++;
                    }
                }
            }

            return (double)conflictPairs / totalPairs;
        }

        /// <summary>
        /// 计算任务启动时间的静态重叠率和重叠深度分析
        /// </summary>
        /// <param name="solution">任务Offset解</param>
        /// <param name="tasks">任务列表（用于获取执行时间信息）</param>
        /// <returns>综合重叠指标（考虑重叠率和重叠深度分布的均衡性）</returns>
        public static double CalculateOverlapRate(Dictionary<int, int> solution, List<TaskItem> tasks = null)
        {
            // 初始值设为0，只有在有重叠时才进行计算
            if (solution == null || solution.Count <= 1)
                return 0.0;

            try
            {
                // 如果没有提供任务列表，无法进行精确的重叠分析，返回0
                if (tasks == null || tasks.Count == 0)
                {
                    //Console.WriteLine($"⚠️ [重叠深度分析] 缺少任务列表，无法计算重叠指标");
                    return 0.0;
                }

                // 创建任务信息列表，包含offset和执行时间
                var taskInfos = new List<(string TaskName, int OffsetUs, int ExecutionTimeUs, int Priority)>();

                foreach (var task in tasks)
                {
                    if (task.Enabled &&
                        task.ExecutionTimeUs.HasValue && task.ExecutionTimeUs.Value > 0 &&
                        !string.IsNullOrEmpty(task.TaskName))
                    {
                        int offsetUs = solution.ContainsKey(task.TaskIndex) ? solution[task.TaskIndex] : (task.OffsetUs ?? 0);
                        taskInfos.Add((task.TaskName, offsetUs, task.ExecutionTimeUs.Value, task.Priority ?? int.MaxValue));
                    }
                }

                if (taskInfos.Count <= 1)
                    return 0.0;

                // 按offset排序，便于分析
                taskInfos = taskInfos.OrderBy(t => t.OffsetUs).ToList();

                //Console.WriteLine($"📊 [重叠深度分析] 分析 {taskInfos.Count} 个任务的重叠分布:");

                // Step 1: 建立时间线，统计每个时间点的任务堆叠深度
                var timelineEvents = new List<(int TimeUs, string EventType, string TaskName)>();
                
                foreach (var task in taskInfos)
                {
                    timelineEvents.Add((task.OffsetUs, "START", task.TaskName));
                    timelineEvents.Add((task.OffsetUs + task.ExecutionTimeUs, "END", task.TaskName));
                }
                
                // 按时间排序事件
                timelineEvents = timelineEvents.OrderBy(e => e.TimeUs).ThenBy(e => e.EventType == "END" ? 0 : 1).ToList();

                // Step 2: 模拟时间线，统计重叠深度分布
                var activeTasksStack = new List<string>();
                var overlapDepthDistribution = new Dictionary<int, int>(); // 深度 -> 持续时间(us)
                var maxOverlapDepth = 0;
                var totalTimeUs = 0;
                int lastTimeUs = 0;

                foreach (var eventGroup in timelineEvents.GroupBy(e => e.TimeUs))
                {
                    int currentTimeUs = eventGroup.Key;
                    
                    // 统计上一个时间段的重叠深度
                    if (lastTimeUs < currentTimeUs && activeTasksStack.Count > 0)
                    {
                        int duration = currentTimeUs - lastTimeUs;
                        int depth = activeTasksStack.Count;
                        totalTimeUs += duration;
                        
                        if (!overlapDepthDistribution.ContainsKey(depth))
                            overlapDepthDistribution[depth] = 0;
                        overlapDepthDistribution[depth] += duration;
                        
                        maxOverlapDepth = Math.Max(maxOverlapDepth, depth);
                    }

                    // 处理当前时间点的所有事件
                    foreach (var evt in eventGroup.OrderBy(e => e.EventType == "END" ? 0 : 1))
                    {
                        if (evt.EventType == "START")
                        {
                            activeTasksStack.Add(evt.TaskName);
                        }
                        else if (evt.EventType == "END")
                        {
                            activeTasksStack.Remove(evt.TaskName);
                        }
                    }
                    
                    lastTimeUs = currentTimeUs;
                }

                // Step 3: 计算重叠质量指标（添加NaN防护逻辑）
                double basicOverlapRate = 0.0;
                double weightedOverlapRate = 0.0;
                double overlapBalanceScore = 1.0; // 1表示完美均衡，0表示极度不均衡

                if (totalTimeUs > 0 && overlapDepthDistribution.Any())
                {
                    // 基础重叠率：有重叠的时间比例
                    int overlapTimeUs = overlapDepthDistribution.Where(kv => kv.Key > 1).Sum(kv => kv.Value);
                    basicOverlapRate = (double)overlapTimeUs / totalTimeUs;

                    // 只有存在重叠时才计算重叠相关指标
                    if (overlapTimeUs > 0 && maxOverlapDepth > 1)
                    {
                        // 加权重叠率：考虑重叠深度的严重程度
                        double weightedOverlapTimeUs = 0;
                        foreach (var kv in overlapDepthDistribution)
                        {
                            if (kv.Key > 1) // 只考虑真正的重叠
                            {
                                // 深度越大，权重指数增长（深度2权重1，深度3权重4，深度4权重9...）
                                double weight = Math.Pow(kv.Key - 1, 2);
                                weightedOverlapTimeUs += kv.Value * weight;
                            }
                        }
                        
                        // 防止除零，确保分母大于0
                        double denominator = totalTimeUs * Math.Pow(maxOverlapDepth - 1, 2);
                        if (denominator > 0)
                        {
                            weightedOverlapRate = weightedOverlapTimeUs / denominator;
                        }

                        // 重叠均衡性评分：基于深度分布的方差
                        var overlapDepths = overlapDepthDistribution.Where(kv => kv.Key > 1);
                        if (overlapDepths.Any())
                        {
                            // 计算重叠深度的加权平均和方差
                            double totalOverlapTime = overlapDepths.Sum(kv => kv.Value);
                            
                            if (totalOverlapTime > 0)
                            {
                                double weightedMean = overlapDepths.Sum(kv => kv.Key * kv.Value) / totalOverlapTime;
                                double variance = overlapDepths.Sum(kv => Math.Pow(kv.Key - weightedMean, 2) * kv.Value) / totalOverlapTime;
                                
                                // 确保variance为非负数，防止Math.Sqrt异常
                                variance = Math.Max(0, variance);
                                
                                // 方差越大，均衡性越差（0-1范围，1表示完美均衡）
                                double maxPossibleVariance = Math.Pow(Math.Max(1, maxOverlapDepth - 2), 2); // 防止负数
                                if (maxPossibleVariance > 0)
                                {
                                    overlapBalanceScore = Math.Max(0, 1 - (variance / (maxPossibleVariance + 1)));
                                }
                            }
                        }
                    }
                }
                


                // Step 4: 输出详细分析
                //Console.WriteLine($"📈 [重叠深度分布]:");
                foreach (var kv in overlapDepthDistribution.OrderBy(x => x.Key))
                {
                    double percentage = totalTimeUs > 0 ? (kv.Value * 100.0 / totalTimeUs) : 0;
                    string depthDesc = kv.Key == 1 ? "无重叠" : $"{kv.Key}任务堆叠";
                    //Console.WriteLine($"   {depthDesc}: {TaskUtility.ConvertToMilliseconds(kv.Value):F2}ms ({percentage:F1}%)");
                }

                //Console.WriteLine($"📊 [重叠质量指标]:");
                //Console.WriteLine($"   基础重叠率: {basicOverlapRate:F3} ({basicOverlapRate * 100:F1}%)");
                //Console.WriteLine($"   加权重叠率: {weightedOverlapRate:F3} ({weightedOverlapRate * 100:F1}%)");
                //Console.WriteLine($"   最大堆叠深度: {maxOverlapDepth}");
                //Console.WriteLine($"   重叠均衡性: {overlapBalanceScore:F3} (1=完美均衡, 0=极度不均衡)");

                // Step 5: 计算综合重叠指标（防护NaN）
                // 综合指标 = 加权重叠率 * (2 - 重叠均衡性)
                // 这样既惩罚高重叠率，又惩罚不均衡的重叠分布
                double compositeOverlapMetric = weightedOverlapRate * (2.0 - overlapBalanceScore);
                

                
                //Console.WriteLine($"🎯 [综合重叠指标]: {compositeOverlapMetric:F3} (越小越好)");

                return Math.Max(0.0, Math.Min(1.0, compositeOverlapMetric));

            }
            catch (Exception ex)
            {
                //Console.WriteLine($"❌ [重叠深度分析] 计算失败: {ex.Message}");
                return 0.0; // 发生异常时返回0，表示无重叠
            }
        }

        /// <summary>
        /// 检查启发式种子生成的可行性
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="maxOffsetUs">最大offset限制(微秒)</param>
        /// <returns>可行性检查结果</returns>
        private (bool Success, string ErrorMessage) CheckHeuristicSeedFeasibility(List<TaskItem> tasks, int maxOffsetUs)
        {
            try
            {
                //Console.WriteLine($"[预检查] 测试在最大offset({TaskUtility.ConvertToMilliseconds(maxOffsetUs):F3}ms)限制下的启发式种子生成可行性");

                // 过滤出可优化的任务（非手动输入的任务）
                var optimizableTasks = tasks.Where(t => !t.ManualInput).ToList();

                // 如果没有可优化的任务，认为是可行的
                if (optimizableTasks.Count == 0)
                {
                    //Console.WriteLine($"[预检查] 所有任务都已手动设置offset，无需检查启发式种子生成");
                    return (true, null);
                }

                // 测试所有基础启发式策略的种子生成
                var basicStrategies = new[]
                {
                    HeuristicSortStrategy.NonPreemptiveFirst,
                    HeuristicSortStrategy.HighPriorityFirst,
                    HeuristicSortStrategy.OffsetCalculatorBased // 新增：测试OffsetCalculator策略
                };

                int successfulStrategies = 0;

                foreach (var strategy in basicStrategies)
                {
                    var testTasks = CloneTaskList(optimizableTasks);
                    var seed = TaskUtility.GenerateHeuristicSeed(testTasks, _timeBaseUs, maxOffsetUs, strategy);

                    if (seed != null && seed.Count > 0)
                    {
                        // 检查生成的种子是否在各任务的约束范围内
                        bool isValid = true;
                        var taskIndexToTask = testTasks.ToDictionary(t => t.TaskIndex, t => t);
                        
                        foreach (var kvp in seed)
                        {
                            if (taskIndexToTask.ContainsKey(kvp.Key))
                            {
                                var task = taskIndexToTask[kvp.Key];
                                int taskMaxOffsetUs = task.MaxOffsetUs > 0 ? task.MaxOffsetUs : maxOffsetUs;
                                
                                if (kvp.Value > taskMaxOffsetUs + 1) // 允许1us容差
                                {
                                    //Console.WriteLine($"[预检查] 策略{strategy}生成的种子超出任务限制: TaskIndex={kvp.Key}, Offset={TaskUtility.ConvertToMilliseconds(kvp.Value):F3}ms, 任务限制={TaskUtility.ConvertToMilliseconds(taskMaxOffsetUs):F3}ms");
                                    isValid = false;
                                    break;
                                }
                            }
                        }

                        if (isValid)
                        {
                            successfulStrategies++;
                            //Console.WriteLine($"[预检查] 策略{strategy}启发式种子生成成功，{seed.Count}个任务offset均在限制范围内");
                        }
                    }
                    else
                    {
                        //Console.WriteLine($"[预检查] 策略{strategy}启发式种子生成失败，返回null");
                    }
                }

                // 如果至少有一个基础策略成功，认为是可行的
                if (successfulStrategies > 0)
                {
                    //Console.WriteLine($"[预检查] {successfulStrategies}/{basicStrategies.Length}个基础策略成功，启发式种子生成可行");
                    return (true, null);
                }
                else
                {
                    // 计算建议的最小offset值
                    var suggestedOffsetUs = CalculateSuggestedMinimumOffsetUs(optimizableTasks);
                    var errorMessage = $"当前最大offset设置({TaskUtility.ConvertToMilliseconds(maxOffsetUs):F1}ms)过小，无法为任务集生成合适的启发式初始解。" +
                                     $"根据任务特征分析，建议将最大offset设置为至少 {TaskUtility.ConvertToMilliseconds(suggestedOffsetUs):F1}ms 以获得可行的调度方案。";

                    //Console.WriteLine($"[预检查] 所有基础策略都失败，启发式种子生成不可行");
                    return (false, errorMessage);
                }
            }
            catch (Exception ex)
            {
                var errorMessage = $"启发式种子可行性检查失败: {ex.Message}";
                //Console.WriteLine($"[预检查异常] {errorMessage}");
                return (false, errorMessage);
            }
        }

        /// <summary>
        /// 计算建议的最小offset值
        /// </summary>
        /// <param name="tasks">可优化的任务列表</param>
        /// <returns>建议的最小offset值(微秒)</returns>
        private int CalculateSuggestedMinimumOffsetUs(List<TaskItem> tasks)
        {
            if (tasks == null || tasks.Count == 0)
                return 10000; // 默认建议值

            try
            {
                // 基于任务周期的统计特征来估算合理的offset范围
                var periodsUs = tasks.Select(t => t.PeriodUs).Where(p => p.HasValue && p.Value > 0).Select(p => p.Value).ToArray();
                if (periodsUs.Length == 0)
                    return 10000;

                var minPeriodUs = periodsUs.Min();
                var maxPeriodUs = periodsUs.Max();
                var avgPeriodUs = periodsUs.Average();
                
                // 建议的offset应该至少是最小周期的10%，并且不超过平均周期的50%
                var suggestedOffsetUs = Math.Max(minPeriodUs * 0.1, Math.Min(avgPeriodUs * 0.5, maxPeriodUs * 0.2));
                
                // 确保建议值不小于5ms(5000us)，不大于100ms(100000us)
                suggestedOffsetUs = Math.Max(5000, Math.Min(100000, suggestedOffsetUs));
                
                // 向上取整到最近的整数
                return (int)Math.Ceiling(suggestedOffsetUs);
            }
            catch
            {
                return 10000; // 异常情况下的默认建议值
            }
        }

        #endregion

        #region 共享启发式种子优化

        /// <summary>
        /// 预计算共享启发式种子，只针对昂贵的OffsetCalculator策略以保持算法多样性
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="maxOffsetUs">最大offset限制（微秒）</param>
        /// <returns>预计算的种子集合</returns>
        private SharedHeuristicSeeds PrecomputeSharedHeuristicSeeds(List<TaskItem> tasks, int maxOffsetUs)
        {
            var seeds = new SharedHeuristicSeeds();
            
            // 🎯 保守策略：只预计算最昂贵的OffsetCalculator策略
            // 其他轻量级策略(NonPreemptiveFirst、HighPriorityFirst等)保持独立计算以维持算法多样性
            var expensiveStrategies = new[]
            {
                HeuristicSortStrategy.OffsetCalculatorBased // 只缓存这个昂贵的策略
            };

            foreach (var strategy in expensiveStrategies)
            {
                try
                {
                    var startTime = DateTime.Now;
                    var seed = TaskUtility.GenerateHeuristicSeed(tasks, _timeBaseUs, maxOffsetUs, strategy);
                    var elapsed = DateTime.Now - startTime;
                    
                    if (seed != null && seed.Count > 0)
                    {
                        seeds.Seeds[strategy] = seed;
                        //Console.WriteLine($"💎 [共享缓存] OffsetCalculator策略种子生成成功，耗时{elapsed.TotalMilliseconds:F1}ms，{seed.Count}个任务");
                        //Console.WriteLine($"🎯 [共享缓存] OffsetCalculator种子已缓存，SA和GA将复用此昂贵计算结果");
                        //Console.WriteLine($"✨ [共享缓存] 其他轻量级策略将保持独立计算，维持算法多样性");
                    }
                    else
                    {
                        //Console.WriteLine($"❌ [共享缓存] OffsetCalculator策略种子生成失败");
                    }
                }
                catch (Exception ex)
                {
                    //Console.WriteLine($"❌ [共享缓存] OffsetCalculator策略种子生成异常: {ex.Message}");
                }
            }

            return seeds;
        }

        /// <summary>
        /// 使用共享种子运行模拟退火算法
        /// </summary>
        private OptimizationResultSummary RunSimulatedAnnealingWithSharedSeeds(List<TaskItem> tasks, 
            int maxOffsetUs, IntelligentSchedulingParameters parameters, SharedHeuristicSeeds sharedSeeds)
        {
            // 如果有共享种子，直接使用；否则回退到原方法
            if (sharedSeeds != null)
            {
                return RunSimulatedAnnealingWithPrecomputedSeeds(tasks, maxOffsetUs, parameters, sharedSeeds);
            }
            else
            {
                return RunSimulatedAnnealing(tasks, maxOffsetUs, parameters);
            }
        }

        /// <summary>
        /// 使用共享种子运行遗传算法
        /// </summary>
        private OptimizationResultSummary RunGeneticAlgorithmWithSharedSeeds(List<TaskItem> tasks, 
            int maxOffsetUs, IntelligentSchedulingParameters parameters, SharedHeuristicSeeds sharedSeeds)
        {
            // 如果有共享种子，直接使用；否则回退到原方法
            if (sharedSeeds != null)
            {
                return RunGeneticAlgorithmWithPrecomputedSeeds(tasks, maxOffsetUs, parameters, sharedSeeds);
            }
            else
            {
                return RunGeneticAlgorithm(tasks, maxOffsetUs, parameters);
            }
        }

        /// <summary>
        /// 使用预计算种子的模拟退火优化
        /// </summary>
        private OptimizationResultSummary RunSimulatedAnnealingWithPrecomputedSeeds(List<TaskItem> tasks, 
            int maxOffsetUs, IntelligentSchedulingParameters parameters, SharedHeuristicSeeds sharedSeeds)
        {
            try
            {
                //Console.WriteLine($"🔥 [模拟退火-智能缓存] 开始优化，最大offset={TaskUtility.ConvertToMilliseconds(maxOffsetUs):F3}ms");
                
                // 🎯 保守方案：检查是否有OffsetCalculator缓存，有则使用智能缓存模式
                if (sharedSeeds.HasSeed(HeuristicSortStrategy.OffsetCalculatorBased))
                {
                    //Console.WriteLine($"💎 [模拟退火-智能缓存] 检测到OffsetCalculator缓存，将复用昂贵计算结果");
                    // 这里暂时还是调用原方法，但传递了缓存信息供未来优化使用
                    // TODO: 未来可以深度集成到具体优化器中，实现精确的缓存控制
                }
                else
                {
                    //Console.WriteLine($"⚠️ [模拟退火-智能缓存] 未检测到OffsetCalculator缓存，将使用原始计算模式");
                }

                if (parameters.UseMultiStartStrategies)
                {
                    return RunMultiStartSimulatedAnnealingWithSeeds(tasks, maxOffsetUs, parameters, sharedSeeds);
                }
                else
                {
                    return RunSingleStartSimulatedAnnealingWithSeeds(tasks, maxOffsetUs, parameters, sharedSeeds);
                }
            }
            catch (Exception ex)
            {
                //Console.WriteLine($"❌ [模拟退火-智能缓存] 优化失败: {ex.Message}");
                return new OptimizationResultSummary
                {
                    Algorithm = "IntelligentCachedSimulatedAnnealing",
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 使用预计算种子的遗传算法优化
        /// </summary>
        private OptimizationResultSummary RunGeneticAlgorithmWithPrecomputedSeeds(List<TaskItem> tasks, 
            int maxOffsetUs, IntelligentSchedulingParameters parameters, SharedHeuristicSeeds sharedSeeds)
        {
            try
            {
                //Console.WriteLine($"🧬 [遗传算法-智能缓存] 开始优化，最大offset={TaskUtility.ConvertToMilliseconds(maxOffsetUs):F3}ms");

                // 🎯 保守方案：检查是否有OffsetCalculator缓存，有则使用智能缓存模式
                if (sharedSeeds.HasSeed(HeuristicSortStrategy.OffsetCalculatorBased))
                {
                    //Console.WriteLine($"💎 [遗传算法-智能缓存] 检测到OffsetCalculator缓存，将复用昂贵计算结果");
                    // 这里暂时还是调用原方法，但传递了缓存信息供未来优化使用
                    // TODO: 未来可以深度集成到具体优化器中，实现精确的缓存控制
                }
                else
                {
                    //Console.WriteLine($"⚠️ [遗传算法-智能缓存] 未检测到OffsetCalculator缓存，将使用原始计算模式");
                }

                if (parameters.UseMultiStartStrategies)
                {
                    return RunMultiStartGeneticAlgorithmWithSeeds(tasks, maxOffsetUs, parameters, sharedSeeds);
                }
                else
                {
                    return RunSingleStartGeneticAlgorithmWithSeeds(tasks, maxOffsetUs, parameters, sharedSeeds);
                }
            }
            catch (Exception ex)
            {
                //Console.WriteLine($"❌ [遗传算法-智能缓存] 优化失败: {ex.Message}");
                return new OptimizationResultSummary
                {
                    Algorithm = "IntelligentCachedGeneticAlgorithm",
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        // 智能缓存实现：保持原始算法逻辑，但OffsetCalculator缓存在TaskUtility级别生效
        private OptimizationResultSummary RunMultiStartSimulatedAnnealingWithSeeds(List<TaskItem> tasks, 
            int maxOffsetUs, IntelligentSchedulingParameters parameters, SharedHeuristicSeeds sharedSeeds)
        {
            // 🎯 效果：OffsetCalculator策略已经在预计算阶段被缓存，不会重复计算
            // ✨ 优势：其他策略(NonPreemptiveFirst等)仍然独立计算，保持多样性
            //Console.WriteLine($"🔄 [多起点SA-智能缓存] 保持原始算法逻辑，OffsetCalculator缓存将在TaskUtility级别生效");
            return RunMultiStartSimulatedAnnealing(tasks, maxOffsetUs, parameters);
        }

        private OptimizationResultSummary RunSingleStartSimulatedAnnealingWithSeeds(List<TaskItem> tasks, 
            int maxOffsetUs, IntelligentSchedulingParameters parameters, SharedHeuristicSeeds sharedSeeds)
        {
            //Console.WriteLine($"🔄 [单起点SA-智能缓存] 保持原始算法逻辑，OffsetCalculator缓存将在TaskUtility级别生效");
            return RunSingleStartSimulatedAnnealing(tasks, maxOffsetUs, parameters);
        }

        private OptimizationResultSummary RunMultiStartGeneticAlgorithmWithSeeds(List<TaskItem> tasks, 
            int maxOffsetUs, IntelligentSchedulingParameters parameters, SharedHeuristicSeeds sharedSeeds)
        {
            //Console.WriteLine($"🔄 [多起点GA-智能缓存] 保持原始算法逻辑，OffsetCalculator缓存将在TaskUtility级别生效");
            return RunMultiStartGeneticAlgorithm(tasks, maxOffsetUs, parameters);
        }

        private OptimizationResultSummary RunSingleStartGeneticAlgorithmWithSeeds(List<TaskItem> tasks, 
            int maxOffsetUs, IntelligentSchedulingParameters parameters, SharedHeuristicSeeds sharedSeeds)
        {
            //Console.WriteLine($"🔄 [单起点GA-智能缓存] 保持原始算法逻辑，OffsetCalculator缓存将在TaskUtility级别生效");
            return RunSingleStartGeneticAlgorithm(tasks, maxOffsetUs, parameters);
        }

        #endregion
    }

    #region 数据结构定义

    /// <summary>
    /// 智能调度参数
    /// </summary>
    public class IntelligentSchedulingParameters
    {
        /// <summary>
        /// 是否使用智能模拟退火
        /// </summary>
        public bool UseIntelligentSA { get; set; } = true;

        /// <summary>
        /// 是否使用改进遗传算法
        /// </summary>
        public bool UseImprovedGA { get; set; } = true;

        /// <summary>
        /// 找到完美解时是否提前停止
        /// </summary>
        public bool UseEarlyStoppingForPerfectSolution { get; set; } = false;

        /// <summary>
        /// 是否使用早停策略
        /// </summary>
        public bool UseEarlyStopping { get; set; } = false;

        /// <summary>
        /// 模拟退火最大迭代次数
        /// </summary>
        public int SAMaxIterations { get; set; } = 3000;//12000;

        /// <summary>
        /// 模拟退火最大重启次数
        /// </summary>
        public int SAMaxRestarts { get; set; } = 3;

        /// <summary>
        /// 遗传算法种群大小
        /// </summary>
        public int GAPopulationSize { get; set; } = 100;

        /// <summary>
        /// 遗传算法最大世代数
        /// </summary>
        public int GAMaxGenerations { get; set; } = 200;//800;

        /// <summary>
        /// 是否使用多起点策略
        /// </summary>
        public bool UseMultiStartStrategies { get; set; } = false;

        /// <summary>
        /// 是否使用基础策略
        /// </summary>
        public bool UseBasicStrategies { get; set; } = true;

        /// <summary>
        /// 是否使用扩展策略
        /// </summary>
        public bool UseExtendedStrategies { get; set; } = true;

        /// <summary>
        /// 是否使用高级策略
        /// </summary>
        public bool UseAdvancedStrategies { get; set; } = true;


        /// <summary>
        /// 随机种子（用于确定性测试，null表示使用时间种子）
        /// </summary>
        public int? RandomSeed { get; set; } = null;

        /// <summary>
        /// 是否启用确定性模式（使用固定随机种子确保结果可重现）
        /// </summary>
        public bool EnableDeterministicMode { get; set; } = false;
    }

    /// <summary>
    /// 智能调度结果
    /// </summary>
    public class IntelligentSchedulingResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 总耗时
        /// </summary>
        public TimeSpan TotalTime { get; set; }

        /// <summary>
        /// 原始最大Offset值
        /// </summary>
        public int OriginalMaxOffsetUs { get; set; }

        /// <summary>
        /// 测试的Offset序列
        /// </summary>
        public List<int> OffsetSequence { get; set; } = new List<int>();

        /// <summary>
        /// 每个步骤的优化结果
        /// </summary>
        public List<DualTrackOptimizationResult> StepResults { get; set; } = new List<DualTrackOptimizationResult>();

        /// <summary>
        /// 最终最优结果
        /// </summary>
        public OptimizationResultSummary FinalBestResult { get; set; }

        /// <summary>
        /// 优化决策
        /// </summary>
        public OptimizationDecision OptimizationDecision { get; set; }

        /// <summary>
        /// 参数
        /// </summary>
        public IntelligentSchedulingParameters Parameters { get; set; }

        /// <summary>
        /// 是否提前停止
        /// </summary>
        public int? EarlyStoppedAt { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// 双轨优化结果
    /// </summary>
    public class DualTrackOptimizationResult
    {
        /// <summary>
        /// 当前最大Offset值
        /// </summary>
        public int MaxOffsetUs { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 总耗时
        /// </summary>
        public TimeSpan TotalTime { get; set; }

        /// <summary>
        /// 模拟退火结果
        /// </summary>
        public OptimizationResultSummary SimulatedAnnealingResult { get; set; }

        /// <summary>
        /// 遗传算法结果
        /// </summary>
        public OptimizationResultSummary GeneticAlgorithmResult { get; set; }

        /// <summary>
        /// 最优结果
        /// </summary>
        public OptimizationResultSummary BestResult { get; set; }

        /// <summary>
        /// 决策理由
        /// </summary>
        public string Decision { get; set; }
    }

    /// <summary>
    /// 优化结果摘要
    /// </summary>
    public class OptimizationResultSummary
    {
        /// <summary>
        /// 算法名称
        /// </summary>
        public string Algorithm { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 最佳成本
        /// </summary>
        public double BestCost { get; set; }

        /// <summary>
        /// 总迭代次数
        /// </summary>
        public int TotalIterations { get; set; }

        /// <summary>
        /// 优化耗时
        /// </summary>
        public TimeSpan OptimizationTime { get; set; }

        /// <summary>
        /// 优化后的任务列表
        /// </summary>
        public List<TaskItem> OptimizedTasks { get; set; }

        /// <summary>
        /// 可调度性分析结果
        /// </summary>
        public SchedulabilityResult SchedulabilityResult { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 多起点策略详细结果（仅在使用多起点策略时有值）
        /// </summary>
        public MultiStartResult MultiStartDetails { get; set; }

        /// <summary>
        /// 是否为最优解（所有任务满足截止时间且响应时间比率非常小）
        /// 注意：为了确保渐进式搜索能够找到最小Offset，这里的条件设置得更严格
        /// </summary>
        public bool IsOptimal => Success && 
            (SchedulabilityResult?.Statistics?.DeadlineMissCount ?? int.MaxValue) == 0 &&
            (SchedulabilityResult?.Statistics?.MaxResponseTimeRatio ?? double.MaxValue) < 0.1; // 从0.8改为0.1，更严格

        /// <summary>
        /// 统一挑选阶段缓存的额外指标（用于报告/下游复用，避免重复计算）
        /// </summary>
        public SelectionExtraMetrics ExtraMetrics { get; set; }
    }

    /// <summary>
    /// 优化决策信息
    /// </summary>
    public class OptimizationDecision
    {
        /// <summary>
        /// 最终选择的最大Offset值
        /// </summary>
        public int FinalMaxOffsetUs { get; set; }

        /// <summary>
        /// 原始用户设定的最大Offset值
        /// </summary>
        public int OriginalMaxOffsetUs { get; set; }

        /// <summary>
        /// Offset减少量
        /// </summary>
        public int OffsetReductionUs { get; set; }

        /// <summary>
        /// 测试的Offset值数量
        /// </summary>
        public int TestedOffsetCount { get; set; }

        /// <summary>
        /// 性能改进度
        /// </summary>
        public double PerformanceImprovement { get; set; }

        /// <summary>
        /// 优化效果评价
        /// </summary>
        public string OptimizationEffectiveness { get; set; }

        /// <summary>
        /// 总优化时间
        /// </summary>
        public TimeSpan TotalOptimizationTime { get; set; }

        /// <summary>
        /// 决策总结
        /// </summary>
        public string DecisionSummary { get; set; }
    }

    /// <summary>
    /// 策略候选结果 - 用于统一选择逻辑
    /// </summary>
    public class StrategyCandidateResult
    {
        /// <summary>
        /// 算法名称
        /// </summary>
        public string Algorithm { get; set; }

        /// <summary>
        /// 策略名称
        /// </summary>
        public string Strategy { get; set; }

        /// <summary>
        /// 成本值
        /// </summary>
        public double Cost { get; set; }

        /// <summary>
        /// 解决方案（offset字典）
        /// </summary>
        public Dictionary<int, int> Solution { get; set; }

        /// <summary>
        /// 原始优化结果引用
        /// </summary>
        public OptimizationResultSummary OriginalResult { get; set; }

        /// <summary>
        /// 若来源于多起点策略，则指向对应的策略结果对象，便于回填指标
        /// </summary>
        public MultiStartStrategyResult SourceStrategyResult { get; set; }

        /// <summary>
        /// 已计算的额外指标（挑选阶段计算并缓存）
        /// </summary>
        public SelectionExtraMetrics ExtraMetrics { get; set; }
    }

    /// <summary>
    /// 统一挑选阶段的额外指标
    /// </summary>
    public class SelectionExtraMetrics
    {
        public double ConflictRate { get; set; }
        public double StaticOverlapRate { get; set; }
        public double StartOverlapRate { get; set; }
        public double DynamicBusyAtStartRatio { get; set; }
        public int MaxOffsetUs { get; set; }
        public double PriorityAlignmentCost { get; set; }
        public double StartClusteringScore { get; set; }
        public int PeakStartConcurrency { get; set; }
        public double StartConcurrencyVariance { get; set; }
    }

    /// <summary>
    /// 共享启发式种子数据结构
    /// </summary>
    public class SharedHeuristicSeeds
    {
        /// <summary>
        /// 按策略分组的预计算种子
        /// </summary>
        public Dictionary<HeuristicSortStrategy, Dictionary<int, int>> Seeds { get; set; } = new Dictionary<HeuristicSortStrategy, Dictionary<int, int>>();

        /// <summary>
        /// 获取指定策略的种子（如果存在）
        /// </summary>
        /// <param name="strategy">策略</param>
        /// <returns>种子字典，如果不存在则返回null</returns>
        public Dictionary<int, int> GetSeed(HeuristicSortStrategy strategy)
        {
            return Seeds.TryGetValue(strategy, out var seed) ? seed : null;
        }

        /// <summary>
        /// 检查是否包含指定策略的种子
        /// </summary>
        /// <param name="strategy">策略</param>
        /// <returns>是否包含</returns>
        public bool HasSeed(HeuristicSortStrategy strategy)
        {
            return Seeds.ContainsKey(strategy) && Seeds[strategy] != null && Seeds[strategy].Count > 0;
        }
    }

    #endregion
}
