using System;
using System.Collections.Generic;
using System.Linq;

namespace CalculateOffset
{
    /// <summary>
    /// Offset计算器，实现任务Offset值的计算
    /// </summary>
    public class OffsetCalculator
    {
        // 单例实例，用于其他类访问
        private static OffsetCalculator _instance;
        public static OffsetCalculator Instance => _instance;
        
        // 时基(微秒)
        private int _timeBaseUs;
        
        // 最大允许的Offset值(微秒)
        private int _maxOffsetUs;

        // 计算得到的最佳Offset值(微秒)
        public int CalcMaxOffsetUs;

        // 计算得到的最终的任务排布的方差值
        public double CalcVariance;

        // 计算得到的最佳的任务排布的方差值
        public double BestVariance;
        
        // 最多时间点的任务数量
        public int MaxTasksCount { get; private set; }
        
        // 最大任务数出现的比例
        public double MaxTasksRatio { get; private set; }

        
        // 二分查找精度(微秒)
        private const int BINARY_SEARCH_PRECISION = 10000; // 10000微秒 = 10ms

        // 任务权重缓存
        private Dictionary<int, double> _taskWeightsCache = new Dictionary<int, double>();

        // 构造函数
        public OffsetCalculator(int timeBaseUs, int maxOffsetUs)
        {
            _timeBaseUs = Math.Max(TaskUtility.MIN_TIME_BASE_US, timeBaseUs); // 确保时基不小于最小值
            _maxOffsetUs = Math.Max(0, maxOffsetUs);
            
            // 更新单例实例
            _instance = this;
        }
        
        /// <summary>
        /// 计算最佳的Offset值
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="enableMinOffsetSearch">是否启用二分法自动寻找最小Offset的优化，默认为true。设为false时只计算初始解，提升计算效率</param>
        /// <returns>是否计算成功</returns>
        public bool CalculateOptimalOffsets(List<TaskItem> tasks, bool enableMinOffsetSearch = true)
        {
            return CalculateOffsetsInternal(tasks, enableMinOffsetSearch);
        }
        
        /// <summary>
        /// 快速计算Offset值（跳过二分法优化，适用于初始解生成）
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <returns>是否计算成功</returns>
        public bool CalculateInitialOffsets(List<TaskItem> tasks)
        {
            return CalculateOffsetsInternal(tasks, enableMinOffsetSearch: false);
        }
        
        /// <summary>
        /// 内部Offset计算方法
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="enableMinOffsetSearch">是否启用二分法自动寻找最小Offset的优化</param>
        /// <returns>是否计算成功</returns>
        private bool CalculateOffsetsInternal(List<TaskItem> tasks, bool enableMinOffsetSearch)
        {
            // 过滤出有效任务（至少有周期值的任务）
            var validTasks = TaskUtility.GetValidTasks(tasks);
            
            if (validTasks.Count == 0)
            {
                return false; // 没有有效任务
            }
            
            // 注意：不再重新分配TaskIndex，保持传入时的原始索引
            // 这确保了与外部系统的索引映射关系不被破坏
            
            // 重置权重缓存
            _taskWeightsCache.Clear();
            
            // 预先计算所有任务的权重
            var taskWeights = CalculateTaskWeights(validTasks);
            
            // 保存原始设置
            int originalMaxOffsetUs = _maxOffsetUs;
            
            // 找出最大周期值的2倍作为参考最大offset
            int maxPeriodUs = validTasks.Max(t => t.PeriodUs ?? 0) * 2;
            
            // 首先根据前置任务关系进行拓扑排序，确保前置任务在依赖任务之前调度
            List<TaskItem> topologicallySortedTasks;
            try
            {
                topologicallySortedTasks = TaskValidator.TopologicalSort(validTasks);
            }
            catch (CircularDependencyException ex)
            {
                return false; // 循环依赖导致计算失败
            }
            
            // 在拓扑排序的基础上，对没有依赖关系的任务按优先级和周期进行二次排序
            var sortedTasks = SortTasksWithPrerequisites(topologicallySortedTasks);
            
            // 创建原始任务副本
            var originalTasks = CloneTasks(sortedTasks);
            
            // 根据是否启用优化模式决定使用的最大offset值
            int effectiveMaxOffsetUs;
            if (enableMinOffsetSearch)
            {
                // 优化模式：使用最大周期的2倍作为初始搜索范围
                effectiveMaxOffsetUs = maxPeriodUs;
                _maxOffsetUs = effectiveMaxOffsetUs;
            }
            else
            {
                // 快速模式：使用当前设定的_maxOffsetUs值（可能是递减的限制值）
                effectiveMaxOffsetUs = _maxOffsetUs;
            }
            
            // 计算LCM一次，避免重复计算（性能优化）
            int lcm = TaskUtility.CalculateHyperPeriod(validTasks);
            
            // 使用确定的最大偏移值进行计算和评估，传入预计算的LCM
            bool initialCalculationSuccess = PerformOffsetCalculation(originalTasks, taskWeights, lcm);
            CalcVariance = initialCalculationSuccess ? EvaluateVariance(originalTasks, lcm) : double.MaxValue;
            BestVariance = CalcVariance;
            
            // 存储当前最佳结果的任务列表
            List<TaskItem> bestTasks = CloneTasks(originalTasks);
            CalcMaxOffsetUs = effectiveMaxOffsetUs;
            
            // 根据参数决定是否启用二分法优化寻找最小Offset
            if (enableMinOffsetSearch)
            {
                // 二分查找的范围
                int low = 10000;
                int high = effectiveMaxOffsetUs;
                
                // 二分查找循环 - 使用定义的精度常量
                while ((high - low) > BINARY_SEARCH_PRECISION)
                {
                    // 计算中点并四舍五入到BINARY_SEARCH_PRECISION的倍数
                    int mid = (low + high) / 2;
                    mid = (int)(Math.Round(mid / (double)BINARY_SEARCH_PRECISION) * BINARY_SEARCH_PRECISION);
                    
                    // 避免重复计算相同的值
                    if (mid == low || mid == high)
                        break;
                    
                    // 使用当前mid值尝试计算
                    var testTasks = CloneTasks(sortedTasks);
                    _maxOffsetUs = mid;
                    
                    bool success = PerformOffsetCalculation(testTasks, taskWeights, lcm);
                    if (!success) {
                        // 如果计算失败，提高下限
                        low = mid;
                        continue;
                    }

                    // 评估当前配置方差
                    double currentVariance = EvaluateVariance(testTasks, lcm);

                    if (currentVariance < BestVariance)
                    {
                        BestVariance = currentVariance;
                    }

                    // 如果方差越小，表示任务分布越均匀，浮点数允许误差在0.0001之内
                    if (currentVariance <= BestVariance + 0.0001)
                    {
                        // 方差不变或更好，记录当前值并尝试更小的范围
                        CalcMaxOffsetUs = mid;
                        bestTasks = CloneTasks(testTasks);
                        high = mid;
                    }
                    else
                    {
                        // 性能有任何下降，尝试更大的值
                        low = mid;
                    }
                }
            }
            else
            {
                // 跳过二分法优化，直接使用初始计算结果
                // 这种模式用于快速获得初始解，适用于启发式种子生成等场景
            }
            
            // 比较计算得到的最佳最大offset与用户设定的原始值
            // 只有当计算值小于原始值时才使用计算值
            bool useCalculatedOffset = CalcMaxOffsetUs < originalMaxOffsetUs;

            // 恢复原始最大Offset设置或使用新计算的值
            if (useCalculatedOffset)
            {
                // 使用计算出的最佳值
                _maxOffsetUs = CalcMaxOffsetUs;

                // 通过TaskIndex拷贝偏移量，而不是依赖索引位置
                foreach (var bestTask in bestTasks)
                {
                    // 在原始任务列表中找到对应的任务
                    var originalTask = tasks.FirstOrDefault(t => t.TaskIndex == bestTask.TaskIndex);
                    if (originalTask != null && !originalTask.ManualInput)
                    {
                        originalTask.OffsetUs = bestTask.OffsetUs;
                    }
                }
            }
            else
            {
                // 恢复原始设置
                _maxOffsetUs = originalMaxOffsetUs;

                // 通过TaskIndex拷贝偏移量
                foreach (var originalTask in originalTasks)
                {
                    // 在原始任务列表中找到对应的任务
                    var task = tasks.FirstOrDefault(t => t.TaskIndex == originalTask.TaskIndex);
                    if (task != null && !task.ManualInput)
                    {
                        task.OffsetUs = originalTask.OffsetUs;
                    }
                }
            }

            // 统计最终计算后任务的最大任务情况，传入预计算的LCM
            CalculateMaxTaskPoint(tasks, lcm);
            
            // 最终验证：检查所有任务是否满足前置任务时间约束
            var (isValid, violations) = ValidatePrerequisiteTimeConstraints(tasks);
            // 静默验证前置任务时间约束

            return true;
        }
        
        /// <summary>
        /// 计算最大任务点（调试用）
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="lcm">预计算的LCM值，避免重复计算</param>
        private void CalculateMaxTaskPoint(List<TaskItem> tasks, int lcm)
        {
            // 过滤出有效任务
            var validTasks = tasks.Where(t => t.PeriodUs.HasValue && t.PeriodUs.Value > 0 && t.OffsetUs.HasValue).ToList();
            
            if (validTasks.Count == 0) return;
            
            // 内部时基步长(微秒)
            int internalTimeBaseUs = TaskUtility.GetInternalTimeBaseUs(_timeBaseUs);
            
            // 创建任务计数映射
            Dictionary<int, int> taskCountAtTime = new Dictionary<int, int>();
            
            foreach (var task in validTasks)
            {
                int offsetUs = task.OffsetUs.Value;
                int periodUs = task.PeriodUs.Value;
                int executionTimeUs = task.ExecutionTimeUs ?? TaskUtility.MIN_EXECUTION_TIME_US;
                
                for (int time = offsetUs; time < lcm; time += periodUs)
                {
                    for (int execTime = time; execTime < time + executionTimeUs; execTime += internalTimeBaseUs)
                    {
                        int endTimePoint = time + executionTimeUs;
                        
                        if (!taskCountAtTime.ContainsKey(execTime))
                            taskCountAtTime[execTime] = 0;
                        taskCountAtTime[execTime]++;
                    }
                }
            }

            // 更新最大任务时间点和数量
            if (taskCountAtTime.Count > 0)
            {
                // 找出最大任务数量
                int maxTaskTimePointCount = taskCountAtTime.Values.Max();
                MaxTasksCount = maxTaskTimePointCount;

                // 找出具有最大任务数的时间点个数
                var maxEntries = taskCountAtTime.Where(kv => kv.Value == maxTaskTimePointCount).ToList();
                int maxTaskTimePoints = maxEntries.Count;

                // 计算最大任务数出现的比例：具有最大任务数的时间点个数 / 总时间点个数
                MaxTasksRatio = (double)maxTaskTimePoints / taskCountAtTime.Count;
            }
        }
        
        /// <summary>
        /// 执行偏移量计算的核心逻辑
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="taskWeights">任务权重字典，以TaskIndex为键</param>
        /// <param name="lcm">预计算的LCM值，避免重复计算</param>
        /// <returns>是否计算成功</returns>
        private bool PerformOffsetCalculation(List<TaskItem> tasks, Dictionary<int, double> taskWeights, int lcm)
        {
            if (tasks.Count == 0) return false;
            
            // 如果未提供权重字典，则计算任务权重
            if (taskWeights == null)
            {
                taskWeights = CalculateTaskWeights(tasks);
            }
            
            // 创建时间线，用于跟踪每个时间点的CPU占用情况
            Dictionary<int, double> timeline = new Dictionary<int, double>();
            
            // 内部时基步长(微秒)
            int internalTimeBaseUs = TaskUtility.GetInternalTimeBaseUs(_timeBaseUs);
            
            // 第一次遍历：处理手动设置了Offset的任务
            foreach (var task in tasks.Where(t => t.ManualInput && t.OffsetUs.HasValue))
            {
                // 使用该任务的最大offset限制，如果未设置则使用全局限制
                int taskMaxOffsetUs = task.MaxOffsetUs > 0 ? task.MaxOffsetUs : _maxOffsetUs;
                
                // 无需转换，直接使用微秒值
                int offsetInUs = Math.Min(task.OffsetUs.Value, taskMaxOffsetUs);
                
                // 验证手动设置的Offset是否满足前置任务约束
                int minStartTimeUs = CalculateMinStartTime(task, tasks);
                if (offsetInUs < minStartTimeUs)
                {
                    // 如果手动设置的值不满足前置任务约束，使用约束的最小值
                    offsetInUs = minStartTimeUs;
                    // 更新任务的Offset显示值
                    task.OffsetUs = offsetInUs;
                }
                
                // 确保偏移量是内部时基的整数倍
                offsetInUs = (int)(Math.Round(offsetInUs / (double)internalTimeBaseUs) * internalTimeBaseUs);
                
                // 更新任务的内部微秒偏移量(但不更改显示值)
                UpdateTimeline(timeline, task, lcm, offsetInUs, taskWeights);
            }
            
            // 第二次遍历：计算未设置Offset的任务
            foreach (var task in tasks.Where(t => !t.ManualInput || !t.OffsetUs.HasValue))
            {
                // 计算最佳Offset(微秒精度，确保是内部时基的整数倍)
                int bestOffsetInUs = FindBestOffset(timeline, task, lcm, taskWeights, tasks);
                
                // 直接赋值微秒值
                task.OffsetUs = bestOffsetInUs;
                    
                // 更新时间线(使用内部微秒偏移量)
                UpdateTimeline(timeline, task, lcm, bestOffsetInUs, taskWeights);
            }
            
            return true;
        }
        
        /// <summary>
        /// 更新时间线
        /// </summary>
        /// <param name="timeline">时间线</param>
        /// <param name="task">任务</param>
        /// <param name="lcm">周期最小公倍数</param>
        /// <param name="offsetInUs">内部微秒精度的偏移量</param>
        /// <param name="taskWeights">任务权重字典</param>
        private void UpdateTimeline(Dictionary<int, double> timeline, TaskItem task, int lcm, int offsetInUs, Dictionary<int, double> taskWeights = null)
        {
            // 直接使用微秒值
            int periodUs = task.PeriodUs.Value;
            
            // 执行时间直接使用微秒值
            int executionTimeUs = task.ExecutionTimeUs ?? TaskUtility.MIN_EXECUTION_TIME_US;
            
            // 获取任务权重 - 优化为O(1)查找
            double weight = 1.0;
            if (taskWeights != null && taskWeights.ContainsKey(task.TaskIndex))
            {
                weight = taskWeights[task.TaskIndex];
            }
            else if (taskWeights != null)
            {
                weight = 1.0; // 默认权重
            }
            
            // 计算任务对时间线的影响
            for (int time = offsetInUs; time < lcm; time += periodUs)
            {
                // 任务执行的时间范围，计算执行结束时间点
                int endTimePoint = time + executionTimeUs;
                
                // 以用户设定的 US_STEP 精度步长标记执行中的时间点
                for (int t = time; t < endTimePoint && t < lcm; t += TaskUtility.US_STEP)
                {
                    // 构建时间线的索引
                    if (!timeline.ContainsKey(t))
                        timeline[t] = 0;
                    timeline[t] += weight; // 使用一致的权重计算
                }
            }
        }
        
        /// <summary>
        /// 寻找最佳的Offset值
        /// </summary>
        /// <param name="timeline">时间线</param>
        /// <param name="task">任务</param>
        /// <param name="lcm">周期最小公倍数</param>
        /// <param name="taskWeights">任务权重字典</param>
        /// <param name="allTasks">所有任务列表，用于计算前置任务约束</param>
        /// <returns>最佳Offset值(微秒精度)</returns>
        private int FindBestOffset(Dictionary<int, double> timeline, TaskItem task, int lcm, Dictionary<int, double> taskWeights = null, List<TaskItem> allTasks = null)
        {
            // 直接使用微秒值
            int periodUs = task.PeriodUs.Value;
            
            // 计算前置任务约束的最小启动时间
            int minStartTimeUs = CalculateMinStartTime(task, allTasks);
            
            // 使用该任务的最大offset限制，如果未设置则使用全局限制
            int taskMaxOffsetUs = task.MaxOffsetUs > 0 ? task.MaxOffsetUs : _maxOffsetUs;
            
            // 限制搜索范围在最大Offset值内(微秒精度)，确保非负
            int searchRange = Math.Max(0, Math.Min(taskMaxOffsetUs, periodUs - 1));
            
            // 确保搜索起始点不小于前置任务约束的最小启动时间
            int searchStart = Math.Max(minStartTimeUs, 0);
            
            // 如果最小启动时间超过搜索范围，调整搜索范围
            if (searchStart > searchRange)
            {
                searchRange = searchStart;
            }
            
            // 内部时基步长(微秒)
            int internalTimeBaseUs = TaskUtility.GetInternalTimeBaseUs(_timeBaseUs);

            // 执行时间直接使用微秒值
            int executionTimeUs = TaskUtility.GetTaskExecutionTimeUs(task);
            
            // 获取任务权重 - 优化为O(1)查找
            double weight;
            if (taskWeights != null && taskWeights.ContainsKey(task.TaskIndex))
            {
                weight = taskWeights[task.TaskIndex];
            }
            else if (taskWeights != null)
            {
                weight = 1.0; // 默认权重
            }
            else
            {
                weight = 1.0;
            }
            
            // 存储每个可能的Offset值的冲突度量
            Dictionary<int, double> conflictMetrics = new Dictionary<int, double>();
            
            // 搜索步长必须是内部时基的整数倍，确保结果偏移量满足时基倍数要求
            int stepUs = internalTimeBaseUs;
            
            // 确保搜索起始点是时基的整数倍
            searchStart = (int)(Math.Ceiling(searchStart / (double)stepUs) * stepUs);
            
            // 评估每个可能的Offset值，从前置任务约束的最小时间开始搜索
            for (int offset = searchStart; offset <= searchRange; offset += stepUs)
            {
                double totalConflict = 0;
                
                // 计算该Offset下，任务在整个LCM周期内的冲突情况
                for (int time = offset; time < lcm; time += periodUs)
                {
                    // 计算执行结束时间点
                    int endTimePoint = time + executionTimeUs;
                    
                    // 以用户设定的 US_STEP 精度步长计算任务执行时间内的冲突
                    for (int t = time; t < endTimePoint && t < lcm; t += TaskUtility.US_STEP)
                    {
                        // 构建时间线索引
                        if (timeline.ContainsKey(t))
                        {
                            totalConflict += timeline[t];
                        }
                    }
                }
                
                // 存储该Offset的冲突度量
                conflictMetrics[offset] = totalConflict;
            }
            
            // 检查是否有可用的偏移量
            if (conflictMetrics.Count == 0)
            {
                // 如果没有可用的偏移量，使用最小启动时间作为默认值
                return minStartTimeUs;
            }
            
            // 寻找冲突最小的Offset值
            int bestOffset = conflictMetrics.OrderBy(kv => kv.Value).ThenBy(kv => kv.Key).First().Key;
            
            // 确保返回的偏移量是内部时基的整数倍
            bestOffset = (int)(Math.Round(bestOffset / (double)internalTimeBaseUs) * internalTimeBaseUs);
            
            return bestOffset;
        }
        
        /// <summary>
        /// 计算任务的最小启动时间，基于前置任务的完成时间
        /// </summary>
        /// <param name="task">当前任务</param>
        /// <param name="allTasks">所有任务列表</param>
        /// <returns>最小启动时间(微秒)</returns>
        private int CalculateMinStartTime(TaskItem task, List<TaskItem> allTasks)
        {
            if (allTasks == null || string.IsNullOrWhiteSpace(task.PrerequisiteTasks))
            {
                return 0; // 没有前置任务约束
            }
            
            // 解析前置任务名称
            var prerequisiteNames = TaskValidator.ParsePrerequisiteTaskNames(task.PrerequisiteTasks);
            if (prerequisiteNames.Count == 0)
            {
                return 0;
            }
            
            int maxPrerequisiteEndTime = 0;
            
            // 查找所有前置任务，计算它们的最晚完成时间
            foreach (var prerequisiteName in prerequisiteNames)
            {
                var prerequisiteTask = allTasks.FirstOrDefault(t => 
                    string.Equals(t.TaskName?.Trim(), prerequisiteName, StringComparison.OrdinalIgnoreCase));
                
                if (prerequisiteTask != null && prerequisiteTask.OffsetUs.HasValue)
                {
                    // 计算前置任务的完成时间 = Offset + ExecutionTime
                    int prerequisiteOffsetUs = prerequisiteTask.OffsetUs.Value;
                    int prerequisiteExecutionUs = prerequisiteTask.ExecutionTimeUs ?? TaskUtility.MIN_EXECUTION_TIME_US;
                    int prerequisiteEndTimeUs = prerequisiteOffsetUs + prerequisiteExecutionUs;
                    
                    maxPrerequisiteEndTime = Math.Max(maxPrerequisiteEndTime, prerequisiteEndTimeUs);
                }
            }
            
            return maxPrerequisiteEndTime;
        }
        
        /// <summary>
        /// 验证所有任务是否满足前置任务时间约束
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <returns>验证结果和详细信息</returns>
        public (bool isValid, List<string> violations) ValidatePrerequisiteTimeConstraints(List<TaskItem> tasks)
        {
            var violations = new List<string>();
            bool isValid = true;
            
            foreach (var task in tasks)
            {
                if (string.IsNullOrWhiteSpace(task.PrerequisiteTasks) || !task.OffsetUs.HasValue)
                    continue;
                    
                int minStartTimeUs = CalculateMinStartTime(task, tasks);
                int actualOffsetUs = task.OffsetUs.Value;
                
                if (actualOffsetUs < minStartTimeUs)
                {
                    isValid = false;
                    double minStartTimeMs = TaskUtility.ConvertToMilliseconds(minStartTimeUs);
                    violations.Add($"任务 '{task.TaskName}' 的Offset({TaskUtility.ConvertToMilliseconds(task.OffsetUs.Value):F3}ms)小于前置任务约束的最小时间({minStartTimeMs:F3}ms)");
                }
            }
            
            return (isValid, violations);
        }
        
        /// <summary>
        /// 评估任务调度方差
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="lcm">预计算的LCM值，避免重复计算</param>
        /// <returns>方差值</returns>
        private double EvaluateVariance(List<TaskItem> tasks, int lcm)
        {
            if (tasks.Count == 0)
                return 0;
                
            // 预先计算所有任务的权重以提高性能
            var taskWeights = CalculateTaskWeights(tasks);
            
            // 创建超周期时间线
            Dictionary<int, double> timeline = new Dictionary<int, double>();

            // 模拟任务执行，统一使用相对权重以保持一致性
            foreach (var task in tasks)
            {
                if (!task.OffsetUs.HasValue || !task.PeriodUs.HasValue || task.PeriodUs.Value <= 0)
                    continue;
                
                int periodUs = task.PeriodUs.Value;
                int offsetUs = task.OffsetUs.Value;
                int executionTimeUs = task.ExecutionTimeUs ?? TaskUtility.MIN_EXECUTION_TIME_US;
                
                // 获取任务权重
                double weight = taskWeights[task.TaskIndex];
                
                // 性能优化：预先计算结束时间点，减少循环内计算
                int taskEndTimeInCycle = offsetUs + executionTimeUs;
                
                for (int time = offsetUs; time < lcm; time += periodUs)
                {
                    // 任务执行的时间范围
                    int endTimePoint = time + executionTimeUs;
                    
                    // 以用户设定的 US_STEP 精度步长标记执行中的时间点
                    for (int t = time; t < endTimePoint && t < lcm; t += TaskUtility.US_STEP)
                    {
                        if (!timeline.ContainsKey(t))
                            timeline[t] = 0;
                        timeline[t] += weight; // 使用一致的权重计算
                    }
                }
            }
            
            // 性能优化：避免在没有数据时进行计算
            if (timeline.Count == 0)
                return 0;
                
            // 计算加权负载分布方差
            double avgLoad = timeline.Values.Average();
            double variance = timeline.Values.Select(v => Math.Pow(v - avgLoad, 2)).Average();
            
            return variance;
        }
        
        /// <summary>
        /// 克隆任务列表（已重构为使用TaskUtility公共方法）
        /// </summary>
        private List<TaskItem> CloneTasks(List<TaskItem> tasks)
        {
            return TaskUtility.CloneTasks(tasks);
        }

        /// <summary>
        /// 在拓扑排序基础上进行二次排序，考虑优先级和周期
        /// </summary>
        /// <param name="topologicallySortedTasks">已拓扑排序的任务列表</param>
        /// <returns>最终排序的任务列表</returns>
        private List<TaskItem> SortTasksWithPrerequisites(List<TaskItem> topologicallySortedTasks)
        {
            // 构建依赖层级
            var dependencyLevels = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
            var taskMap = new Dictionary<string, TaskItem>(StringComparer.OrdinalIgnoreCase);
            
            // 初始化
            foreach (var task in topologicallySortedTasks)
            {
                if (!string.IsNullOrWhiteSpace(task.TaskName))
                {
                    var taskName = task.TaskName.Trim();
                    taskMap[taskName] = task;
                    dependencyLevels[taskName] = 0;
                }
            }
            
            // 计算每个任务的依赖层级
            foreach (var task in topologicallySortedTasks)
            {
                if (string.IsNullOrWhiteSpace(task.TaskName))
                    continue;
                    
                var taskName = task.TaskName.Trim();
                var prerequisites = TaskValidator.ParsePrerequisiteTaskNames(task.PrerequisiteTasks);
                
                if (prerequisites.Count > 0)
                {
                    int maxPrerequisiteLevel = 0;
                    foreach (var prerequisite in prerequisites)
                    {
                        if (dependencyLevels.ContainsKey(prerequisite))
                        {
                            maxPrerequisiteLevel = Math.Max(maxPrerequisiteLevel, dependencyLevels[prerequisite]);
                        }
                    }
                    dependencyLevels[taskName] = maxPrerequisiteLevel + 1;
                }
            }
            
            // 按依赖层级分组，然后在每个层级内按优先级、周期和执行时间排序
            var result = topologicallySortedTasks
                .Where(t => !string.IsNullOrWhiteSpace(t.TaskName))
                .GroupBy(t => dependencyLevels.ContainsKey(t.TaskName.Trim()) ? dependencyLevels[t.TaskName.Trim()] : 0)
                .OrderBy(g => g.Key) // 按依赖层级排序
                .SelectMany(g => g.OrderByDescending(t => t.Priority ?? 0) // 在同一层级内按优先级排序
                             .ThenBy(t => t.PeriodUs ?? int.MaxValue) // 优先级相同时按周期排序
                             .ThenByDescending(t => t.ExecutionTimeUs ?? 0)) // 周期相同时按执行时间降序排序（执行时间长的在前）
                .ToList();
            
            // 添加没有任务名的任务到末尾
            result.AddRange(topologicallySortedTasks.Where(t => string.IsNullOrWhiteSpace(t.TaskName)));
            
            // 任务排序完成（考虑前置任务关系）
            
            return result;
        }

        /// <summary>
        /// 计算任务权重列表，用于优化计算
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="useCache">是否使用缓存</param>
        /// <returns>任务权重字典</returns>
        private Dictionary<int, double> CalculateTaskWeights(List<TaskItem> tasks, bool useCache = true)
        {
            // 创建新的权重字典
            Dictionary<int, double> taskWeights = new Dictionary<int, double>();
            
            // 如果启用缓存且缓存不为空，尝试从缓存查找
            if (useCache && _taskWeightsCache.Count > 0)
            {
                bool allTasksInCache = true;
                
                // 检查所有任务是否都在缓存中 - 优化为O(1)查找
                foreach (var task in tasks)
                {
                    if (_taskWeightsCache.ContainsKey(task.TaskIndex))
                    {
                        taskWeights[task.TaskIndex] = _taskWeightsCache[task.TaskIndex];
                    }
                    else
                    {
                        allTasksInCache = false;
                        break;
                    }
                }
                
                // 如果所有任务都在缓存中找到了权重，直接返回
                if (allTasksInCache)
                {
                    return taskWeights;
                }
            }
            
            // 如果不使用缓存或缓存中没有全部找到，重新计算权重
            
            // 清空权重字典重新填充
            taskWeights.Clear();
            
            // 根据优先级排序任务
            var prioritizedTasks = tasks.OrderByDescending(t => t.Priority ?? 0).ToList();
            
            // 计算相对权重: 与具体优先级值无关，只与排序有关
            int taskCount = prioritizedTasks.Count;
            for (int i = 0; i < taskCount; i++)
            {
                // 计算相对权重
                // 1.0是基础权重，优先级越高额外权重越大，排序越靠前权重越大
                double relativeWeight = 1.0 + ((double)(taskCount - i - 1) / Math.Max(1, taskCount));
                
                // 权重范围在1.0到2.0之间
                taskWeights[prioritizedTasks[i].TaskIndex] = relativeWeight;
                
                // 存入缓存
                if (useCache)
                {
                    _taskWeightsCache[prioritizedTasks[i].TaskIndex] = relativeWeight;
                }
            }
            
            return taskWeights;
        }
        
        /// <summary>
        /// 获取单个任务的权重
        /// </summary>
        /// <param name="task">任务项</param>
        /// <returns>权重值</returns>
        //private double GetTaskWeight(TaskItem task)
        //{
        //    // 检查缓存
        //    if (_taskWeightsCache.ContainsKey(task))
        //    {
        //        return _taskWeightsCache[task];
        //    }
            
        //    // 如果未缓存，使用CalculateTaskWeights方法计算
        //    // 创建只包含当前任务的列表
        //    var singleTaskList = new List<TaskItem> { task };
            
        //    // 不使用缓存，直接计算权重
        //    var weights = CalculateTaskWeights(singleTaskList, false);
            
        //    // 确保任务存在于结果中
        //    if (weights.ContainsKey(task))
        //    {
        //        // 存入缓存
        //        _taskWeightsCache[task] = weights[task];
        //        return weights[task];
        //    }
            
        //    // 如果计算失败，返回默认权重1.0
        //    return 1.0;
        //}
    }
} 

