using System;
using System.Collections.Generic;
using System.Linq;

/*
 * 🔍 问题分析报告：不可抢占任务大offset和随机性问题
 * 
 * 潜在问题根源：
 * 1. 权重计算过度惩罚：CalculateConflictWeight中，不可抢占任务的权重乘数过大
 *    - 不可抢占阻塞高优先级：10倍权重
 *    - 被不可抢占阻塞：5倍权重
 *    - 优先级差异：最多可达(优先级差+1)*2倍
 *    - 组合起来可能导致数百倍的权重放大
 * 
 * 2. 搜索范围过大：FindLowConflictPosition在整个周期内搜索，
 *    但不可抢占任务实际上在周期前部分更合适
 * 
 * 3. 初始解随机性：每次都完全随机生成初始解，
 *    导致算法从不同起点开始，结果差异大
 * 
 * 4. 温度自适应策略：微调概率公式可能不合理，
 *    导致在低温时仍进行大跳跃
 * 
 * 调试信息已添加到以下关键方法：
 * - GenerateTargetedPerturbation: 扰动策略选择
 * - FindLowConflictPosition: 冲突位置搜索
 * - CalculatePreciseConflictScore: 冲突分数计算
 * - CalculateConflictWeight: 权重计算
 * - GenerateInitialSolution: 初始解生成
 * 
 * 🔧 已应用的关键修复：
 * 1. 温度比例限制：防止微调概率超过100%
 * 2. 自适应温度上限：限制最大温度为2000，避免算法失控
 * 3. 不可抢占权重优化：降低权重乘数(10→3, 5→2)，避免过度惩罚
 * 4. 搜索范围限制：不可抢占任务优先在周期前50%搜索，避免大offset
 */

namespace CalculateOffset
{
    /// <summary>
    /// 模拟退火优化器 - 用于寻找最优的任务Offset组合
    /// </summary>
    public class SimulatedAnnealingOptimizer
    {
        #region 常量定义

        /// <summary>
        /// 默认初始温度
        /// </summary>
        private const double DEFAULT_INITIAL_TEMPERATURE = 1000.0;

        /// <summary>
        /// 默认最终温度
        /// </summary>
        private const double DEFAULT_FINAL_TEMPERATURE = 0.1;

        /// <summary>
        /// 默认冷却率（每次迭代温度的衰减系数）
        /// </summary>
        private const double DEFAULT_COOLING_RATE = 0.995;

        /// <summary>
        /// 默认最大迭代次数
        /// </summary>
        private const int DEFAULT_MAX_ITERATIONS = 10000;

        /// <summary>
        /// 每个温度级别的内循环次数
        /// </summary>
        private const int INNER_LOOP_COUNT = 50;

        /// <summary>
        /// 扰动强度（相对于最大Offset的比例）
        /// </summary>
        private const double PERTURBATION_STRENGTH = 0.1;

        #endregion

        #region 私有字段

        /// <summary>
        /// 可调度性分析器
        /// </summary>
        private readonly SchedulabilityAnalyzer _analyzer;

        /// <summary>
        /// 随机数生成器
        /// </summary>
        private readonly Random _random;

        /// <summary>
        /// 时基值(微秒)
        /// </summary>
        private readonly int _timeBaseUs;

        /// <summary>
        /// 最大偏移量(微秒)
        /// </summary>
        private readonly int _maxOffsetUs;

        /// <summary>
        /// 优化参数
        /// </summary>
        private readonly OptimizationParameters _parameters;

        /// <summary>
        /// 动态扰动信息（用于记录扰动策略统计）
        /// </summary>
        private DynamicPerturbationInfo _dynamicPerturbationInfo;

        /// <summary>
        /// 策略自适应统计器（多臂老虎机）
        /// </summary>
        private StrategyAdaptationTracker _strategyTracker;

        /// <summary>
        /// 动态内循环调节器
        /// </summary>
        private DynamicInnerLoopController _innerLoopController;

        /// <summary>
        /// 自适应冷却控制器
        /// </summary>
        private AdaptiveCoolingController _coolingController;
        
        /// <summary>
        /// 最佳解缓存管理器 - 实现三级挑选机制的一级挑选
        /// </summary>
        private ThreeTierSelectionCache _bestSolutionCache;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="timeBaseUs">时基值(微秒)</param>
        /// <param name="maxOffsetUs">最大偏移量(微秒)</param>
        /// <param name="parameters">优化参数，为空时使用默认值</param>
        public SimulatedAnnealingOptimizer(int timeBaseUs, int maxOffsetUs, 
            OptimizationParameters parameters = null)
        {
            _analyzer = new SchedulabilityAnalyzer(timeBaseUs);
            _parameters = parameters ?? new OptimizationParameters();
            _random = _parameters.RandomSeed.HasValue ? new Random(_parameters.RandomSeed.Value) : new Random();
            _timeBaseUs = timeBaseUs;
            _maxOffsetUs = maxOffsetUs;
            
            // 初始化智能控制器
            _strategyTracker = new StrategyAdaptationTracker();
            _innerLoopController = new DynamicInnerLoopController();
            _coolingController = new AdaptiveCoolingController();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 执行模拟退火优化（智能版本：支持重启机制和策略自适应）
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="initialSolution">外部提供的初始解（可选）</param>
        /// <returns>优化结果</returns>
        public SimulatedAnnealingResult Optimize(List<TaskItem> tasks, Dictionary<int, int> initialSolution = null)
        {
            try
            {
                // 📊 输出优化开始前的缓存统计
                var initialCacheStats = SchedulabilityAnalyzer.GetCacheStatistics();
                Console.WriteLine($"🚀 [SA优化开始] 评估缓存状态: {initialCacheStats.CacheSize}个解已缓存, 命中率: {initialCacheStats.HitRate:P2}");

                // 🔍 调试信息：输出关键参数设置
                //Console.WriteLine($"[调试] 模拟退火参数设置:");
                //Console.WriteLine($"  初始温度: {_parameters.InitialTemperature}");
                //Console.WriteLine($"  最终温度: {_parameters.FinalTemperature}");
                //Console.WriteLine($"  冷却率: {_parameters.CoolingRate}");
                //Console.WriteLine($"  最大迭代数: {_parameters.MaxIterations}");
                //Console.WriteLine($"  最大重启次数: {_parameters.MaxRestarts}");
                //Console.WriteLine($"  高响应比权重: {_parameters.HighResponseRatioWeight}");
                //Console.WriteLine($"  扰动强度: {PERTURBATION_STRENGTH}");
                //Console.WriteLine($"  启发式播种: {_parameters.UseHeuristicSeeding}");
                //Console.WriteLine($"  使用高级扰动: {_parameters.UseAdvancedPerturbation}");
                //Console.WriteLine($"  使用目标扰动: {_parameters.UseTargetedPerturbation}");
                //Console.WriteLine($"");

                // 验证输入
                var validTasks = ValidateAndPrepareTasks(tasks);
                if (validTasks.Count == 0)
                {
                    return new SimulatedAnnealingResult
                    {
                        Success = false,
                        ErrorMessage = "没有有效的任务可优化"
                    };
                }

                // 🔍 调试信息：输出任务概览
                //Console.WriteLine($"[调试] 任务概览 (共{validTasks.Count}个有效任务):");
                foreach (var task in validTasks)
                {
                    double periodMs = TaskUtility.ConvertToMilliseconds(task.PeriodUs ?? 0);
                    double executionTimeMs = TaskUtility.ConvertToMilliseconds(task.ExecutionTimeUs ?? 0);
                    //Console.WriteLine($"  {task.TaskName}: 周期={periodMs:F1}ms, 执行时间={executionTimeMs:F1}ms, 优先级={task.Priority}, 不可抢占={task.NonPreemptive}");
                }
                //Console.WriteLine($"");

                // 记录开始时间
                var startTime = DateTime.Now;

                // 全局最佳解跟踪
                Dictionary<int, int> globalBestSolution = null;
                double globalBestCost = double.MaxValue;
                var restartInfo = new RestartMechanismInfo();
                var strategyAdaptationInfo = new StrategyAdaptationInfo();

                // 重启循环
                for (int restartRound = 0; restartRound < _parameters.MaxRestarts; restartRound++)
                {
                     // 执行单轮退火（第一轮使用外部提供的初始解）
                     var initialSolutionForRound = (restartRound == 0) ? initialSolution : null;
                     var singleRunResult = ExecuteSingleAnnealingRun(validTasks, restartRound, restartInfo, strategyAdaptationInfo, initialSolutionForRound);
                     
                     // 累积重启统计信息
                     restartInfo.InitialCosts.Add(singleRunResult.BestCost); // 暂时用最佳成本代替初始成本
                     restartInfo.TotalIterations += singleRunResult.TotalIterations;
                     restartInfo.TotalAcceptedMoves += singleRunResult.AcceptedMoves;
                     restartInfo.TotalImprovingMoves += singleRunResult.ImprovingMoves;
                     restartInfo.MaxRestarts = _parameters.MaxRestarts;
                     
                     // 更新全局最佳解
                     if (singleRunResult.BestCost < globalBestCost)
                     {
                         globalBestCost = singleRunResult.BestCost;
                         globalBestSolution = CloneSolution(singleRunResult.BestSolution);
                         restartInfo.BestRestartRound = restartRound;
                         restartInfo.ImprovementsByRestart[restartRound] = globalBestCost;
                     }

                    // 检查是否需要继续重启
                    if (!ShouldContinueRestart(singleRunResult, restartRound, globalBestCost))
                    {
                        restartInfo.ActualRestarts = restartRound + 1;
                        break;
                    }

                    // 准备下一轮重启
                    PrepareForNextRestart(restartRound, singleRunResult);
                }

                // 应用全局最佳解
                ApplySolutionToTasks(globalBestSolution, validTasks);
                var finalAnalysis = _analyzer.AnalyzeSchedulability(validTasks);

                // 汇总统计信息
                var finalStatistics = BuildFinalStatistics(restartInfo, strategyAdaptationInfo, 
                    startTime, globalBestCost, validTasks);

                // 📊 输出优化结束后的缓存统计
                var finalCacheStats = SchedulabilityAnalyzer.GetCacheStatistics();
                var cacheHitIncrease = finalCacheStats.HitCount - initialCacheStats.HitCount;
                var cacheMissIncrease = finalCacheStats.MissCount - initialCacheStats.MissCount;
                Console.WriteLine($"🎯 [SA优化完成] 缓存统计变化: 新增命中 {cacheHitIncrease} 次, 新增未命中 {cacheMissIncrease} 次");
                Console.WriteLine($"📈 [SA优化完成] 最终缓存: {finalCacheStats.CacheSize}个解, 总命中率: {finalCacheStats.HitRate:P2}");
                if (cacheHitIncrease > 0)
                {
                    Console.WriteLine($"⚡ [性能提升] SA优化过程中缓存节省了 {cacheHitIncrease} 次WCRT分析！");
                }

                return new SimulatedAnnealingResult
                {
                    Success = true,
                    OptimizedTasks = validTasks,
                    BestCost = globalBestCost,
                    SchedulabilityResult = finalAnalysis,
                    Statistics = finalStatistics
                };
            }
            catch (Exception ex)
            {
                return new SimulatedAnnealingResult
                {
                    Success = false,
                    ErrorMessage = $"优化过程出错: {ex.Message}"
                };
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 验证并准备任务数据
        /// </summary>
        /// <param name="tasks">原始任务列表</param>
        /// <returns>有效任务列表</returns>
        private List<TaskItem> ValidateAndPrepareTasks(List<TaskItem> tasks)
        {
            return tasks.Where(t => 
                t.Enabled &&
                !string.IsNullOrWhiteSpace(t.TaskName) &&
                t.PeriodUs.HasValue && t.PeriodUs.Value > 0 &&
                t.ExecutionTimeUs.HasValue && t.ExecutionTimeUs.Value > 0
            ).ToList();
        }

        /// <summary>
        /// 生成初始解（为每个任务随机分配Offset）
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <returns>任务索引到Offset(微秒)的字典</returns>
        private Dictionary<int, int> GenerateInitialSolution(List<TaskItem> tasks)
                {
            var solution = new Dictionary<int, int>();
            int timeBaseUs = _timeBaseUs;

            // 🔍 调试信息：输出初始解生成参数
            double timeBaseMs = TaskUtility.ConvertToMilliseconds(timeBaseUs);
            Console.WriteLine($"[调试] GenerateInitialSolution - 时基: {timeBaseMs:F1}ms，将使用各任务的独立offset限制");

            foreach (var task in tasks)
            {
                if (task.ManualInput)
                {
                    // 手动输入的任务保持原始Offset
                    int manualOffsetUs = task.OffsetUs ?? 0;
                    solution[task.TaskIndex] = manualOffsetUs;
                    double manualOffsetMs = TaskUtility.ConvertToMilliseconds(manualOffsetUs);
                    Console.WriteLine($"[调试] 任务{task.TaskName}: 手动输入offset = {manualOffsetMs:F1}ms");
                }
                else
                {
                    // 获取该任务的最大offset限制
                    int taskMaxOffsetUs = task.MaxOffsetUs > 0 ? task.MaxOffsetUs : _maxOffsetUs;
                    
                    // 统一的随机初始化策略
                    int randomOffsetUs;
                    if (_parameters.UseHeuristicSeeding && task.NonPreemptive)
                    {
                        // 不可抢占任务：在周期的前半部分随机选择，避免过度延迟
                        int periodUs = task.PeriodUs.Value;
                        int maxInitialOffsetUs = Math.Min(taskMaxOffsetUs, periodUs / 2); // 限制在周期的50%内
                        // 方案1：直接生成时基对齐的随机offset，避免浮点运算
                        int maxSteps = maxInitialOffsetUs / timeBaseUs;
                        randomOffsetUs = _random.Next(0, maxSteps + 1) * timeBaseUs;
                        double maxInitialOffsetMs = TaskUtility.ConvertToMilliseconds(maxInitialOffsetUs);
                        Console.WriteLine($"[调试] 任务{task.TaskName}: 不可抢占启发式播种, 限制在周期50%内({maxInitialOffsetMs:F1}ms)");
                    }
                    else
                    {
                        // 所有其他任务：全范围随机初始化
                        // 方案1：直接生成时基对齐的随机offset，避免浮点运算
                        int maxSteps = taskMaxOffsetUs / timeBaseUs;
                        randomOffsetUs = _random.Next(0, maxSteps + 1) * timeBaseUs;
                        Console.WriteLine($"[调试] 任务{task.TaskName}: 标准随机播种，任务限制={TaskUtility.ConvertToMilliseconds(taskMaxOffsetUs):F1}ms");
                    }
                    solution[task.TaskIndex] = randomOffsetUs;

                    double randomOffsetMs = TaskUtility.ConvertToMilliseconds(randomOffsetUs);
                    Console.WriteLine($"[调试] 任务{task.TaskName}: 随机生成offset = {randomOffsetMs:F1}ms (不可抢占: {task.NonPreemptive}, 优先级: {task.Priority})");
                }
            }

            return solution;
        }

        /// <summary>
        /// 生成邻居解（扰动当前解）
        /// </summary>
        /// <param name="currentSolution">当前解</param>
        /// <returns>邻居解</returns>
        private Dictionary<int, int> GenerateNeighborSolution(Dictionary<int, int> currentSolution)
        {
            return GenerateNeighborSolutionWithTemperature(currentSolution, null);
        }

        /// <summary>
        /// 根据温度生成邻居解（支持动态扰动策略）
        /// </summary>
        /// <param name="currentSolution">当前解</param>
        /// <param name="currentTemperature">当前温度（可选，用于动态扰动）</param>
        /// <param name="validTasks">有效任务列表（可选，用于跳过ManualInput任务）</param>
        /// <returns>邻居解</returns>
        private Dictionary<int, int> GenerateNeighborSolutionWithTemperature(Dictionary<int, int> currentSolution, double? currentTemperature, List<TaskItem> validTasks = null)
        {
            var neighborSolution = new Dictionary<int, int>(currentSolution);
            int timeBaseUs = _timeBaseUs;
            
            // 创建任务索引到任务对象的映射，用于获取每个任务的MaxOffsetUs
            var taskIndexToTask = validTasks?.ToDictionary(t => t.TaskIndex, t => t) ?? new Dictionary<int, TaskItem>();

            int perturbCount;
            double perturbationStrength;

            if (_parameters.UseDynamicPerturbation && currentTemperature.HasValue && _dynamicPerturbationInfo != null)
            {
                // 动态扰动策略：根据温度调整扰动强度
                var temperatureRatio = CalculateTemperatureRatio(currentTemperature.Value);
                
                // 计算扰动任务数：高温时更多任务，低温时更少任务
                var taskCountRatio = _parameters.LowTempMinPerturbationRatio + 
                    (temperatureRatio * (_parameters.HighTempMaxPerturbationRatio - _parameters.LowTempMinPerturbationRatio));
                perturbCount = Math.Max(1, Math.Min((int)Math.Round(currentSolution.Count * taskCountRatio), currentSolution.Count));
                
                // 计算扰动强度：高温时更大范围，低温时更小范围
                var offsetRatio = _parameters.LowTempMinOffsetRatio + 
                    (temperatureRatio * (_parameters.HighTempMaxOffsetRatio - _parameters.LowTempMinOffsetRatio));
                perturbationStrength = offsetRatio;

                // 记录扰动统计
                RecordPerturbationStatistics(currentTemperature.Value, temperatureRatio, perturbCount, offsetRatio);
            }
            else
            {
                // 固定扰动策略：传统方法
                perturbCount = Math.Min(_random.Next(1, 4), currentSolution.Count);
                perturbationStrength = PERTURBATION_STRENGTH; // 使用固定的10%扰动强度
            }

            // 随机选择要扰动的任务（跳过手动输入任务）
            var taskIndices = currentSolution.Keys.ToList();
            
            // 如果有任务信息，过滤掉手动输入任务
            if (validTasks != null)
            {
                var manualInputTaskIds = validTasks.Where(t => t.ManualInput).Select(t => t.TaskIndex).ToHashSet();
                taskIndices = taskIndices.Where(id => !manualInputTaskIds.Contains(id)).ToList();
            }
            
            // 如果没有可扰动的任务，返回原解
            if (taskIndices.Count == 0)
            {
                return neighborSolution;
            }
            
            // 调整扰动任务数，不能超过可扰动任务数
            perturbCount = Math.Min(perturbCount, taskIndices.Count);
            var selectedTasks = taskIndices.OrderBy(x => _random.Next()).Take(perturbCount);
            
            foreach (int taskIndex in selectedTasks)
            {
                // 获取该任务的最大offset限制
                int taskMaxOffsetUs = taskIndexToTask.ContainsKey(taskIndex) ? 
                    taskIndexToTask[taskIndex].MaxOffsetUs : _maxOffsetUs;
                    
                // 根据扰动强度进行扰动（微秒为单位）
                int perturbationRangeUs = (int)(taskMaxOffsetUs * perturbationStrength);
                // 方案1：直接生成时基对齐的随机扰动，避免浮点运算
                int maxSteps = perturbationRangeUs / timeBaseUs;
                int randomSteps = _random.Next(-maxSteps, maxSteps + 1);
                int perturbationUs = randomSteps * timeBaseUs;
                
                // 应用扰动
                int newOffsetUs = currentSolution[taskIndex] + perturbationUs;
                
                // 限制在有效范围内（使用该任务的最大offset限制）
                newOffsetUs = Math.Max(0, Math.Min(newOffsetUs, taskMaxOffsetUs));
                
                // 对齐到时基
                newOffsetUs = (newOffsetUs / timeBaseUs) * timeBaseUs;
                
                neighborSolution[taskIndex] = newOffsetUs;
            }

            return neighborSolution;
        }

        /// <summary>
        /// 评估解的质量（成本函数）
        /// </summary>
        /// <param name="solution">Offset解</param>
        /// <returns>成本值</returns>
        private double EvaluateSolution(Dictionary<int, int> solution)
        {
            // 创建任务副本并应用解
            var tempTasks = new List<TaskItem>();
            
            // 这里需要从某个地方获取任务模板，我们先用一个简化的方法
            // 实际使用时需要传入完整的任务信息
            foreach (var kvp in solution)
            {
                // 这是一个简化版本，实际应该有完整的任务信息
                // 为了测试，我们需要一个更好的方法来获取任务信息
            }

            // 暂时返回一个基于Offset方差的简单评估
            // 实际应该使用SchedulabilityAnalyzer
            var offsets = solution.Values.ToList();
            if (offsets.Count == 0) return double.MaxValue;

            double mean = offsets.Average();
            double variance = offsets.Select(x => Math.Pow(x - mean, 2)).Average();
            return variance + offsets.Max() * 0.01; // 简单的评估函数
        }

        /// <summary>
        /// 使用完整任务信息评估解的质量
        /// </summary>
        /// <param name="tasks">完整任务列表</param>
        /// <returns>成本值</returns>
        private double EvaluateSolutionWithTasks(List<TaskItem> tasks)
        {
            var result = _analyzer.AnalyzeSchedulability(tasks);
            // 降噪：仅在成本显著变更或出现截止期违背时输出
            if (result.Statistics != null)
            {
                if (result.Statistics.DeadlineMissCount > 0 || result.CostValue == 0 || result.CostValue == double.MaxValue)
                {
                    Console.WriteLine($"[SA] 评估: 成本={result.CostValue:F6}, Miss={result.Statistics.DeadlineMissCount}, MaxR/DDL={result.Statistics.MaxResponseTimeRatio:F6}");
                }
            }
            return result.IsValid ? result.CostValue : double.MaxValue;
        }

        /// <summary>
        /// 克隆解
        /// </summary>
        /// <param name="solution">原始解</param>
        /// <returns>克隆的解</returns>
        private Dictionary<int, int> CloneSolution(Dictionary<int, int> solution)
        {
            return new Dictionary<int, int>(solution);
        }



        /// <summary>
        /// 将解应用到任务列表
        /// </summary>
        /// <param name="solution">Offset解</param>
        /// <param name="tasks">任务列表</param>
        private void ApplySolutionToTasks(Dictionary<int, int> solution, List<TaskItem> tasks)
        {
            foreach (var task in tasks)
            {
                if (solution.ContainsKey(task.TaskIndex))
                {
                    // 跳过手动输入任务，保持其原始offset值
                    if (task.ManualInput)
                    {
                        continue;
                    }
                    task.OffsetUs = solution[task.TaskIndex];
                }
            }
        }

        #endregion

        #region 动态扰动策略

        /// <summary>
        /// 初始化动态扰动信息
        /// </summary>
        /// <param name="initialTemperature">初始温度</param>
        /// <param name="finalTemperature">最终温度</param>
        private void InitializeDynamicPerturbationInfo(double initialTemperature, double finalTemperature)
        {
            if (_parameters.UseDynamicPerturbation)
            {
                _dynamicPerturbationInfo = new DynamicPerturbationInfo
                {
                    WasUsed = true,
                    InitialTemperature = initialTemperature,
                    FinalTemperature = finalTemperature
                };
            }
            else
            {
                _dynamicPerturbationInfo = new DynamicPerturbationInfo
                {
                    WasUsed = false,
                    InitialTemperature = initialTemperature,
                    FinalTemperature = finalTemperature
                };
            }
        }

        /// <summary>
        /// 计算温度比例（相对于初始温度的比例，用于动态扰动）
        /// </summary>
        /// <param name="currentTemperature">当前温度</param>
        /// <returns>温度比例（0.0-1.0）</returns>
        private double CalculateTemperatureRatio(double currentTemperature)
        {
            if (_dynamicPerturbationInfo == null) return 0.0;

            double initialTemp = _dynamicPerturbationInfo.InitialTemperature;
            double finalTemp = _dynamicPerturbationInfo.FinalTemperature;

            if (Math.Abs(initialTemp - finalTemp) < 1e-10) return 1.0;

            // 计算当前温度在初始-最终温度区间的位置
            double ratio = (currentTemperature - finalTemp) / (initialTemp - finalTemp);
            return Math.Max(0.0, Math.Min(1.0, ratio));
        }

        /// <summary>
        /// 记录扰动统计信息
        /// </summary>
        /// <param name="temperature">当前温度</param>
        /// <param name="temperatureRatio">温度比例</param>
        /// <param name="perturbedTaskCount">扰动任务数</param>
        /// <param name="offsetRatio">偏移比例</param>
        private void RecordPerturbationStatistics(double temperature, double temperatureRatio, int perturbedTaskCount, double offsetRatio)
        {
            if (_dynamicPerturbationInfo == null) return;

            // 记录初始和最终统计
            if (_dynamicPerturbationInfo.InitialPerturbationStats == null)
            {
                _dynamicPerturbationInfo.InitialPerturbationStats = new PerturbationStatistics
                {
                    Temperature = temperature,
                    PerturbedTaskCount = perturbedTaskCount,
                    PerturbedTaskRatio = temperatureRatio,
                    OffsetRatio = offsetRatio
                };
            }

            _dynamicPerturbationInfo.FinalPerturbationStats = new PerturbationStatistics
            {
                Temperature = temperature,
                PerturbedTaskCount = perturbedTaskCount,
                PerturbedTaskRatio = temperatureRatio,
                OffsetRatio = offsetRatio
            };

            // 累计统计
            if (_dynamicPerturbationInfo.PerturbationSnapshots.Count == 0)
            {
                _dynamicPerturbationInfo.AveragePerturbedTaskCount = perturbedTaskCount;
                _dynamicPerturbationInfo.AverageOffsetRatio = offsetRatio;
            }
            else
            {
                int count = _dynamicPerturbationInfo.PerturbationSnapshots.Count + 1;
                _dynamicPerturbationInfo.AveragePerturbedTaskCount = 
                    (_dynamicPerturbationInfo.AveragePerturbedTaskCount * (count - 1) + perturbedTaskCount) / count;
                _dynamicPerturbationInfo.AverageOffsetRatio = 
                    (_dynamicPerturbationInfo.AverageOffsetRatio * (count - 1) + offsetRatio) / count;
            }
        }

        /// <summary>
        /// 记录扰动快照（定期采样）
        /// </summary>
        /// <param name="iteration">当前迭代次数</param>
        /// <param name="temperature">当前温度</param>
        /// <param name="perturbedTaskCount">扰动任务数</param>
        /// <param name="offsetRatio">偏移比例</param>
        private void RecordPerturbationSnapshot(int iteration, double temperature, int perturbedTaskCount, double offsetRatio)
        {
            if (_dynamicPerturbationInfo == null || !_parameters.UseDynamicPerturbation) return;

            // 每100次迭代记录一次快照
            if (iteration % 100 == 0 || iteration == 1)
            {
                var snapshot = new PerturbationSnapshot
                {
                    Iteration = iteration,
                    Temperature = temperature,
                    TemperatureRatio = CalculateTemperatureRatio(temperature),
                    PerturbedTaskCount = perturbedTaskCount,
                    OffsetRatio = offsetRatio
                };

                _dynamicPerturbationInfo.PerturbationSnapshots.Add(snapshot);
            }
        }

        #endregion

        #region 自适应温度计算

        /// <summary>
        /// 计算自适应初始温度
        /// </summary>
        /// <param name="validTasks">有效任务列表</param>
        /// <param name="initialSolution">初始解</param>
        /// <returns>自适应温度信息</returns>
        private AdaptiveTemperatureInfo CalculateAdaptiveInitialTemperature(List<TaskItem> validTasks, 
            Dictionary<int, int> initialSolution)
        {
            var adaptiveInfo = new AdaptiveTemperatureInfo
            {
                WasUsed = true,
                OriginalInitialTemperature = _parameters.InitialTemperature,
                TargetAcceptanceRate = _parameters.TargetInitialAcceptanceRate,
                RandomWalkSteps = _parameters.RandomWalkSteps
            };

            var startTime = DateTime.Now;

            // 执行随机游走来收集成本增加的样本
            var costIncreases = new List<double>();
            var currentSolution = CloneSolution(initialSolution);
            var currentCost = EvaluateSolutionWithTemplate(currentSolution, validTasks);

            for (int i = 0; i < _parameters.RandomWalkSteps; i++)
            {
                // 生成邻居解
                var neighborSolution = GenerateNeighborSolution(currentSolution);
                var neighborCost = EvaluateSolutionWithTemplate(neighborSolution, validTasks);

                // 计算成本差异
                double costDelta = neighborCost - currentCost;

                // 只关注成本增加的移动
                if (costDelta > 0)
                {
                    costIncreases.Add(costDelta);
                }

                // 无条件接受移动（随机游走，不考虑温度）
                currentSolution = neighborSolution;
                currentCost = neighborCost;
            }

            adaptiveInfo.CalculationTime = DateTime.Now - startTime;
            adaptiveInfo.ValidCostIncreaseCount = costIncreases.Count;

            // 计算自适应初始温度
            if (costIncreases.Count > 0)
            {
                // 计算平均成本增量
                adaptiveInfo.AverageCostIncrease = costIncreases.Average();

                // 使用Metropolis准则反解温度: T = -ΔC / ln(P)
                double targetLogProb = Math.Log(_parameters.TargetInitialAcceptanceRate);
                
                // 防止除零错误
                if (Math.Abs(targetLogProb) < 1e-10)
                {
                    adaptiveInfo.AdaptiveInitialTemperature = _parameters.MaxInitialTemperature;
                }
                else
                {
                    double calculatedTemperature = -adaptiveInfo.AverageCostIncrease / targetLogProb;
                    
                    // 🔧 修复：应用更严格的温度限制，防止温度过高
                    double maxReasonableTemperature = Math.Min(_parameters.MaxInitialTemperature, 2000.0);
                    adaptiveInfo.AdaptiveInitialTemperature = Math.Max(
                        _parameters.MinInitialTemperature,
                        Math.Min(maxReasonableTemperature, calculatedTemperature)
                    );
                    
                    // 如果计算的温度超过合理范围，记录警告
                    if (calculatedTemperature > maxReasonableTemperature)
                    {
                        //Console.WriteLine($"[警告] 自适应温度计算结果({calculatedTemperature:F1})过高，已限制为{maxReasonableTemperature:F1}");
                    }
                }
            }
            else
            {
                // 如果没有发现成本增加的移动，使用原始温度
                adaptiveInfo.AdaptiveInitialTemperature = _parameters.InitialTemperature;
                adaptiveInfo.AverageCostIncrease = 0.0;
            }

            return adaptiveInfo;
        }

        #endregion

        #region 重新设计的评估方法

        /// <summary>
        /// 使用给定的任务模板评估解
        /// </summary>
        /// <param name="solution">Offset解</param>
        /// <param name="taskTemplate">任务模板</param>
        /// <returns>成本值</returns>
        public double EvaluateSolutionWithTemplate(Dictionary<int, int> solution, List<TaskItem> taskTemplate)
        {
            // 克隆任务模板
            var tempTasks = CloneTaskTemplate(taskTemplate);
            
            // 应用解到任务
            ApplySolutionToTasks(solution, tempTasks);
            
            // 使用SchedulabilityAnalyzer评估
            return EvaluateSolutionWithTasks(tempTasks);
        }

        /// <summary>
        /// 克隆任务模板
        /// </summary>
        /// <param name="taskTemplate">任务模板</param>
        /// <returns>克隆的任务列表</returns>
        private List<TaskItem> CloneTaskTemplate(List<TaskItem> taskTemplate)
        {
            return TaskUtility.CloneTasks(taskTemplate);
        }

        #endregion

        #region 早停准则实现

        /// <summary>
        /// 初始化早停信息
        /// </summary>
        /// <returns>早停信息对象</returns>
        private EarlyStoppingInfo InitializeEarlyStoppingInfo()
        {
            return new EarlyStoppingInfo
            {
                WasUsed = _parameters.UseEarlyStopping,
                WasTriggered = false,
                LastImprovementIteration = 0,
                NoImprovementIterations = 0
            };
        }

        /// <summary>
        /// 检查早停条件
        /// </summary>
        /// <param name="currentIteration">当前迭代次数</param>
        /// <param name="lastImprovementIteration">最后改进迭代次数</param>
        /// <param name="costHistory">成本历史</param>
        /// <param name="bestCost">当前最佳成本</param>
        /// <param name="earlyStoppingInfo">早停信息</param>
        /// <returns>是否应该早停</returns>
        private bool CheckEarlyStoppingConditions(int currentIteration, int lastImprovementIteration, 
            List<double> costHistory, double bestCost, EarlyStoppingInfo earlyStoppingInfo)
        {
            // 更新无改进迭代数
            int noImprovementIterations = currentIteration - lastImprovementIteration;
            earlyStoppingInfo.NoImprovementIterations = noImprovementIterations;
            earlyStoppingInfo.LastImprovementIteration = lastImprovementIteration;

            // 条件1：无改进迭代数超过阈值
            if (noImprovementIterations >= _parameters.NoImprovementThreshold)
            {
                earlyStoppingInfo.WasTriggered = true;
                earlyStoppingInfo.StoppingReason = $"无改进迭代数超过阈值({_parameters.NoImprovementThreshold})";
                earlyStoppingInfo.SavedIterations = _parameters.MaxIterations - currentIteration;
                earlyStoppingInfo.SavedTimePercentage = (double)earlyStoppingInfo.SavedIterations / _parameters.MaxIterations;
                return true;
            }

            // 条件2：收敛检测（成本变化很小）
            if (costHistory.Count >= _parameters.ConvergenceWindowSize)
            {
                var recentCosts = costHistory.Skip(costHistory.Count - _parameters.ConvergenceWindowSize).ToList();
                var variance = CalculateVariance(recentCosts);
                earlyStoppingInfo.ConvergenceWindowVariance = variance;

                if (variance < _parameters.ConvergenceThreshold)
                {
                    earlyStoppingInfo.WasTriggered = true;
                    earlyStoppingInfo.StoppingReason = $"成本收敛，方差({variance:F6}) < 阈值({_parameters.ConvergenceThreshold})";
                    earlyStoppingInfo.SavedIterations = _parameters.MaxIterations - currentIteration;
                    earlyStoppingInfo.SavedTimePercentage = (double)earlyStoppingInfo.SavedIterations / _parameters.MaxIterations;
                    return true;
                }
            }

            // 条件3：已达到足够好的解（成本非常低）
            if (bestCost < _parameters.ImprovementThreshold)
            {
                earlyStoppingInfo.WasTriggered = true;
                earlyStoppingInfo.StoppingReason = $"达到优秀解，成本({bestCost:F6}) < 阈值({_parameters.ImprovementThreshold})";
                earlyStoppingInfo.SavedIterations = _parameters.MaxIterations - currentIteration;
                earlyStoppingInfo.SavedTimePercentage = (double)earlyStoppingInfo.SavedIterations / _parameters.MaxIterations;
                return true;
            }

            return false;
        }

        /// <summary>
        /// 计算方差
        /// </summary>
        /// <param name="values">数值列表</param>
        /// <returns>方差</returns>
        private double CalculateVariance(List<double> values)
        {
            if (values.Count <= 1) return 0.0;
            
            double mean = values.Average();
            double sumSquaredDiff = values.Sum(x => Math.Pow(x - mean, 2));
            return sumSquaredDiff / values.Count;
        }

        #endregion

        #region 精细化扰动策略实现

        /// <summary>
        /// 初始化精细化扰动信息
        /// </summary>
        /// <returns>精细化扰动信息对象</returns>
        private AdvancedPerturbationInfo InitializeAdvancedPerturbationInfo()
        {
            return new AdvancedPerturbationInfo
            {
                WasUsed = _parameters.UseAdvancedPerturbation
            };
        }

        /// <summary>
        /// 生成精细化邻居解
        /// </summary>
        /// <param name="currentSolution">当前解</param>
        /// <param name="validTasks">有效任务列表</param>
        /// <param name="temperature">当前温度</param>
        /// <returns>邻居解和扰动类型</returns>
        private (Dictionary<int, int> Solution, string PerturbationType) GenerateAdvancedNeighborSolution(
            Dictionary<int, int> currentSolution, List<TaskItem> validTasks, double temperature)
        {
            // 决定使用哪种扰动策略
            double randomValue = _random.NextDouble();
            
            if (_parameters.UseTargetedPerturbation && randomValue < 0.5)
            {
                // 针对性扰动：优先扰动高响应比任务
                return (GenerateTargetedPerturbation(currentSolution, validTasks, temperature), "Targeted");
            }
            else if (_parameters.UseCorrelationPerturbation && randomValue < 0.8)
            {
                // 相关性扰动：考虑任务间依赖关系
                return (GenerateCorrelationPerturbation(currentSolution, validTasks, temperature), "Correlation");
            }
            else
            {
                // 随机扰动（回退策略）
                return (GenerateNeighborSolutionWithTemperature(currentSolution, temperature, validTasks), "Random");
            }
        }

        /// <summary>
        /// 生成针对性扰动（优先扰动高响应比任务）
        /// </summary>
        /// <param name="currentSolution">当前解</param>
        /// <param name="validTasks">有效任务列表</param>
        /// <param name="temperature">当前温度</param>
        /// <returns>扰动后的解</returns>
        private Dictionary<int, int> GenerateTargetedPerturbation(
            Dictionary<int, int> currentSolution, List<TaskItem> validTasks, double temperature)
        {
            // 应用当前解到任务，计算响应比和阻塞分析
            var tasksCopy = CloneTaskTemplate(validTasks);
            ApplySolutionToTasks(currentSolution, tasksCopy);
            var analysis = _analyzer.AnalyzeSchedulability(tasksCopy);

            // 🔍 调试信息：输出当前解和阻塞情况
            //Console.WriteLine($"[调试] GenerateTargetedPerturbation - 当前温度: {temperature:F2}");
            foreach (var task in tasksCopy)
            {
                var taskCurrentOffset = currentSolution[task.TaskIndex];
                var taskResult = analysis.TaskResults.FirstOrDefault(r => r.TaskIndex == task.TaskIndex);
                var blockingTime = taskResult?.BlockingTimeUs ?? 0;
                //Console.WriteLine($"[调试] 任务{task.TaskName}: offset={taskCurrentOffset:F1}ms, 阻塞时间={blockingTime/1000.0:F1}ms, 不可抢占={task.NonPreemptive}, 优先级={task.Priority}");
            }

            // 策略1：找出高响应比任务（现有逻辑保持）
            var responseRatios = analysis.TaskResults.Select(r => r.ResponseTimeRatio).ToList();
            double avgResponseRatio = responseRatios.Average();

            var highResponseTasks = new List<TaskItem>();
            for (int i = 0; i < analysis.TaskResults.Count; i++)
            {
                if (responseRatios[i] > avgResponseRatio)
                {
                    // 根据TaskIndex找到对应的任务
                    var taskResult = analysis.TaskResults[i];
                    var correspondingTask = tasksCopy.FirstOrDefault(t => t.TaskIndex == taskResult.TaskIndex);
                    if (correspondingTask != null)
                    {
                        highResponseTasks.Add(correspondingTask);
                    }
                }
            }

            // 策略2：新增阻塞分析 - 找出被严重阻塞的高优先级任务
            var blockedTasks = new List<TaskItem>();
            var blockingTasks = new List<TaskItem>(); // 造成阻塞的低优先级不可抢占任务

            foreach (var taskResult in analysis.TaskResults)
            {
                // 如果任务有显著的阻塞时间，找出阻塞源
                if (taskResult.BlockingTimeUs > 0)
                {
                    var blockedTask = tasksCopy.FirstOrDefault(t => t.TaskIndex == taskResult.TaskIndex);
                    if (blockedTask != null)
                    {
                        blockedTasks.Add(blockedTask);
                        
                        // 找出造成阻塞的不可抢占任务（优先级低于被阻塞任务）
                        var potentialBlockingTasks = tasksCopy.Where(t => 
                            t.NonPreemptive && 
                            !t.ManualInput &&
                            (t.Priority ?? 0) < (blockedTask.Priority ?? 0)).ToList();
                        
                        blockingTasks.AddRange(potentialBlockingTasks);
                        
                        // 🔍 调试信息：输出阻塞分析结果
                        //Console.WriteLine($"[调试] 任务{blockedTask.TaskName}被阻塞{taskResult.BlockingTimeUs/1000.0:F1}ms, 可能的阻塞源: {string.Join(",", potentialBlockingTasks.Select(t => t.TaskName))}");
                    }
                }
            }

            // 决策：优先扰动造成阻塞的任务，其次扰动高响应比任务
            List<TaskItem> targetTasks = new List<TaskItem>();
            bool isBlockingStrategy = false;
            
            if (blockingTasks.Any())
            {
                // 优先策略：扰动造成阻塞的不可抢占任务
                targetTasks = blockingTasks.Distinct().Where(t => !t.ManualInput).ToList();
                isBlockingStrategy = true;
                //Console.WriteLine($"[调试] 阻塞感知扰动 - 发现{targetTasks.Count}个造成阻塞的不可抢占任务: {string.Join(",", targetTasks.Select(t => t.TaskName))}");
            }
            else if (highResponseTasks.Any())
            {
                // 备用策略：扰动高响应比任务
                targetTasks = highResponseTasks.Where(t => !t.ManualInput).ToList();
                //Console.WriteLine($"[调试] 响应比扰动 - 发现{targetTasks.Count}个高响应比任务: {string.Join(",", targetTasks.Select(t => t.TaskName))}");
            }

            // 如果没有目标任务，回退到随机扰动
            if (!targetTasks.Any())
            {
                //Console.WriteLine($"[调试] 无目标任务，回退到随机扰动");
                return GenerateNeighborSolutionWithTemperature(currentSolution, temperature, validTasks);
            }

            var neighborSolution = new Dictionary<int, int>(currentSolution);
            int timeBaseUs = _timeBaseUs;

            // 从目标任务中选择进行智能扰动
            var targetTask = targetTasks[_random.Next(targetTasks.Count)];
            int currentOffsetUs = neighborSolution[targetTask.TaskIndex];
            
            // 获取该任务的最大offset限制
            int taskMaxOffsetUs = targetTask.MaxOffsetUs;
            
            // 🔍 调试信息：输出选择的目标任务
            double currentOffsetMs = TaskUtility.ConvertToMilliseconds(currentOffsetUs);
            //Console.WriteLine($"[调试] 选择目标任务: {targetTask.TaskName}, 当前offset: {currentOffsetMs:F1}ms, 不可抢占: {targetTask.NonPreemptive}");
            
            // 核心改进：温度自适应的多策略扰动
            if (isBlockingStrategy && targetTask.NonPreemptive)
            {
                // 针对阻塞任务的专门修复策略
                //Console.WriteLine($"[调试] 阻塞修复 - 对造成阻塞的任务{targetTask.TaskName}进行智能修复");
                
                // 计算温度比例
                double temperatureRatio = temperature / _parameters.InitialTemperature;
                double strategyRoll = _random.NextDouble();
                
                // 🔍 调试信息：输出策略选择参数
                // 🔧 修复：限制温度比例在合理范围内，防止微调概率超过100%
                temperatureRatio = Math.Min(1.0, Math.Max(0.0, temperatureRatio));
                //Console.WriteLine($"[调试] 温度比例: {temperatureRatio:F3}(修复后), 策略随机数: {strategyRoll:F3}");
                
                // 增强改进：温度自适应策略选择
                // 高温时更多大跳跃(探索)，低温时更多微调(利用)
                double microAdjustmentProbability = 0.8 * temperatureRatio + 0.2;
                
                //Console.WriteLine($"[调试] 微调概率: {microAdjustmentProbability:F3}(已修复)");
                
                if (strategyRoll < microAdjustmentProbability)
                {
                    // 策略A: 微调（Local Search）- 在当前位置附近精细搜索
                    //Console.WriteLine($"[调试] SA阻塞修复 - 对 {targetTask.TaskName} 进行微调策略 (温度比例: {temperatureRatio:F3})");
                    int periodUs = targetTask.PeriodUs.Value;
                    int perturbationRangeUs = (int)(periodUs * 0.1 * (1 + temperatureRatio)); // 10%周期内微调，温度越高范围越大
                    // 方案1：直接生成时基对齐的随机扰动，避免浮点运算
                    int maxSteps = perturbationRangeUs / timeBaseUs;
                    int randomSteps = _random.Next(-maxSteps, maxSteps + 1);
                    int perturbationUs = randomSteps * timeBaseUs;
                    int newOffsetUs = Math.Max(0, Math.Min(taskMaxOffsetUs, currentOffsetUs + perturbationUs));
                    newOffsetUs = (newOffsetUs / timeBaseUs) * timeBaseUs;
                    neighborSolution[targetTask.TaskIndex] = newOffsetUs;
                    
                    // 🔍 调试信息：输出微调结果
                    double periodMs = TaskUtility.ConvertToMilliseconds(periodUs);
                    double perturbationRangeMs = TaskUtility.ConvertToMilliseconds(perturbationRangeUs);
                    double perturbationMs = TaskUtility.ConvertToMilliseconds(perturbationUs);
                    double newOffsetMs = TaskUtility.ConvertToMilliseconds(newOffsetUs);
                    //Console.WriteLine($"[调试] 微调结果 - 周期: {periodMs:F1}ms, 扰动范围: {perturbationRangeMs:F1}ms, 扰动量: {perturbationMs:F1}ms, 新offset: {newOffsetMs:F1}ms");
                }
                else
                {
                    // 策略B: 大范围跳跃（Escape）- 跳出局部最优
                    //Console.WriteLine($"[调试] SA阻塞修复 - 对 {targetTask.TaskName} 进行大跳跃策略 (温度比例: {temperatureRatio:F3})");
                    
                    // 增强改进：负载感知的跳跃位置选择
                    int newOffsetUs = FindLowConflictPosition(targetTask, taskMaxOffsetUs, timeBaseUs, validTasks);
                    neighborSolution[targetTask.TaskIndex] = newOffsetUs;
                    
                    // 🔍 调试信息：输出大跳跃结果
                    double newOffsetMs = TaskUtility.ConvertToMilliseconds(newOffsetUs);
                    //Console.WriteLine($"[调试] 大跳跃结果 - 从 {currentOffsetMs:F1}ms 跳跃到 {newOffsetMs:F1}ms (跳跃距离: {Math.Abs(newOffsetMs - currentOffsetMs):F1}ms)");
                }
            }
            else
            {
                // 对高响应比任务：使用适中的扰动
                int perturbationRangeUs = (int)(taskMaxOffsetUs * PERTURBATION_STRENGTH * _parameters.HighResponseRatioWeight);
                // 方案1：直接生成时基对齐的随机扰动，避免浮点运算
                int maxSteps = perturbationRangeUs / timeBaseUs;
                int randomSteps = _random.Next(-maxSteps, maxSteps + 1);
                int perturbationUs = randomSteps * timeBaseUs;
                int newOffsetUs = Math.Max(0, Math.Min(taskMaxOffsetUs, currentOffsetUs + perturbationUs));
                newOffsetUs = (newOffsetUs / timeBaseUs) * timeBaseUs;
                neighborSolution[targetTask.TaskIndex] = newOffsetUs;
                
                // 🔍 调试信息：输出高响应比任务扰动结果
                double perturbationRangeMs = TaskUtility.ConvertToMilliseconds(perturbationRangeUs);
                double newOffsetMs = TaskUtility.ConvertToMilliseconds(newOffsetUs);
                //Console.WriteLine($"[调试] 响应比扰动 - 任务{targetTask.TaskName}, 扰动范围: {perturbationRangeMs:F1}ms, 从 {currentOffsetMs:F1}ms 到 {newOffsetMs:F1}ms");
            }

            return neighborSolution;
        }

        /// <summary>
        /// 寻找冲突较少的位置进行大跳跃（精确负载感知策略）
        /// </summary>
        /// <param name="targetTask">目标任务</param>
        /// <param name="maxOffsetUs">最大Offset(微秒)</param>
        /// <param name="timeBaseUs">时基微秒值</param>
        /// <param name="allTasks">所有任务列表</param>
        /// <returns>优化的新位置(微秒)</returns>
        private int FindLowConflictPosition(TaskItem targetTask, int maxOffsetUs, int timeBaseUs, List<TaskItem> allTasks)
        {
            int periodUs = targetTask.PeriodUs.Value;
            int bestOffsetUs = 0;
            double minConflictScore = double.MaxValue;
            
            // 🔍 调试信息：输出搜索参数（显示转换）
            double periodMs = TaskUtility.ConvertToMilliseconds(periodUs);
            double maxOffsetMs = TaskUtility.ConvertToMilliseconds(maxOffsetUs);
            double timeBaseMs = TaskUtility.ConvertToMilliseconds(timeBaseUs);
            //Console.WriteLine($"[调试] FindLowConflictPosition - 任务{targetTask.TaskName}: 周期={periodMs:F1}ms, 最大offset={maxOffsetMs:F1}ms, 时基={timeBaseMs:F1}ms");
            
            // 计算超周期（用于精确冲突分析）
            int hyperPeriodUs = TaskUtility.CalculateHyperPeriod(allTasks);
            double hyperPeriodMs = TaskUtility.ConvertToMilliseconds(hyperPeriodUs);
            // 限制分析窗口，避免过度计算
            double analysisWindowMs = Math.Min(hyperPeriodMs, periodMs * 10); // 最多分析10个目标任务周期
            
            // 🔧 修复：针对不可抢占任务优化搜索范围，避免过大offset
            int searchRangeUs = periodUs;
            if (targetTask.NonPreemptive)
            {
                // 不可抢占任务优先在周期前部分搜索，避免大offset
                searchRangeUs = Math.Min(periodUs / 2, maxOffsetUs); // 限制在周期的50%内
                double searchRangeMs = TaskUtility.ConvertToMilliseconds(searchRangeUs);
                //Console.WriteLine($"[调试] 不可抢占任务{targetTask.TaskName}搜索范围限制到{searchRangeMs:F1}ms");
            }
            
            // 在搜索范围内采样多个候选位置
            int sampleCount = Math.Min(20, (int)(searchRangeUs / timeBaseUs)); // 最多20个采样点
            sampleCount = Math.Max(sampleCount, 5); // 至少5个采样点
            
            //Console.WriteLine($"[调试] 精确负载分析 - 任务{targetTask.TaskName} 分析窗口: {analysisWindowMs:F1}ms, 采样点: {sampleCount}");
            
            // 🔍 调试信息：记录所有候选位置的冲突分数
            var candidateResults = new List<(int offsetUs, double score)>();
            
            for (int i = 0; i < sampleCount; i++)
            {
                int candidateOffsetUs = (int)((double)i / sampleCount * searchRangeUs); // 使用限制后的搜索范围
                candidateOffsetUs = (candidateOffsetUs / timeBaseUs) * timeBaseUs;
                candidateOffsetUs = Math.Min(candidateOffsetUs, maxOffsetUs);
                
                // 核心改进：精确冲突计算（考虑整个分析窗口内的所有实例）
                int analysisWindowUs = (int)(analysisWindowMs * 1000);
                double conflictScore = CalculatePreciseConflictScore(targetTask, candidateOffsetUs, allTasks, analysisWindowUs, timeBaseUs);
                
                candidateResults.Add((candidateOffsetUs, conflictScore));
                
                // 记录最佳位置
                if (conflictScore < minConflictScore)
                {
                    minConflictScore = conflictScore;
                    bestOffsetUs = candidateOffsetUs;
                }
            }
            
            // 🔍 调试信息：输出所有候选位置的详细信息
            //Console.WriteLine($"[调试] 候选位置分析结果:");
            candidateResults.OrderBy(r => r.score).Take(5).ToList().ForEach(r => 
            {
                double offsetMs = TaskUtility.ConvertToMilliseconds(r.offsetUs);
                //Console.WriteLine($"  offset={offsetMs:F1}ms, 冲突分数={r.score:F2}");
            });
            
            // 🔍 警告：如果最佳offset过大
            double bestOffsetMs = TaskUtility.ConvertToMilliseconds(bestOffsetUs);
            if (bestOffsetMs > periodMs * 0.5)
            {
                //Console.WriteLine($"[警告] 任务{targetTask.TaskName}的最佳offset({bestOffsetMs:F1}ms)超过周期的50%({periodMs * 0.5:F1}ms)！");
                //Console.WriteLine($"[警告] 这可能表明存在严重的任务冲突或参数配置问题");
            }
            
            //Console.WriteLine($"[调试] 精确负载跳跃 - 为任务{targetTask.TaskName}找到最优位置: {bestOffsetMs:F1}ms (精确冲突分数: {minConflictScore:F2})");
            return bestOffsetUs;
        }

        /// <summary>
        /// 计算精确的冲突分数（考虑超周期内所有实例的冲突）
        /// </summary>
        /// <param name="targetTask">目标任务</param>
        /// <param name="candidateOffsetUs">候选offset位置(微秒)</param>
        /// <param name="allTasks">所有任务列表</param>
        /// <param name="analysisWindowUs">分析窗口大小(微秒)</param>
        /// <param name="timeBaseUs">时基微秒值</param>
        /// <returns>精确冲突分数</returns>
        private double CalculatePreciseConflictScore(TaskItem targetTask, int candidateOffsetUs, List<TaskItem> allTasks, 
            int analysisWindowUs, int timeBaseUs)
        {
            double totalConflictScore = 0;
            int targetPeriodUs = targetTask.PeriodUs.Value;
            int targetExecUs = targetTask.ExecutionTimeUs ?? 10;
            int targetPriority = targetTask.Priority ?? 0;
            
            // 🔍 调试信息：只在分析第一个候选位置或找到最佳位置时输出详细信息
            double candidateOffsetMs = TaskUtility.ConvertToMilliseconds(candidateOffsetUs);
            double targetPeriodMs = TaskUtility.ConvertToMilliseconds(targetPeriodUs);
            bool shouldDebug = candidateOffsetUs == 0 || Math.Abs(candidateOffsetMs % targetPeriodMs) < 0.01;
            
            //if (shouldDebug)
            //{
            //    Console.WriteLine($"[调试] CalculatePreciseConflictScore - 任务{targetTask.TaskName}, offset={candidateOffsetMs:F1}ms:");
            //}
            
            // 为每个其他任务计算冲突
            foreach (var otherTask in allTasks)
            {
                if (otherTask.TaskIndex == targetTask.TaskIndex) continue;
                
                int otherPeriodUs = otherTask.PeriodUs.Value;
                int otherExecUs = otherTask.ExecutionTimeUs ?? 10;
                int otherPriority = otherTask.Priority ?? 0;
                
                if (otherPeriodUs <= 0) continue;
                
                // 精确分析：遍历分析窗口内的所有实例冲突
                double taskConflictScore = 0;
                int conflictCount = 0;
                
                // 直接使用微秒进行精确计算
                double otherPeriodMs = TaskUtility.ConvertToMilliseconds(otherPeriodUs);
                double otherExecMs = TaskUtility.ConvertToMilliseconds(otherExecUs);
                double targetExecMs = TaskUtility.ConvertToMilliseconds(targetExecUs);
                
                // 目标任务的所有实例
                for (int targetStartUs = candidateOffsetUs; targetStartUs < analysisWindowUs; targetStartUs += targetPeriodUs)
                {
                    int targetEndUs = targetStartUs + targetExecUs;
                    
                    // 其他任务的所有实例（假设其他任务offset=0，这是最坏情况估算）
                    for (int otherStartUs = 0; otherStartUs < analysisWindowUs; otherStartUs += otherPeriodUs)
                    {
                        int otherEndUs = otherStartUs + otherExecUs;
                        
                        // 检查时间重叠
                        int overlapStartUs = Math.Max(targetStartUs, otherStartUs);
                        int overlapEndUs = Math.Min(targetEndUs, otherEndUs);
                        int overlapDurationUs = Math.Max(0, overlapEndUs - overlapStartUs);
                        
                        if (overlapDurationUs > 0)
                        {
                            // 智能权重计算（转换为毫秒用于权重计算兼容性）
                            double overlapDurationMs = TaskUtility.ConvertToMilliseconds(overlapDurationUs);
                            double conflictWeight = CalculateConflictWeight(targetTask, otherTask, overlapDurationMs);
                            taskConflictScore += conflictWeight;
                            conflictCount++;
                        }
                    }
                }
                
                totalConflictScore += taskConflictScore;
                
                if (shouldDebug && taskConflictScore > 0)
                {
                    //Console.WriteLine($"  与任务{otherTask.TaskName}冲突: {conflictCount}次, 总分数={taskConflictScore:F2}");
                }
            }
            
            if (shouldDebug)
            {
                //Console.WriteLine($"  总冲突分数: {totalConflictScore:F2}");
            }
            
            return totalConflictScore;
        }

        /// <summary>
        /// 计算冲突权重（考虑优先级、任务特性和重叠时长）
        /// </summary>
        /// <param name="targetTask">目标任务</param>
        /// <param name="otherTask">冲突任务</param>
        /// <param name="overlapDuration">重叠时长</param>
        /// <returns>冲突权重</returns>
        private double CalculateConflictWeight(TaskItem targetTask, TaskItem otherTask, double overlapDuration)
        {
            int targetPriority = targetTask.Priority ?? 0;
            int otherPriority = otherTask.Priority ?? 0;
            
            double baseWeight = overlapDuration;
            double originalWeight = baseWeight;
            
            // 🔍 调试信息：输出权重计算过程（只在第一次调用时输出）
            bool shouldDebugWeight = _random.NextDouble() < 0.01; // 1%概率输出权重计算详情
            
            //if (shouldDebugWeight)
            //{
                //Console.WriteLine($"[调试] 权重计算 - {targetTask.TaskName}(优先级{targetPriority},不可抢占{targetTask.NonPreemptive}) vs {otherTask.TaskName}(优先级{otherPriority},不可抢占{otherTask.NonPreemptive})");
                //Console.WriteLine($"  基础权重(重叠时长): {baseWeight:F2}ms");
            //}
            
            // 优先级相关权重
            if (otherPriority > targetPriority)
            {
                // 高优先级任务冲突：严重问题，高权重
                double priorityMultiplier = (otherPriority - targetPriority + 1) * 2.0;
                baseWeight *= priorityMultiplier;
                //if (shouldDebugWeight) Console.WriteLine($"  高优先级冲突乘数: {priorityMultiplier:F1}, 新权重: {baseWeight:F2}");
            }
            else if (otherPriority < targetPriority)
            {
                // 低优先级任务冲突：可抢占，较低权重
                baseWeight *= 0.5;
                //if (shouldDebugWeight) Console.WriteLine($"  低优先级冲突乘数: 0.5, 新权重: {baseWeight:F2}");
            }
            // 同优先级：基础权重
            
            // 🔧 修复：不可抢占特性权重（降低权重乘数，避免过度惩罚）
            if (targetTask.NonPreemptive && otherPriority > targetPriority)
            {
                // 不可抢占任务阻塞高优先级任务：严重但不要过度惩罚
                baseWeight *= 3.0; // 从10.0降低到3.0
                //if (shouldDebugWeight) Console.WriteLine($"  不可抢占阻塞高优先级乘数: 3.0(已优化), 新权重: {baseWeight:F2}");
            }
            
            if (otherTask.NonPreemptive && targetPriority > otherPriority)
            {
                // 被低优先级不可抢占任务阻塞：中等严重性
                baseWeight *= 2.0; // 从5.0降低到2.0
                //if (shouldDebugWeight) Console.WriteLine($"  被不可抢占任务阻塞乘数: 2.0(已优化), 新权重: {baseWeight:F2}");
            }
            
            // 🔍 警告：权重过大的情况
            if (baseWeight > originalWeight * 50)
            {
                //Console.WriteLine($"[警告] 权重过大 - {targetTask.TaskName} vs {otherTask.TaskName}: {originalWeight:F2} -> {baseWeight:F2} (放大{baseWeight/originalWeight:F1}倍)");
            }
            
            return baseWeight;
        }



        /// <summary>
        /// 生成相关性扰动（考虑任务间依赖关系）
        /// </summary>
        /// <param name="currentSolution">当前解</param>
        /// <param name="validTasks">有效任务列表</param>
        /// <param name="temperature">当前温度</param>
        /// <returns>扰动后的解</returns>
        private Dictionary<int, int> GenerateCorrelationPerturbation(
            Dictionary<int, int> currentSolution, List<TaskItem> validTasks, double temperature)
        {
            var neighborSolution = new Dictionary<int, int>(currentSolution);
            int timeBaseUs = _timeBaseUs;
            
            // 创建任务索引到任务对象的映射，用于获取每个任务的MaxOffsetUs
            var taskIndexToTask = validTasks.ToDictionary(t => t.TaskIndex, t => t);

            // 选择一个主要任务进行扰动（跳过手动输入任务）
            var nonManualTasks = validTasks.Where(t => !t.ManualInput).ToList();
            
            // 如果没有可扰动的任务，返回原解
            if (nonManualTasks.Count == 0)
            {
                return neighborSolution;
            }
            
            var primaryTask = nonManualTasks[_random.Next(nonManualTasks.Count)];
            int primaryCurrentOffsetUs = neighborSolution[primaryTask.TaskIndex];
            int primaryMaxOffsetUs = primaryTask.MaxOffsetUs;
            
            // 扰动主要任务
            int perturbationRangeUs = (int)(primaryMaxOffsetUs * PERTURBATION_STRENGTH);
            // 方案1：直接生成时基对齐的随机扰动，避免浮点运算
            int maxSteps = perturbationRangeUs / timeBaseUs;
            int randomSteps = _random.Next(-maxSteps, maxSteps + 1);
            int perturbationUs = randomSteps * timeBaseUs;
            int newPrimaryOffsetUs = Math.Max(0, Math.Min(primaryMaxOffsetUs, primaryCurrentOffsetUs + perturbationUs));
            neighborSolution[primaryTask.TaskIndex] = (newPrimaryOffsetUs / timeBaseUs) * timeBaseUs;

            // 连锁扰动：相关任务（相似周期的任务，跳过手动输入任务）
            if (_random.NextDouble() < _parameters.CorrelationPerturbationProbability)
            {
                var correlatedTasks = validTasks.Where(t => 
                    t.TaskIndex != primaryTask.TaskIndex &&
                    !t.ManualInput &&
                    Math.Abs(t.PeriodUs.Value - primaryTask.PeriodUs.Value) <= primaryTask.PeriodUs.Value * 0.5 // 周期相似度50%以内
                ).ToList();

                foreach (var correlatedTask in correlatedTasks)
                {
                    if (_random.NextDouble() < 0.3) // 30%概率扰动相关任务
                    {
                        int correlatedCurrentOffsetUs = neighborSolution[correlatedTask.TaskIndex];
                        int correlatedMaxOffsetUs = correlatedTask.MaxOffsetUs;
                        int smallPerturbationUs = (int)(perturbationUs * 0.3); // 更小的扰动
                        int newCorrelatedOffsetUs = Math.Max(0, Math.Min(correlatedMaxOffsetUs, correlatedCurrentOffsetUs + smallPerturbationUs));
                        neighborSolution[correlatedTask.TaskIndex] = (newCorrelatedOffsetUs / timeBaseUs) * timeBaseUs;
                    }
                }
            }

            return neighborSolution;
        }

        /// <summary>
        /// 记录扰动改进效果
        /// </summary>
        /// <param name="advancedPerturbationInfo">精细化扰动信息</param>
        /// <param name="perturbationType">扰动类型</param>
        /// <param name="improvement">改进量</param>
        private void RecordPerturbationImprovement(AdvancedPerturbationInfo advancedPerturbationInfo, 
            string perturbationType, double improvement)
        {
            switch (perturbationType)
            {
                case "Random":
                    advancedPerturbationInfo.EffectivenessStats.RandomPerturbationImprovements++;
                    break;
                case "Targeted":
                    advancedPerturbationInfo.EffectivenessStats.TargetedPerturbationImprovements++;
                    advancedPerturbationInfo.TargetedStats.HighResponseRatioTaskPerturbations++;
                    advancedPerturbationInfo.TargetedStats.AverageResponseRatioReduction += improvement;
                    break;
                case "Correlation":
                    advancedPerturbationInfo.EffectivenessStats.CorrelationPerturbationImprovements++;
                    advancedPerturbationInfo.CorrelationStats.ChainPerturbationCount++;
                    break;
            }
        }

        /// <summary>
        /// 完成扰动策略统计
        /// </summary>
        /// <param name="advancedPerturbationInfo">精细化扰动信息</param>
        /// <param name="perturbationTypeHistory">扰动类型历史</param>
        private void FinalizePerturbationStatistics(AdvancedPerturbationInfo advancedPerturbationInfo, 
            List<string> perturbationTypeHistory)
        {
            if (perturbationTypeHistory.Count == 0) return;

            // 计算总统计
            var totalPerturbations = perturbationTypeHistory.Count;
            var targetedCount = perturbationTypeHistory.Count(p => p == "Targeted");
            var correlationCount = perturbationTypeHistory.Count(p => p == "Correlation");
            var randomCount = perturbationTypeHistory.Count(p => p == "Random");

            // 更新针对性扰动统计
            advancedPerturbationInfo.TargetedStats.TotalPerturbations = totalPerturbations;
            if (advancedPerturbationInfo.TargetedStats.HighResponseRatioTaskPerturbations > 0)
            {
                advancedPerturbationInfo.TargetedStats.AverageResponseRatioReduction /= 
                    advancedPerturbationInfo.TargetedStats.HighResponseRatioTaskPerturbations;
            }

            // 更新相关性扰动统计
            advancedPerturbationInfo.CorrelationStats.TotalPerturbationDecisions = correlationCount;
            if (correlationCount > 0)
            {
                advancedPerturbationInfo.CorrelationStats.AverageChainLength = 
                    (double)advancedPerturbationInfo.CorrelationStats.ChainPerturbationCount / correlationCount;
            }

            // 计算效果比较
            var randomEffectiveness = randomCount > 0 ? 
                (double)advancedPerturbationInfo.EffectivenessStats.RandomPerturbationImprovements / randomCount : 0;
            var targetedEffectiveness = targetedCount > 0 ? 
                (double)advancedPerturbationInfo.EffectivenessStats.TargetedPerturbationImprovements / targetedCount : 0;
            var correlationEffectiveness = correlationCount > 0 ? 
                (double)advancedPerturbationInfo.EffectivenessStats.CorrelationPerturbationImprovements / correlationCount : 0;

            advancedPerturbationInfo.EffectivenessStats.TargetedEffectivenessRatio = 
                randomEffectiveness > 0 ? targetedEffectiveness / randomEffectiveness : 1.0;
            advancedPerturbationInfo.EffectivenessStats.CorrelationEffectivenessRatio = 
                randomEffectiveness > 0 ? correlationEffectiveness / randomEffectiveness : 1.0;
        }

        #endregion

        #region 重启机制和智能策略适应

        /// <summary>
        /// 执行单轮模拟退火
        /// </summary>
        /// <param name="validTasks">有效任务列表</param>
        /// <param name="restartRound">重启轮次</param>
        /// <param name="restartInfo">重启信息</param>
        /// <param name="strategyAdaptationInfo">策略适应信息</param>
        /// <returns>单轮结果</returns>
        private SingleRunResult ExecuteSingleAnnealingRun(List<TaskItem> validTasks, int restartRound, 
            RestartMechanismInfo restartInfo, StrategyAdaptationInfo strategyAdaptationInfo, 
            Dictionary<int, int> externalInitialSolution = null)
        {
            // 生成初始解（重启时使用不同策略，第一轮可能使用外部初始解）
            var currentSolution = (externalInitialSolution != null && restartRound == 0) ? 
                CloneSolution(externalInitialSolution) : 
                GenerateInitialSolutionForRestart(validTasks, restartRound);
            var currentCost = EvaluateSolutionWithTemplate(currentSolution, validTasks);

            // 初始化最佳解缓存管理器 - 三级挑选机制的一级挑选
            _bestSolutionCache = new ThreeTierSelectionCache(validTasks, "SA");
            _bestSolutionCache.TryAddSolution(currentSolution, currentCost);

            // 计算自适应初始温度
            double temperature = CalculateInitialTemperatureForRun(validTasks, currentSolution, restartRound);

            // 初始化控制器
            _innerLoopController.Reset(temperature);
            _coolingController.Reset(temperature, _parameters.FinalTemperature);
            if (restartRound == 0) _strategyTracker.Reset(); // 只在第一轮重置策略跟踪器

            // 优化主循环
            int iteration = 0;
            int acceptedMoves = 0;
            int improvingMoves = 0;
            bool earlyStopTriggered = false;
            var costHistory = new List<double>();
            int lastImprovementIteration = 0;

            while (temperature > _parameters.FinalTemperature && 
                   iteration < _parameters.MaxIterations && 
                   !earlyStopTriggered)
            {
                // 动态内循环控制
                int dynamicInnerLoopCount = _innerLoopController.CalculateInnerLoopCount(temperature, acceptedMoves, iteration);
                
                for (int innerLoop = 0; innerLoop < dynamicInnerLoopCount && 
                     iteration < _parameters.MaxIterations && 
                     !earlyStopTriggered; innerLoop++)
                {
                    // 智能策略选择
                    var strategyResult = SelectAndExecuteStrategy(currentSolution, validTasks, temperature);
                    var neighborSolution = strategyResult.Solution;
                    var strategyUsed = strategyResult.Strategy;
                    
                    var neighborCost = EvaluateSolutionWithTemplate(neighborSolution, validTasks);
                    double costDelta = neighborCost - currentCost;

                    // Metropolis判决
                    bool acceptMove = false;
                    bool isImprovement = false;
                    
                    if (costDelta < 0)
                    {
                        acceptMove = true;
                        isImprovement = true;
                        improvingMoves++;
                    }
                    else
                    {
                        double acceptanceProbability = Math.Exp(-costDelta / temperature);
                        acceptMove = _random.NextDouble() < acceptanceProbability;
                    }

                    // 更新策略统计
                    _strategyTracker.RecordStrategyResult(strategyUsed, isImprovement, costDelta);

                    if (acceptMove)
                    {
                        currentSolution = neighborSolution;
                        currentCost = neighborCost;
                        acceptedMoves++;

                        // 尝试将接受的解添加到缓存（自动处理最佳成本更新和去重）
                        var wasAdded = _bestSolutionCache.TryAddSolution(currentSolution, currentCost);
                        if (wasAdded && currentCost < _bestSolutionCache.GetBestCost())
                        {
                            lastImprovementIteration = iteration;
                        }
                    }

                    costHistory.Add(currentCost);
                    
                    // 早停检测
                    if (_parameters.UseEarlyStopping)
                    {
                        earlyStopTriggered = CheckEarlyStoppingConditionsAdvanced(
                            iteration, lastImprovementIteration, costHistory, _bestSolutionCache.GetBestCost());
                    }

                    iteration++;
                }

                // 自适应冷却控制
                temperature = _coolingController.UpdateTemperature(temperature, acceptedMoves, improvingMoves, iteration);
            }

            // 迭代结束后，执行一级挑选：从缓存的相同成本解中选出最优解
            var finalBestSolution = _bestSolutionCache.SelectBestFromCache();
            var finalBestCost = _bestSolutionCache.GetBestCost();

            if (_bestSolutionCache.GetCachedCount() > 1)
            {
                //Console.WriteLine($"[一级挑选-SA] 迭代结束，缓存了 {_bestSolutionCache.GetCachedCount()} 个最佳成本解，最终成本={finalBestCost:F6}");
            }

            return new SingleRunResult
            {
                BestSolution = finalBestSolution ?? currentSolution,
                BestCost = finalBestCost,
                TotalIterations = iteration,
                AcceptedMoves = acceptedMoves,
                ImprovingMoves = improvingMoves,
                EarlyStopTriggered = earlyStopTriggered,
                FinalTemperature = temperature
            };
        }

        /// <summary>
        /// 智能策略选择与执行（多臂老虎机）
        /// </summary>
        /// <param name="currentSolution">当前解</param>
        /// <param name="validTasks">有效任务列表</param>
        /// <param name="temperature">当前温度</param>
        /// <returns>策略执行结果</returns>
        private StrategyExecutionResult SelectAndExecuteStrategy(Dictionary<int, int> currentSolution, 
            List<TaskItem> validTasks, double temperature)
        {
            // 使用多臂老虎机选择策略
            var selectedStrategy = _strategyTracker.SelectStrategy(_random);
            Dictionary<int, int> solution;

            switch (selectedStrategy)
            {
                case PerturbationStrategy.Random:
                    solution = GenerateNeighborSolutionWithTemperature(currentSolution, temperature, validTasks);
                    break;
                    
                case PerturbationStrategy.Targeted:
                    solution = GenerateTargetedPerturbation(currentSolution, validTasks, temperature);
                    break;
                    
                case PerturbationStrategy.Correlation:
                    solution = GenerateCorrelationPerturbation(currentSolution, validTasks, temperature);
                    break;
                    
                case PerturbationStrategy.Hybrid:
                    solution = GenerateHybridPerturbation(currentSolution, validTasks, temperature);
                    break;
                    
                default:
                    solution = GenerateNeighborSolutionWithTemperature(currentSolution, temperature, validTasks);
                    selectedStrategy = PerturbationStrategy.Random;
                    break;
            }

            return new StrategyExecutionResult
            {
                Solution = solution,
                Strategy = selectedStrategy
            };
        }

        /// <summary>
        /// 混合扰动策略
        /// </summary>
        /// <param name="currentSolution">当前解</param>
        /// <param name="validTasks">有效任务列表</param>
        /// <param name="temperature">当前温度</param>
        /// <returns>扰动后的解</returns>
        private Dictionary<int, int> GenerateHybridPerturbation(Dictionary<int, int> currentSolution, 
            List<TaskItem> validTasks, double temperature)
        {
            // 混合策略：根据温度动态调整策略组合
            double temperatureRatio = CalculateTemperatureRatio(temperature);
            
            if (temperatureRatio > 0.7) // 高温：大范围随机探索
            {
                return GenerateNeighborSolutionWithTemperature(currentSolution, temperature, validTasks);
            }
            else if (temperatureRatio > 0.3) // 中温：目标导向扰动
            {
                return GenerateTargetedPerturbation(currentSolution, validTasks, temperature);
            }
            else // 低温：精细化局部优化
            {
                return GenerateCorrelationPerturbation(currentSolution, validTasks, temperature);
            }
        }

        /// <summary>
        /// 为重启生成不同的初始解
        /// </summary>
        /// <param name="validTasks">有效任务列表</param>
        /// <param name="restartRound">重启轮次</param>
        /// <returns>初始解</returns>
        private Dictionary<int, int> GenerateInitialSolutionForRestart(List<TaskItem> validTasks, int restartRound)
        {
            // 在第一轮（非重启轮）时，使用启发式种子
            if (restartRound == 0 && _parameters.UseHeuristicSeeding)
            {
                // 计算所有任务的最大MaxOffsetUs值，用于启发式种子生成
                int actualMaxOffsetUs = validTasks.Any(t => t.MaxOffsetUs > 0) ? 
                    validTasks.Where(t => t.MaxOffsetUs > 0).Max(t => t.MaxOffsetUs) : _maxOffsetUs;
                
                // 调用 TaskUtility 中的共享方法
                var heuristicSolution = TaskUtility.GenerateHeuristicSeed(validTasks, _timeBaseUs, actualMaxOffsetUs);
                if (heuristicSolution != null && heuristicSolution.Count > 0)
                {
                    // 成功生成种子，直接返回
                    return heuristicSolution;
                }
                // 如果生成失败，则回退到下面的随机生成逻辑
            }

            // 对于重启轮次，或者当启发式失败时，使用您现有的多样化随机策略
            var solution = new Dictionary<int, int>();
            int timeBaseUs = _timeBaseUs;

            foreach (var task in validTasks)
            {
                int randomOffsetUs;
                int taskMaxOffsetUs = task.MaxOffsetUs;
                
                if (task.ManualInput && task.OffsetUs.HasValue)
                {
                    solution[task.TaskIndex] = (int)Math.Min(task.OffsetUs.Value, taskMaxOffsetUs);
                    continue;
                }
                
                switch (restartRound % 4)
                {
                    case 0: // 完全随机
                        // 方案1：直接生成时基对齐的随机offset，避免浮点运算
                        int maxSteps0 = taskMaxOffsetUs / timeBaseUs;
                        randomOffsetUs = _random.Next(0, maxSteps0 + 1) * timeBaseUs;
                        break;
                        
                    case 1: // 偏向小值
                        // 方案1：使用整数版本的偏向小值分布
                        int maxSteps1 = taskMaxOffsetUs / timeBaseUs;
                        int randomStep1 = _random.Next(0, maxSteps1 + 1);
                        randomOffsetUs = (int)(Math.Pow((double)randomStep1 / maxSteps1, 0.5) * maxSteps1) * timeBaseUs;
                        break;
                        
                    case 2: // 偏向大值
                        // 方案1：使用整数版本的偏向大值分布
                        int maxSteps2 = taskMaxOffsetUs / timeBaseUs;
                        int randomStep2 = _random.Next(0, maxSteps2 + 1);
                        randomOffsetUs = (int)(Math.Pow((double)randomStep2 / maxSteps2, 2.0) * maxSteps2) * timeBaseUs;
                        break;
                        
                    case 3: // 集中在中间
                        // 方案1：使用整数版本的中间分布
                        int maxSteps3 = taskMaxOffsetUs / timeBaseUs;
                        int r1 = _random.Next(0, maxSteps3 + 1);
                        int r2 = _random.Next(0, maxSteps3 + 1);
                        randomOffsetUs = ((r1 + r2) / 2) * timeBaseUs;
                        break;
                        
                    default:
                        // 方案1：默认随机
                        int maxStepsDefault = taskMaxOffsetUs / timeBaseUs;
                        randomOffsetUs = _random.Next(0, maxStepsDefault + 1) * timeBaseUs;
                        break;
                }
                
                // 对齐到时基
                solution[task.TaskIndex] = (randomOffsetUs / timeBaseUs) * timeBaseUs;
            }

            return solution;
        }

        /// <summary>
        /// 计算重启轮次的初始温度
        /// </summary>
        /// <param name="validTasks">有效任务列表</param>
        /// <param name="initialSolution">初始解</param>
        /// <param name="restartRound">重启轮次</param>
        /// <returns>初始温度</returns>
        private double CalculateInitialTemperatureForRun(List<TaskItem> validTasks, 
            Dictionary<int, int> initialSolution, int restartRound)
        {
            if (!_parameters.UseAdaptiveInitialTemperature)
            {
                // 重启时稍微提高温度以增加探索性
                return _parameters.InitialTemperature * (1.0 + restartRound * 0.1);
            }

            var adaptiveInfo = CalculateAdaptiveInitialTemperature(validTasks, initialSolution);
            return adaptiveInfo.AdaptiveInitialTemperature * (1.0 + restartRound * 0.05);
        }

        /// <summary>
        /// 检查是否应该继续重启
        /// </summary>
        /// <param name="singleRunResult">单轮结果</param>
        /// <param name="restartRound">当前重启轮次</param>
        /// <param name="globalBestCost">全局最佳成本</param>
        /// <returns>是否继续重启</returns>
        private bool ShouldContinueRestart(SingleRunResult singleRunResult, int restartRound, double globalBestCost)
        {
            // 达到最大重启次数
            if (restartRound >= _parameters.MaxRestarts - 1)
                return false;

            // 如果找到了非常好的解，提前停止
            if (globalBestCost < _parameters.RestartStopThreshold)
                return false;

            // 如果连续几轮都没有改进，停止重启
            if (restartRound >= _parameters.NoImprovementRestartThreshold)
            {
                // 检查最近几轮是否有显著改进
                var recentImprovements = _strategyTracker.GetRecentImprovementRate(_parameters.NoImprovementRestartThreshold);
                if (recentImprovements < _parameters.MinImprovementRateForRestart)
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 为下一轮重启做准备
        /// </summary>
        /// <param name="restartRound">当前重启轮次</param>
        /// <param name="singleRunResult">单轮结果</param>
        private void PrepareForNextRestart(int restartRound, SingleRunResult singleRunResult)
        {
            // 更新策略适应性学习
            _strategyTracker.UpdateForNextRestart();
            
            // 重置控制器（为下一轮做准备）
            _innerLoopController.PrepareForRestart();
            _coolingController.PrepareForRestart();
        }

        /// <summary>
        /// 构建最终统计信息
        /// </summary>
        /// <param name="restartInfo">重启信息</param>
        /// <param name="strategyAdaptationInfo">策略适应信息</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="globalBestCost">全局最佳成本</param>
        /// <param name="validTasks">有效任务列表</param>
        /// <returns>最终统计信息</returns>
        private OptimizationStatistics BuildFinalStatistics(RestartMechanismInfo restartInfo, 
            StrategyAdaptationInfo strategyAdaptationInfo, DateTime startTime, 
            double globalBestCost, List<TaskItem> validTasks)
        {
            var endTime = DateTime.Now;
            
            return new OptimizationStatistics
            {
                InitialCost = restartInfo.InitialCosts.FirstOrDefault(),
                FinalCost = globalBestCost,
                BestCost = globalBestCost,
                TotalIterations = restartInfo.TotalIterations,
                AcceptedMoves = restartInfo.TotalAcceptedMoves,
                ImprovingMoves = restartInfo.TotalImprovingMoves,
                AcceptanceRate = restartInfo.TotalIterations > 0 ? (double)restartInfo.TotalAcceptedMoves / restartInfo.TotalIterations : 0,
                ImprovementRate = restartInfo.TotalIterations > 0 ? (double)restartInfo.TotalImprovingMoves / restartInfo.TotalIterations : 0,
                OptimizationTime = endTime - startTime,
                RestartInfo = restartInfo,
                                 StrategyAdaptationInfo = strategyAdaptationInfo,
                 InnerLoopAdaptationInfo = _innerLoopController.GetStatistics(),
                 CoolingAdaptationInfo = _coolingController.GetStatistics()
            };
        }

        /// <summary>
        /// 增强版早停检测
        /// </summary>
        /// <param name="currentIteration">当前迭代次数</param>
        /// <param name="lastImprovementIteration">最后改进迭代次数</param>
        /// <param name="costHistory">成本历史</param>
        /// <param name="bestCost">最佳成本</param>
        /// <returns>是否应该早停</returns>
        private bool CheckEarlyStoppingConditionsAdvanced(int currentIteration, int lastImprovementIteration, 
            List<double> costHistory, double bestCost)
        {
            // 基础早停条件
            int noImprovementIterations = currentIteration - lastImprovementIteration;
            if (noImprovementIterations >= _parameters.NoImprovementThreshold)
                return true;

            // 收敛检测
            if (costHistory.Count >= _parameters.ConvergenceWindowSize)
            {
                var recentCosts = costHistory.Skip(costHistory.Count - _parameters.ConvergenceWindowSize).ToList();
                var variance = CalculateVariance(recentCosts);
                if (variance < _parameters.ConvergenceThreshold)
                    return true;
            }

            // 策略效果检测：如果所有策略都表现很差，提前停止
            if (_strategyTracker.AllStrategiesPerformingPoorly())
                return true;

            return false;
        }

        #endregion


    }

    #region 数据结构定义

    /// <summary>
    /// 优化参数
    /// </summary>
    public class OptimizationParameters
    {
        /// <summary>
        /// 初始温度（如果使用自适应温度设定，此值将被覆盖）
        /// </summary>
        public double InitialTemperature { get; set; } = 1000.0;

        /// <summary>
        /// 最终温度
        /// </summary>
        public double FinalTemperature { get; set; } = 0.1;

        /// <summary>
        /// 冷却率
        /// </summary>
        public double CoolingRate { get; set; } = 0.995;

        /// <summary>
        /// 最大迭代次数
        /// </summary>
        public int MaxIterations { get; set; } = 10000;//20000;

        /// <summary>
        /// 是否启用自适应初始温度设定
        /// </summary>
        public bool UseAdaptiveInitialTemperature { get; set; } = true;

        /// <summary>
        /// 目标初始接受率 (0.0 - 1.0)
        /// </summary>
        public double TargetInitialAcceptanceRate { get; set; } = 0.85;

        /// <summary>
        /// 随机游走步数（用于计算自适应温度）
        /// </summary>
        public int RandomWalkSteps { get; set; } = 200;

        /// <summary>
        /// 最小初始温度（防止温度过低）
        /// </summary>
        public double MinInitialTemperature { get; set; } = 1.0;

        /// <summary>
        /// 最大初始温度（防止温度过高）
        /// </summary>
        public double MaxInitialTemperature { get; set; } = 10000.0;

        /// <summary>
        /// 是否启用动态扰动策略
        /// </summary>
        public bool UseDynamicPerturbation { get; set; } = true;

        /// <summary>
        /// 高温时的最大扰动任务数比例 (0.0 - 1.0)
        /// </summary>
        public double HighTempMaxPerturbationRatio { get; set; } = 0.8;

        /// <summary>
        /// 低温时的最小扰动任务数比例 (0.0 - 1.0)
        /// </summary>
        public double LowTempMinPerturbationRatio { get; set; } = 0.1;

        /// <summary>
        /// 高温时的最大扰动范围比例 (0.0 - 1.0)
        /// </summary>
        public double HighTempMaxOffsetRatio { get; set; } = 1.0;

        /// <summary>
        /// 低温时的最小扰动范围比例 (0.0 - 1.0)
        /// </summary>
        public double LowTempMinOffsetRatio { get; set; } = 0.05;

        /// <summary>
        /// 早停准则：启用早停检测
        /// </summary>
        public bool UseEarlyStopping { get; set; } = false;

        /// <summary>
        /// 早停准则：无改进迭代阈值
        /// </summary>
        public int NoImprovementThreshold { get; set; } = 100;

        /// <summary>
        /// 早停准则：最小改进幅度（小于此值视为无显著改进）
        /// </summary>
        public double ImprovementThreshold { get; set; } = 0.001;

        /// <summary>
        /// 早停准则：收敛检测窗口大小
        /// </summary>
        public int ConvergenceWindowSize { get; set; } = 50;

        /// <summary>
        /// 早停准则：收敛阈值（成本变化小于此值视为收敛）
        /// </summary>
        public double ConvergenceThreshold { get; set; } = 0.0001;

        /// <summary>
        /// 扰动策略：使用精细化扰动策略
        /// </summary>
        public bool UseAdvancedPerturbation { get; set; } = true;

        /// <summary>
        /// 扰动策略：针对性扰动 - 优先扰动高响应比任务
        /// </summary>
        public bool UseTargetedPerturbation { get; set; } = true;

        /// <summary>
        /// 扰动策略：相关性扰动 - 考虑任务间依赖关系
        /// </summary>
        public bool UseCorrelationPerturbation { get; set; } = true;

        /// <summary>
        /// 扰动策略：高响应比任务的扰动权重倍数
        /// </summary>
        public double HighResponseRatioWeight { get; set; } = 2.0;

        /// <summary>
        /// 扰动策略：相关任务的连锁扰动概率
        /// </summary>
        public double CorrelationPerturbationProbability { get; set; } = 0.3;

        #region 重启机制参数

        /// <summary>
        /// 最大重启次数
        /// </summary>
        public int MaxRestarts { get; set; } = 3;

        /// <summary>
        /// 重启停止阈值（找到成本低于此值的解时停止重启）
        /// </summary>
        public double RestartStopThreshold { get; set; } = 0.001;

        /// <summary>
        /// 无改进重启阈值（连续多少轮重启无改进后停止）
        /// </summary>
        public int NoImprovementRestartThreshold { get; set; } = 2;

        /// <summary>
        /// 继续重启的最小改进率
        /// </summary>
        public double MinImprovementRateForRestart { get; set; } = 0.01;

        #endregion

        #region 动态策略适应参数

        /// <summary>
        /// 启用策略自适应（多臂老虎机）
        /// </summary>
        public bool UseStrategyAdaptation { get; set; } = true;

        /// <summary>
        /// 策略探索率（ε-贪心算法中的ε值）
        /// </summary>
        public double StrategyExplorationRate { get; set; } = 0.1;

        /// <summary>
        /// 策略学习窗口大小
        /// </summary>
        public int StrategyLearningWindow { get; set; } = 100;

        #endregion

        #region 自适应内循环参数

        /// <summary>
        /// 启用动态内循环控制
        /// </summary>
        public bool UseDynamicInnerLoop { get; set; } = true;

        /// <summary>
        /// 最小内循环次数
        /// </summary>
        public int MinInnerLoopCount { get; set; } = 10;

        /// <summary>
        /// 最大内循环次数
        /// </summary>
        public int MaxInnerLoopCount { get; set; } = 100;

        /// <summary>
        /// 接受率目标（用于调整内循环次数）
        /// </summary>
        public double TargetAcceptanceRate { get; set; } = 0.3;

        #endregion

        #region 自适应冷却参数

        /// <summary>
        /// 启用自适应冷却控制
        /// </summary>
        public bool UseAdaptiveCooling { get; set; } = true;

        /// <summary>
        /// 改进加速冷却因子（有改进时加快冷却）
        /// </summary>
        public double ImprovementCoolingFactor { get; set; } = 1.05;

        /// <summary>
        /// 停滞减缓冷却因子（无改进时减慢冷却）
        /// </summary>
        public double StagnationCoolingFactor { get; set; } = 0.98;

        /// <summary>
        /// 重加温阈值（连续多少次无接受后重加温）
        /// </summary>
        public int ReheatingThreshold { get; set; } = 100;

        /// <summary>
        /// 重加温倍数
        /// </summary>
        public double ReheatingMultiplier { get; set; } = 1.2;

        /// <summary>
        /// 是否在第一轮运行时使用启发式种子作为初始解。
        /// </summary>
        public bool UseHeuristicSeeding { get; set; } = true;

        /// <summary>
        /// 随机种子，为null时使用时间种子
        /// </summary>
        public int? RandomSeed { get; set; } = null;

        #endregion
    }

    /// <summary>
    /// 模拟退火优化结果
    /// </summary>
    public class SimulatedAnnealingResult
    {
        /// <summary>
        /// 优化是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 优化后的任务列表
        /// </summary>
        public List<TaskItem> OptimizedTasks { get; set; }

        /// <summary>
        /// 最佳成本值
        /// </summary>
        public double BestCost { get; set; }

        /// <summary>
        /// 最终的可调度性分析结果
        /// </summary>
        public SchedulabilityResult SchedulabilityResult { get; set; }

        /// <summary>
        /// 优化统计信息
        /// </summary>
        public OptimizationStatistics Statistics { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; } = "";
    }

    /// <summary>
    /// 优化统计信息
    /// </summary>
    public class OptimizationStatistics
    {
        /// <summary>
        /// 初始成本值
        /// </summary>
        public double InitialCost { get; set; }

        /// <summary>
        /// 最终成本值
        /// </summary>
        public double FinalCost { get; set; }

        /// <summary>
        /// 最佳成本值
        /// </summary>
        public double BestCost { get; set; }

        /// <summary>
        /// 找到最佳解的迭代次数
        /// </summary>
        public int BestIteration { get; set; }

        /// <summary>
        /// 总迭代次数
        /// </summary>
        public int TotalIterations { get; set; }

        /// <summary>
        /// 接受移动次数
        /// </summary>
        public int AcceptedMoves { get; set; }

        /// <summary>
        /// 改进移动次数
        /// </summary>
        public int ImprovingMoves { get; set; }

        /// <summary>
        /// 接受率
        /// </summary>
        public double AcceptanceRate { get; set; }

        /// <summary>
        /// 改进率
        /// </summary>
        public double ImprovementRate { get; set; }

        /// <summary>
        /// 优化耗时
        /// </summary>
        public TimeSpan OptimizationTime { get; set; }

        /// <summary>
        /// 自适应温度计算信息
        /// </summary>
        public AdaptiveTemperatureInfo AdaptiveTemperatureInfo { get; set; }

        /// <summary>
        /// 动态扰动策略信息
        /// </summary>
        public DynamicPerturbationInfo DynamicPerturbationInfo { get; set; }

        /// <summary>
        /// 早停信息
        /// </summary>
        public EarlyStoppingInfo EarlyStoppingInfo { get; set; }

        /// <summary>
        /// 精细化扰动信息
        /// </summary>
        public AdvancedPerturbationInfo AdvancedPerturbationInfo { get; set; }

        /// <summary>
        /// 重启机制信息
        /// </summary>
        public RestartMechanismInfo RestartInfo { get; set; }

        /// <summary>
        /// 策略自适应信息
        /// </summary>
        public StrategyAdaptationInfo StrategyAdaptationInfo { get; set; }

        /// <summary>
        /// 内循环自适应信息
        /// </summary>
        public InnerLoopAdaptationInfo InnerLoopAdaptationInfo { get; set; }

        /// <summary>
        /// 冷却自适应信息
        /// </summary>
        public CoolingAdaptationInfo CoolingAdaptationInfo { get; set; }
    }

    /// <summary>
    /// 自适应温度计算信息
    /// </summary>
    public class AdaptiveTemperatureInfo
    {
        /// <summary>
        /// 是否使用了自适应温度
        /// </summary>
        public bool WasUsed { get; set; }

        /// <summary>
        /// 原始设定的初始温度
        /// </summary>
        public double OriginalInitialTemperature { get; set; }

        /// <summary>
        /// 计算得出的自适应初始温度
        /// </summary>
        public double AdaptiveInitialTemperature { get; set; }

        /// <summary>
        /// 目标接受率
        /// </summary>
        public double TargetAcceptanceRate { get; set; }

        /// <summary>
        /// 随机游走步数
        /// </summary>
        public int RandomWalkSteps { get; set; }

        /// <summary>
        /// 随机游走中发现的平均成本增量
        /// </summary>
        public double AverageCostIncrease { get; set; }

        /// <summary>
        /// 随机游走中有效的成本增加移动次数
        /// </summary>
        public int ValidCostIncreaseCount { get; set; }

        /// <summary>
        /// 自适应温度计算耗时
        /// </summary>
        public TimeSpan CalculationTime { get; set; }
    }

    /// <summary>
    /// 动态扰动策略信息
    /// </summary>
    public class DynamicPerturbationInfo
    {
        /// <summary>
        /// 是否使用了动态扰动
        /// </summary>
        public bool WasUsed { get; set; }

        /// <summary>
        /// 初始温度
        /// </summary>
        public double InitialTemperature { get; set; }

        /// <summary>
        /// 最终温度
        /// </summary>
        public double FinalTemperature { get; set; }

        /// <summary>
        /// 初始扰动统计
        /// </summary>
        public PerturbationStatistics InitialPerturbationStats { get; set; }

        /// <summary>
        /// 最终扰动统计
        /// </summary>
        public PerturbationStatistics FinalPerturbationStats { get; set; }

        /// <summary>
        /// 平均扰动任务数
        /// </summary>
        public double AveragePerturbedTaskCount { get; set; }

        /// <summary>
        /// 平均扰动范围比例
        /// </summary>
        public double AverageOffsetRatio { get; set; }

        /// <summary>
        /// 扰动策略变化统计
        /// </summary>
        public List<PerturbationSnapshot> PerturbationSnapshots { get; set; }

        public DynamicPerturbationInfo()
        {
            PerturbationSnapshots = new List<PerturbationSnapshot>();
        }
    }

    /// <summary>
    /// 扰动统计信息
    /// </summary>
    public class PerturbationStatistics
    {
        /// <summary>
        /// 扰动任务数
        /// </summary>
        public int PerturbedTaskCount { get; set; }

        /// <summary>
        /// 扰动任务数比例
        /// </summary>
        public double PerturbedTaskRatio { get; set; }

        /// <summary>
        /// 扰动范围比例
        /// </summary>
        public double OffsetRatio { get; set; }

        /// <summary>
        /// 温度值
        /// </summary>
        public double Temperature { get; set; }
    }

    /// <summary>
    /// 扰动快照（用于记录扰动策略在不同温度下的变化）
    /// </summary>
    public class PerturbationSnapshot
    {
        /// <summary>
        /// 迭代次数
        /// </summary>
        public int Iteration { get; set; }

        /// <summary>
        /// 温度值
        /// </summary>
        public double Temperature { get; set; }

        /// <summary>
        /// 温度比例（相对于初始温度）
        /// </summary>
        public double TemperatureRatio { get; set; }

        /// <summary>
        /// 扰动任务数
        /// </summary>
        public int PerturbedTaskCount { get; set; }

        /// <summary>
        /// 扰动范围比例
        /// </summary>
        public double OffsetRatio { get; set; }
    }

    /// <summary>
    /// 早停信息类
    /// </summary>
    public class EarlyStoppingInfo
    {
        /// <summary>
        /// 是否使用了早停
        /// </summary>
        public bool WasUsed { get; set; }

        /// <summary>
        /// 是否触发了早停
        /// </summary>
        public bool WasTriggered { get; set; }

        /// <summary>
        /// 触发早停的原因
        /// </summary>
        public string StoppingReason { get; set; } = "";

        /// <summary>
        /// 最后一次改进的迭代次数
        /// </summary>
        public int LastImprovementIteration { get; set; }

        /// <summary>
        /// 无改进迭代数
        /// </summary>
        public int NoImprovementIterations { get; set; }

        /// <summary>
        /// 收敛检测窗口内的成本变化
        /// </summary>
        public double ConvergenceWindowVariance { get; set; }

        /// <summary>
        /// 早停节省的迭代数
        /// </summary>
        public int SavedIterations { get; set; }

        /// <summary>
        /// 早停节省的时间比例
        /// </summary>
        public double SavedTimePercentage { get; set; }
    }

    /// <summary>
    /// 精细化扰动信息类
    /// </summary>
    public class AdvancedPerturbationInfo
    {
        /// <summary>
        /// 是否使用了精细化扰动
        /// </summary>
        public bool WasUsed { get; set; }

        /// <summary>
        /// 针对性扰动统计
        /// </summary>
        public TargetedPerturbationStats TargetedStats { get; set; }

        /// <summary>
        /// 相关性扰动统计
        /// </summary>
        public CorrelationPerturbationStats CorrelationStats { get; set; }

        /// <summary>
        /// 扰动策略效果评估
        /// </summary>
        public PerturbationEffectivenessStats EffectivenessStats { get; set; }

        public AdvancedPerturbationInfo()
        {
            TargetedStats = new TargetedPerturbationStats();
            CorrelationStats = new CorrelationPerturbationStats();
            EffectivenessStats = new PerturbationEffectivenessStats();
        }
    }

    /// <summary>
    /// 针对性扰动统计
    /// </summary>
    public class TargetedPerturbationStats
    {
        /// <summary>
        /// 高响应比任务扰动次数
        /// </summary>
        public int HighResponseRatioTaskPerturbations { get; set; }

        /// <summary>
        /// 总扰动次数
        /// </summary>
        public int TotalPerturbations { get; set; }

        /// <summary>
        /// 针对性扰动比例
        /// </summary>
        public double TargetedPerturbationRatio => TotalPerturbations > 0 ? (double)HighResponseRatioTaskPerturbations / TotalPerturbations : 0;

        /// <summary>
        /// 平均响应比降低量
        /// </summary>
        public double AverageResponseRatioReduction { get; set; }
    }

    /// <summary>
    /// 相关性扰动统计
    /// </summary>
    public class CorrelationPerturbationStats
    {
        /// <summary>
        /// 连锁扰动触发次数
        /// </summary>
        public int ChainPerturbationCount { get; set; }

        /// <summary>
        /// 总扰动决策次数
        /// </summary>
        public int TotalPerturbationDecisions { get; set; }

        /// <summary>
        /// 连锁扰动触发率
        /// </summary>
        public double ChainPerturbationRate => TotalPerturbationDecisions > 0 ? (double)ChainPerturbationCount / TotalPerturbationDecisions : 0;

        /// <summary>
        /// 平均连锁长度
        /// </summary>
        public double AverageChainLength { get; set; }
    }

    /// <summary>
    /// 扰动策略效果统计
    /// </summary>
    public class PerturbationEffectivenessStats
    {
        /// <summary>
        /// 随机扰动的改进次数
        /// </summary>
        public int RandomPerturbationImprovements { get; set; }

        /// <summary>
        /// 针对性扰动的改进次数
        /// </summary>
        public int TargetedPerturbationImprovements { get; set; }

        /// <summary>
        /// 相关性扰动的改进次数
        /// </summary>
        public int CorrelationPerturbationImprovements { get; set; }

        /// <summary>
        /// 策略效果比较：针对性扰动相对于随机扰动的改进率提升
        /// </summary>
        public double TargetedEffectivenessRatio { get; set; }

        /// <summary>
        /// 策略效果比较：相关性扰动相对于随机扰动的改进率提升
        /// </summary>
        public double CorrelationEffectivenessRatio { get; set; }
    }

    #endregion

    #region 智能策略适应数据结构

    /// <summary>
    /// 扰动策略枚举
    /// </summary>
    public enum PerturbationStrategy
    {
        Random,
        Targeted,
        Correlation,
        Hybrid
    }

    /// <summary>
    /// 单轮运行结果
    /// </summary>
    public class SingleRunResult
    {
        public Dictionary<int, int> BestSolution { get; set; }
        public double BestCost { get; set; }
        public int TotalIterations { get; set; }
        public int AcceptedMoves { get; set; }
        public int ImprovingMoves { get; set; }
        public bool EarlyStopTriggered { get; set; }
        public double FinalTemperature { get; set; }
    }

    /// <summary>
    /// 策略执行结果
    /// </summary>
    public class StrategyExecutionResult
    {
        public Dictionary<int, int> Solution { get; set; }
        public PerturbationStrategy Strategy { get; set; }
    }

    /// <summary>
    /// 重启机制信息
    /// </summary>
    public class RestartMechanismInfo
    {
        public bool WasUsed => MaxRestarts > 1;
        public int MaxRestarts { get; set; }
        public int ActualRestarts { get; set; }
        public int BestRestartRound { get; set; }
        public Dictionary<int, double> ImprovementsByRestart { get; set; } = new Dictionary<int, double>();
        public List<double> InitialCosts { get; set; } = new List<double>();
        public int TotalIterations { get; set; }
        public int TotalAcceptedMoves { get; set; }
        public int TotalImprovingMoves { get; set; }
    }

    /// <summary>
    /// 策略自适应信息
    /// </summary>
    public class StrategyAdaptationInfo
    {
        public bool WasUsed { get; set; }
        public Dictionary<PerturbationStrategy, int> StrategyUsageCount { get; set; } = new Dictionary<PerturbationStrategy, int>();
        public Dictionary<PerturbationStrategy, int> StrategySuccessCount { get; set; } = new Dictionary<PerturbationStrategy, int>();
        public Dictionary<PerturbationStrategy, double> StrategySuccessRate { get; set; } = new Dictionary<PerturbationStrategy, double>();
        public Dictionary<PerturbationStrategy, double> StrategyReward { get; set; } = new Dictionary<PerturbationStrategy, double>();
        public PerturbationStrategy MostSuccessfulStrategy { get; set; }
        public double AdaptationEffectiveness { get; set; }
    }

    /// <summary>
    /// 内循环自适应信息
    /// </summary>
    public class InnerLoopAdaptationInfo
    {
        public bool WasUsed { get; set; }
        public int MinInnerLoopUsed { get; set; }
        public int MaxInnerLoopUsed { get; set; }
        public double AverageInnerLoopCount { get; set; }
        public List<int> InnerLoopHistory { get; set; } = new List<int>();
        public double TargetAcceptanceRate { get; set; }
        public double ActualAverageAcceptanceRate { get; set; }
    }

    /// <summary>
    /// 冷却自适应信息
    /// </summary>
    public class CoolingAdaptationInfo
    {
        public bool WasUsed { get; set; }
        public int ReheatingCount { get; set; }
        public double TotalReheatingAmount { get; set; }
        public List<double> TemperatureHistory { get; set; } = new List<double>();
        public double AccelerationCount { get; set; }
        public double SlowdownCount { get; set; }
        public double AdaptiveCoolingEffectiveness { get; set; }
    }

    /// <summary>
    /// 策略自适应统计器（多臂老虎机实现）
    /// </summary>
    public class StrategyAdaptationTracker
    {
        private readonly Dictionary<PerturbationStrategy, double> _strategyRewards;
        private readonly Dictionary<PerturbationStrategy, int> _strategyUsageCounts;
        private readonly Dictionary<PerturbationStrategy, int> _strategySuccessCounts;
        private readonly List<StrategyResult> _recentResults;
        private const int MAX_HISTORY_SIZE = 1000;

        public StrategyAdaptationTracker()
        {
            _strategyRewards = new Dictionary<PerturbationStrategy, double>();
            _strategyUsageCounts = new Dictionary<PerturbationStrategy, int>();
            _strategySuccessCounts = new Dictionary<PerturbationStrategy, int>();
            _recentResults = new List<StrategyResult>();

            // 初始化所有策略
            foreach (PerturbationStrategy strategy in Enum.GetValues(typeof(PerturbationStrategy)))
            {
                _strategyRewards[strategy] = 0.0;
                _strategyUsageCounts[strategy] = 0;
                _strategySuccessCounts[strategy] = 0;
            }
        }

        public void Reset()
        {
            foreach (PerturbationStrategy strategy in Enum.GetValues(typeof(PerturbationStrategy)))
            {
                _strategyRewards[strategy] = 0.0;
                _strategyUsageCounts[strategy] = 0;
                _strategySuccessCounts[strategy] = 0;
            }
            _recentResults.Clear();
        }

        public PerturbationStrategy SelectStrategy(Random random)
        {
            // ε-贪心策略选择
            if (random.NextDouble() < 0.1) // ε = 0.1 的探索
            {
                // 随机选择一个策略进行探索
                var strategies = Enum.GetValues(typeof(PerturbationStrategy)).Cast<PerturbationStrategy>().ToArray();
                return strategies[random.Next(strategies.Length)];
            }
            else
            {
                // 选择当前最佳策略
                return GetBestStrategy();
            }
        }

        public void RecordStrategyResult(PerturbationStrategy strategy, bool success, double costDelta)
        {
            _strategyUsageCounts[strategy]++;
            
            if (success)
            {
                _strategySuccessCounts[strategy]++;
                _strategyRewards[strategy] += Math.Abs(costDelta); // 改进量作为奖励
            }

            // 记录最近结果
            _recentResults.Add(new StrategyResult
            {
                Strategy = strategy,
                Success = success,
                CostDelta = costDelta,
                Timestamp = DateTime.Now
            });

            // 限制历史记录大小
            if (_recentResults.Count > MAX_HISTORY_SIZE)
            {
                _recentResults.RemoveAt(0);
            }
        }

        public bool AllStrategiesPerformingPoorly()
        {
            // 如果所有策略的成功率都低于5%，认为表现很差
            foreach (var strategy in _strategyUsageCounts.Keys)
            {
                if (_strategyUsageCounts[strategy] > 20) // 至少尝试20次
                {
                    double successRate = (double)_strategySuccessCounts[strategy] / _strategyUsageCounts[strategy];
                    if (successRate > 0.05) return false;
                }
            }
            return _strategyUsageCounts.Values.Any(count => count > 20);
        }

        public double GetRecentImprovementRate(int windowSize)
        {
            if (_recentResults.Count < windowSize) return 1.0; // 数据不足，假设有改进

            var recentResults = _recentResults.Skip(_recentResults.Count - windowSize);
            var successCount = recentResults.Count(r => r.Success);
            return (double)successCount / windowSize;
        }

        public void UpdateForNextRestart()
        {
            // 在重启间保持策略学习信息，不完全重置
        }

        private PerturbationStrategy GetBestStrategy()
        {
            PerturbationStrategy bestStrategy = PerturbationStrategy.Random;
            double bestScore = double.MinValue;

            foreach (var strategy in _strategyUsageCounts.Keys)
            {
                if (_strategyUsageCounts[strategy] == 0) continue;

                // 计算上置信区间（UCB）得分
                double averageReward = _strategyRewards[strategy] / _strategyUsageCounts[strategy];
                double exploration = Math.Sqrt(2 * Math.Log(_recentResults.Count) / _strategyUsageCounts[strategy]);
                double score = averageReward + exploration;

                if (score > bestScore)
                {
                    bestScore = score;
                    bestStrategy = strategy;
                }
            }

            return bestStrategy;
        }
    }

    /// <summary>
    /// 策略结果记录
    /// </summary>
    public class StrategyResult
    {
        public PerturbationStrategy Strategy { get; set; }
        public bool Success { get; set; }
        public double CostDelta { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 动态内循环控制器
    /// </summary>
    public class DynamicInnerLoopController
    {
        private readonly List<int> _innerLoopHistory;
        private double _targetAcceptanceRate;
        private int _consecutiveNoAcceptance;

        public DynamicInnerLoopController()
        {
            _innerLoopHistory = new List<int>();
            _targetAcceptanceRate = 0.3;
            _consecutiveNoAcceptance = 0;
        }

        public void Reset(double initialTemperature)
        {
            _innerLoopHistory.Clear();
            _consecutiveNoAcceptance = 0;
        }

        public int CalculateInnerLoopCount(double temperature, int recentAcceptedMoves, int iteration)
        {
            // 基于接受率动态调整内循环次数
            const int baseInnerLoop = 50;
            const int minInnerLoop = 10;
            const int maxInnerLoop = 100;

            // 计算最近的接受率
            double recentAcceptanceRate = iteration > 0 ? (double)recentAcceptedMoves / iteration : 0.5;

            int adjustedInnerLoop;
            if (recentAcceptanceRate < _targetAcceptanceRate * 0.5)
            {
                // 接受率过低，增加内循环次数
                adjustedInnerLoop = (int)(baseInnerLoop * 1.5);
            }
            else if (recentAcceptanceRate > _targetAcceptanceRate * 1.5)
            {
                // 接受率过高，减少内循环次数
                adjustedInnerLoop = (int)(baseInnerLoop * 0.7);
            }
            else
            {
                adjustedInnerLoop = baseInnerLoop;
            }

            // 限制在合理范围内
            adjustedInnerLoop = Math.Max(minInnerLoop, Math.Min(maxInnerLoop, adjustedInnerLoop));
            _innerLoopHistory.Add(adjustedInnerLoop);

            return adjustedInnerLoop;
        }

        public void PrepareForRestart()
        {
            _consecutiveNoAcceptance = 0;
        }

        public InnerLoopAdaptationInfo GetStatistics()
        {
            return new InnerLoopAdaptationInfo
            {
                WasUsed = true,
                MinInnerLoopUsed = _innerLoopHistory.Count > 0 ? _innerLoopHistory.Min() : 50,
                MaxInnerLoopUsed = _innerLoopHistory.Count > 0 ? _innerLoopHistory.Max() : 50,
                AverageInnerLoopCount = _innerLoopHistory.Count > 0 ? _innerLoopHistory.Average() : 50,
                InnerLoopHistory = new List<int>(_innerLoopHistory),
                TargetAcceptanceRate = _targetAcceptanceRate
            };
        }
    }

    /// <summary>
    /// 自适应冷却控制器
    /// </summary>
    public class AdaptiveCoolingController
    {
        private readonly List<double> _temperatureHistory;
        private int _reheatingCount;
        private double _totalReheatingAmount;
        private int _consecutiveNoImprovement;
        private double _accelerationCount;
        private double _slowdownCount;

        public AdaptiveCoolingController()
        {
            _temperatureHistory = new List<double>();
            _reheatingCount = 0;
            _totalReheatingAmount = 0;
            _consecutiveNoImprovement = 0;
            _accelerationCount = 0;
            _slowdownCount = 0;
        }

        public void Reset(double initialTemperature, double finalTemperature)
        {
            _temperatureHistory.Clear();
            _temperatureHistory.Add(initialTemperature);
            _consecutiveNoImprovement = 0;
        }

        public double UpdateTemperature(double currentTemperature, int recentAcceptedMoves, 
            int recentImprovingMoves, int iteration)
        {
            const double baseCoolingRate = 0.995;
            double newTemperature = currentTemperature;

            // 判断是否需要调整冷却速度
            if (recentImprovingMoves > 0)
            {
                // 有改进，可以稍微加快冷却
                newTemperature = currentTemperature * (baseCoolingRate * 1.02);
                _accelerationCount++;
                _consecutiveNoImprovement = 0;
            }
            else
            {
                _consecutiveNoImprovement++;
                
                if (_consecutiveNoImprovement > 100) // 长时间无改进
                {
                    // 重加温
                    newTemperature = currentTemperature * 1.2;
                    _reheatingCount++;
                    _totalReheatingAmount += newTemperature - currentTemperature;
                    _consecutiveNoImprovement = 0;
                }
                else
                {
                    // 稍微减慢冷却
                    newTemperature = currentTemperature * (baseCoolingRate * 0.98);
                    _slowdownCount++;
                }
            }

            _temperatureHistory.Add(newTemperature);
            return newTemperature;
        }

        public void PrepareForRestart()
        {
            _consecutiveNoImprovement = 0;
        }

        public CoolingAdaptationInfo GetStatistics()
        {
            return new CoolingAdaptationInfo
            {
                WasUsed = true,
                ReheatingCount = _reheatingCount,
                TotalReheatingAmount = _totalReheatingAmount,
                TemperatureHistory = new List<double>(_temperatureHistory),
                AccelerationCount = _accelerationCount,
                SlowdownCount = _slowdownCount,
                AdaptiveCoolingEffectiveness = CalculateCoolingEffectiveness()
            };
        }

        private double CalculateCoolingEffectiveness()
        {
            if (_temperatureHistory.Count < 2) return 1.0;
            
            // 计算温度下降的平滑度（方差越小越平滑）
            var differences = new List<double>();
            for (int i = 1; i < _temperatureHistory.Count; i++)
            {
                differences.Add(_temperatureHistory[i-1] - _temperatureHistory[i]);
            }
            
            double variance = differences.Count > 1 ? differences.Select(d => Math.Pow(d - differences.Average(), 2)).Average() : 0;
            return 1.0 / (1.0 + variance); // 方差越小，效果越好
        }
    }

    #endregion
} 