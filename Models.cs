using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace CalculateOffset
{
    /// <summary>
    /// 计算类型枚举
    /// </summary>
    public enum CalculationType
    {
        Task = 0,    // 任务
        Message = 1  // 报文
    }

    /// <summary>
    /// 主界面配置参数
    /// </summary>
    public class MainConfig
    {
        // 时基(us)，界面显示ms，内部存储us，用于设定最小的任务时间片，默认1ms=1000us
        public int TimeBaseUs { get; set; } = 1000;

        // 最大Offset值(us)，界面显示ms，内部存储us，限定有效的offset范围，默认100ms=100000us
        public int MaxOffsetUs { get; set; } = 100000;

        // 计算类型，默认为任务
        public CalculationType CalculationType { get; set; } = CalculationType.Task;

        // 上次保存的文件路径
        public string LastSavePath { get; set; } = "";

        // 上次打开的文件路径
        public string LastOpenPath { get; set; } = "";

        // 列宽信息
        public Dictionary<string, int> ColumnWidths { get; set; } = new Dictionary<string, int>();

        // 抖动时间(us)，界面显示ms，内部存储us，用于模拟任务切换的OS开销，默认0.010ms=10us
        public int JitterTimeUs { get; set; } = 10;
    }

    /// <summary>
    /// 任务数据结构
    /// </summary>
    public class TaskItem : INotifyPropertyChanged
    {
        private string _taskName = "";
        private int? _periodUs = 1000; // 周期(us)，默认1ms=1000us
        private int? _offsetUs = 0; // Offset值(us)
        //        private int? _avgExecutionTimeUs = 10; // 平均执行时间(us)，默认10us
        private int? _maxExecutionTimeUs = 10; // 最大执行时间(us)，默认10us
        private bool _enabled = true; // 是否使能，默认使能
        private bool _manualInput = false;
        private int? _priority = 0;
        private int _taskIndex = 0;
        private string _prerequisiteTasks = "";
        private bool _nonPreemptive = false;
        private int _jitterTimeUs = 10; // 抖动时间(us)，默认0.010ms=10us
        private int _maxOffsetUs = 0; // 该任务的最大offset限制(us)，取周期和用户设定的较小值

        // 任务名称
        public string TaskName
        {
            get => _taskName;
            set
            {
                if (_taskName != value)
                {
                    _taskName = value;
                    OnPropertyChanged(nameof(TaskName));
                }
            }
        }

        // 周期(us)
        public int? PeriodUs
        {
            get => _periodUs;
            set
            {
                if (_periodUs != value)
                {
                    _periodUs = value;
                    OnPropertyChanged(nameof(PeriodUs));
                }
            }
        }

        // Offset值(us)
        public int? OffsetUs
        {
            get => _offsetUs;
            set
            {
                if (_offsetUs != value)
                {
                    _offsetUs = value;
                    OnPropertyChanged(nameof(OffsetUs));
                }
            }
        }

		/*
        // 平均执行时间(us)
        public int? AverageExecutionTimeUs
        {
            get => _avgExecutionTimeUs;
            set
            {
                if (_avgExecutionTimeUs != value)
                {
                    _avgExecutionTimeUs = value;
                    OnPropertyChanged(nameof(AverageExecutionTimeUs));
                }
            }
        }
		*/

        // 最大执行时间(us)
        public int? ExecutionTimeUs
        {
            get => _maxExecutionTimeUs;
            set
            {
                if (_maxExecutionTimeUs != value)
                {
                    _maxExecutionTimeUs = value;
                    OnPropertyChanged(nameof(ExecutionTimeUs));
                }
            }
        }

        // 是否使能
        public bool Enabled
        {
            get => _enabled;
            set
            {
                if (_enabled != value)
                {
                    _enabled = value;
                    OnPropertyChanged(nameof(Enabled));
                }
            }
        }

        // 是否手动输入Offset
        public bool ManualInput
        {
            get => _manualInput;
            set
            {
                if (_manualInput != value)
                {
                    _manualInput = value;
                    OnPropertyChanged(nameof(ManualInput));
                }
            }
        }

        // 优先级
        public int? Priority
        {
            get => _priority;
            set
            {
                if (_priority != value)
                {
                    _priority = value;
                    OnPropertyChanged(nameof(Priority));
                }
            }
        }

        // 任务序号，用于在排序后与原始任务匹配
        public int TaskIndex
        {
            get => _taskIndex;
            set
            {
                if (_taskIndex != value)
                {
                    _taskIndex = value;
                    OnPropertyChanged(nameof(TaskIndex));
                }
            }
        }

        // 前置任务，使用逗号分隔多个任务名
        public string PrerequisiteTasks
        {
            get => _prerequisiteTasks;
            set
            {
                if (_prerequisiteTasks != value)
                {
                    _prerequisiteTasks = value ?? "";
                    OnPropertyChanged(nameof(PrerequisiteTasks));
                }
            }
        }

        // 是否为不可抢占任务
        public bool NonPreemptive
        {
            get => _nonPreemptive;
            set
            {
                if (_nonPreemptive != value)
                {
                    _nonPreemptive = value;
                    OnPropertyChanged(nameof(NonPreemptive));
                }
            }
        }

        // 抖动时间(us)，用于模拟任务切换的OS开销
        public int JitterTimeUs
        {
            get => _jitterTimeUs;
            set
            {
                if (_jitterTimeUs != value)
                {
                    _jitterTimeUs = value;
                    OnPropertyChanged(nameof(JitterTimeUs));
                }
            }
        }

        // 该任务的最大offset限制(us)，取周期和用户设定的较小值
        public int MaxOffsetUs
        {
            get => _maxOffsetUs;
            set
            {
                if (_maxOffsetUs != value)
                {
                    _maxOffsetUs = value;
                    OnPropertyChanged(nameof(MaxOffsetUs));
                }
            }
        }

        // 属性变更事件
        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        
        // 默认构造函数，确保所有属性都有合理的初始值
        public TaskItem()
        {
            _taskName = "";
            _periodUs = 1000; // 默认1ms=1000us
            _offsetUs = 0;
            //_avgExecutionTimeUs = 10; // 默认10us
            _maxExecutionTimeUs = 10; // 默认10us
            _enabled = true; // 默认使能
            _manualInput = false;
            _priority = 0;
            _prerequisiteTasks = "";
            _nonPreemptive = false;
            _jitterTimeUs = 10; // 默认0.010ms=10us
            _maxOffsetUs = 0; // 默认0，在计算时统一设置
        }
        
        /// <summary>
        /// 重写Equals方法，使用TaskIndex作为唯一标识
        /// </summary>
        /// <param name="obj">比较对象</param>
        /// <returns>是否相等</returns>
        public override bool Equals(object obj)
        {
            if (!(obj is TaskItem other))
                return false;
                
            return TaskIndex == other.TaskIndex;
        }
        
        /// <summary>
        /// 重写GetHashCode方法，使用TaskIndex的哈希码
        /// </summary>
        /// <returns>哈希码</returns>
        public override int GetHashCode()
        {
            return TaskIndex.GetHashCode();
        }
    }

    /// <summary>
    /// 完整的应用程序配置
    /// </summary>
    public class AppConfig
    {
        // 主界面配置
        public MainConfig MainConfig { get; set; } = new MainConfig();

        // 任务列表
        public List<TaskItem> Tasks { get; set; } = new List<TaskItem>();
    }
} 